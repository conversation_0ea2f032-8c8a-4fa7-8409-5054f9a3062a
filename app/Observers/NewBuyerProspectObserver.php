<?php

namespace App\Observers;

use App\Jobs\Prospects\VerifyNewBuyerProspectJob;
use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Support\Str;

class NewBuyerProspectObserver
{
    /**
     * Handle the NewBuyerProspect "creating" event.
     */
    public function creating(NewBuyerProspect $newBuyerProspect): void
    {
        if (!$newBuyerProspect->reference) {
            $newBuyerProspect->reference = Str::uuid();
        }
    }

    /**
     * Handle the NewBuyerProspect "created" event.
     */
    public function created(NewBuyerProspect $newBuyerProspect): void
    {
        VerifyNewBuyerProspectJob::dispatch($newBuyerProspect->id);
    }

    /**
     * Handle the NewBuyerProspect "updated" event.
     */
    public function updated(NewBuyerProspect $newBuyerProspect): void
    {
        //
    }

    /**
     * Handle the NewBuyerProspect "deleted" event.
     */
    public function deleted(NewBuyerProspect $newBuyerProspect): void
    {
        //
    }

    /**
     * Handle the NewBuyerProspect "restored" event.
     */
    public function restored(NewBuyerProspect $newBuyerProspect): void
    {
        //
    }

    /**
     * Handle the NewBuyerProspect "force deleted" event.
     */
    public function forceDeleted(NewBuyerProspect $newBuyerProspect): void
    {
        //
    }
}
