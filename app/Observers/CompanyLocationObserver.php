<?php

namespace App\Observers;

use App\Jobs\UpdateNonPurchasingLocationsForCompanyLocationJob;
use App\Models\Odin\CompanyLocation;

class CompanyLocationObserver
{
    /**
     * @param CompanyLocation $location
     * @return void
     */
    public function created(CompanyLocation $location): void
    {
        self::handleCompanyLocationZipCodeChange($location);
    }

    /**
     * @param CompanyLocation $location
     * @return void
     */
    public function deleted(CompanyLocation $location): void
    {
        $location?->nonPurchasingLocations()->delete();
    }

    /**
     * @param CompanyLocation $location
     * @return void
     */
    public static function handleCompanyLocationZipCodeChange(CompanyLocation $location): void
    {
        if ($location->address?->zip_code)
            UpdateNonPurchasingLocationsForCompanyLocationJob::dispatch($location->id);
    }
}