<?php

namespace App\Observers;


use App\Models\Campaigns\Modules\Budget\Budget;
use App\Repositories\Campaigns\Modules\Budget\BudgetRepository;

class BudgetObserver
{
    /**
     * Handle the Budget "created" event.
     */
    public function created(Budget $budget): void
    {
        $budget->last_modified_utc = app(BudgetRepository::class)->getCampaignUTCOffset($budget->budgetContainer->campaign);
        $budget->save();
    }

    /**
     * Handle the Budget "updated" event.
     */
    public function updated(Budget $budget): void
    {
        //
    }

    /**
     * Handle the Budget "deleted" event.
     */
    public function deleted(Budget $budget): void
    {
        //
    }

    /**
     * Handle the Budget "restored" event.
     */
    public function restored(Budget $budget): void
    {
        //
    }

    /**
     * Handle the Budget "force deleted" event.
     */
    public function forceDeleted(Budget $budget): void
    {
        //
    }
}
