<?php

namespace App\ActivityLog\Pipes\CompanyCampaign;

use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\Campaigns\Modules\Budget\Budget;
use Closure;
use Spatie\Activitylog\EventLogBag;

class BudgetPipe extends BasePipe
{
    public function handle(EventLogBag $event, Closure $next): EventLogBag
    {
        $this->transformEnumChanges(
            $event,
            Budget::FIELD_TYPE,
            BudgetType::class,
            fn(BudgetType $enum) => $enum->getDisplayName()
        );

        $this->transformEnumChanges(
            $event,
            Budget::FIELD_PRODUCT_CONFIGURATION,
            BudgetProductConfigurationEnum::class,
            fn(BudgetProductConfigurationEnum $enum) => $enum->getDisplayName()
        );

        return $next($event);
    }
}
