<?php

namespace App\ActivityLog\Pipes\CompanyCampaign;

use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\CampaignType;
use App\Models\Campaigns\CompanyCampaign;
use Closure;
use Spatie\Activitylog\EventLogBag;

class CampaignPipe extends BasePipe
{
    public function handle(EventLogBag $event, Closure $next): EventLogBag
    {
        $this->transformEnumChanges(
            $event,
            CompanyCampaign::FIELD_STATUS,
            CampaignStatus::class,
            fn(CampaignStatus $enum) => $enum->getDisplayName()
        );

        $this->transformEnumChanges(
            $event,
            CompanyCampaign::FIELD_TYPE,
            CampaignType::class,
            fn(CampaignType $enum) => $enum->label()
        );

        return $next($event);
    }
}
