<?php

namespace App\ActivityLog\Pipes\CompanyCampaign;

use Closure;
use Exception;
use Illuminate\Support\Arr;
use ReflectionEnum;
use Spatie\Activitylog\Contracts\LoggablePipe;
use Spatie\Activitylog\EventLogBag;

class BasePipe implements LoggablePipe
{
    public function handle(EventLogBag $event, Closure $next): EventLogBag
    {
        return $next($event);
    }

    /**
     * Generic transformer for enum-backed campaign fields.
     *
     * @param EventLogBag $event
     * @param string $field
     * @param class-string $enumClass
     * @param Closure $formatter
     */
    protected function transformEnumChanges(EventLogBag $event, string $field, string $enumClass, Closure $formatter): void
    {
        $oldValue = Arr::get($event->changes, 'old.' . $field);
        $newValue = Arr::get($event->changes, 'attributes.' . $field);

        try {
            $backingType = (new ReflectionEnum($enumClass))->getBackingType()?->getName();
        } catch (Exception $exception) {
            return;
        }

        if (!$backingType) {
            return;
        }

        if ($backingType === 'int') {
            $oldValue = is_numeric($oldValue) ? (int) $oldValue : null;
            $newValue = is_numeric($newValue) ? (int) $newValue : null;
        } elseif ($backingType === 'string') {
            $oldValue = (string) $oldValue;
            $newValue = (string) $newValue;
        } else {
            return;
        }

        $oldEnum = $oldValue !== null ? $enumClass::tryFrom($oldValue) : null;
        $newEnum = $newValue !== null ? $enumClass::tryFrom($newValue) : null;

        if ($oldEnum) {
            Arr::set($event->changes, 'old.' . $field, $formatter($oldEnum));
        }

        if ($newEnum) {
            Arr::set($event->changes, 'attributes.' . $field, $formatter($newEnum));
        }
    }
}
