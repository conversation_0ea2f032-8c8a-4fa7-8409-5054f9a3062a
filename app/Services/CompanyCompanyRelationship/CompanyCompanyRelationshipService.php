<?php

namespace App\Services\CompanyCompanyRelationship;

use App\Enums\ActivityLog\ActivityLogName;
use App\Models\CompanyCompanyRelationship;
use App\Models\Odin\Company;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Repositories\CompanyCompanyRelationship\CompanyCompanyRelationshipRepository;
use Illuminate\Database\Eloquent\Builder;

class CompanyCompanyRelationshipService
{
    public function __construct(
        protected CompanyCompanyRelationshipRepository $companyCompanyRelationshipRepository,
        protected ActivityLogRepository $activityLogRepository,
    )
    {
    }

    /**
     * @param int|null $companyId
     * @param int|null $targetCompanyId
     * @param string|null $relationship
     * @param bool|null $active
     * @return Builder
     */
    public function list(
        ?int $companyId = null,
        ?int $targetCompanyId = null,
        ?string $relationship = null,
        ?bool $active = null,
    ): Builder
    {
        return $this->companyCompanyRelationshipRepository->list(
            companyId: $companyId,
            targetCompanyId: $targetCompanyId,
            relationship: $relationship,
            active: $active,
        );
    }
}
