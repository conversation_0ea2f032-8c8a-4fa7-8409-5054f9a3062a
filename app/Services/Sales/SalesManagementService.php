<?php

namespace App\Services\Sales;

use App\Enums\RoleType;
use App\Enums\RoundRobinType;
use App\Models\RoundRobin;
use App\Models\RoundRobinParticipant;
use App\Models\User;
use App\Repositories\RoundRobins\RoundRobinRepository;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class SalesManagementService
{
    /**
     * @param RoleType $role
     *
     * @return Collection
     */
    public function getAccountAssignmentParticipants(RoleType $role): Collection
    {
        $roundRobin = $this->getAccountAssignmentRoundRobin($role->value);
        $selectColumns = [
            User::TABLE .'.'. User::FIELD_ID,
            User::FIELD_NAME,
        ];

        $participants = RoundRobinParticipant::query()
            ->where(RoundRobinParticipant::FIELD_ROUND_ROBIN_ID, $roundRobin->id)
            ->get()->keyBy(RoundRobinParticipant::FIELD_USER_ID);

        return User::role($role->value)
            ->select($selectColumns)
            ->get()
            ->map(function (User $user) use ($participants) {
                $user->{RoundRobinParticipant::FIELD_ROUND_ROBIN_ID} = Arr::get($participants, $user->id)?->{RoundRobinParticipant::FIELD_ROUND_ROBIN_ID};
                return $user;
            })
            ->sortBy(User::FIELD_NAME);
    }

    /**
     * @param array $addUserIds
     * @param array $removeUserIds
     * @param string $role
     *
     * @return bool
     * @throws Throwable
     */
    public function updateAccountAssignmentParticipants(array $addUserIds, array $removeUserIds, string $role): bool
    {
        try {
            DB::beginTransaction();
            /** @var RoundRobinRepository $repository */
            $repository = app(RoundRobinRepository::class)->setFilter($role);

            if ($addUserIds)
                $repository->addParticipants(RoundRobinType::ACCOUNT_ASSIGNMENT, $addUserIds);
            if ($removeUserIds)
                $repository->removeParticipants(RoundRobinType::ACCOUNT_ASSIGNMENT, $removeUserIds);

            DB::commit();
            return true;
        }
        catch (Throwable $e) {
            logger()->error("Error creating RoundRobinParticipants: " . $e->getMessage());
            DB::rollBack();

            return false;
        }
    }

    /**
     * @param string $role
     *
     * @return RoundRobin
     */
    protected function getAccountAssignmentRoundRobin(string $role): RoundRobin
    {
        $roundRobinRepository = app(RoundRobinRepository::class)->setFilter($role);

        return $roundRobinRepository->getRoundRobin(RoundRobinType::ACCOUNT_ASSIGNMENT);
    }
}
