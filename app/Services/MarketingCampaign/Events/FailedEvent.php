<?php

namespace App\Services\MarketingCampaign\Events;

use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Services\MarketingCampaign\MarketingLogService;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class FailedEvent extends MarketingCampaignConsumerMarketingEvent
{
    public function __construct(
        int $marketingCampaignConsumerId,
        protected string $failureType,
        protected ?string $failureReason = null,
        protected ?array $otherData = null,
    )
    {
        parent::__construct($marketingCampaignConsumerId);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    function trigger(): void
    {
        $service = app(MarketingCampaignConsumerRepository::class);

        $mcc = $service->get(id: $this->marketingCampaignConsumerId);

        $service->updateMarketingCampaignConsumer(
            campaignConsumer: $mcc,
            status: MarketingCampaignConsumerStatus::ERROR
        );

        MarketingLogService::log(
            message: 'Failed to send email',
            namespace: MarketingLogType::WEBHOOK,
            level: LogLevel::ERROR,
            context: [
                'type' => $this->failureType,
                'reason' => $this->failureReason,
                'other' => $this->otherData,
            ],
            relations: [$mcc, $mcc->marketingCampaign]
        );
    }
}
