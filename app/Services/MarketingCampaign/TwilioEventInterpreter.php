<?php

namespace App\Services\MarketingCampaign;

use App\Contracts\Marketing\MarketingEventInterpreterContract;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Services\MarketingCampaign\Events\DeliveredEvent;
use App\Services\MarketingCampaign\Events\OpenedEvent;
use App\Services\MarketingCampaign\Events\UnsubscribedEvent;
use App\Services\MarketingCampaign\Events\MarketingEvent;
use App\Services\MarketingCampaign\Events\ResubscribedEvent;
use Illuminate\Http\Request;

class TwilioEventInterpreter implements MarketingEventInterpreterContract
{
    const string MCC_ID = 'mcc_id';
    const string OPT_OUT_TYPE = 'OptOutType';
    const string FROM_PHONE_NUMBER = 'From';
    const string MESSAGE_STATUS = "MessageStatus";
    const string START_OPT_TYPE = 'START';
    const string STOP_OPT_TYPE = 'STOP';
    const string STATUS_SENT = 'sent';
    const string STATUS_QUEUED = 'queued';
    const string STATUS_DELIVERED = 'delivered';
    const string STATUS_READ = 'read';
    public function interpret(Request $request): ?MarketingEvent
    {
        $marketingCampaignConsumerId = $request->input(self::MCC_ID);
        $optOutType = $request->input(self::OPT_OUT_TYPE);
        $event = null;

        if ($optOutType) {
            if (empty($marketingCampaignConsumerId)) {
                $marketingCampaignConsumerService = app(MarketingCampaignConsumerService::class);

                $marketingCampaignConsumer = $marketingCampaignConsumerService->findMarketingCampaignConsumerByPhone(
                    phone: $request->input(self::FROM_PHONE_NUMBER)
                );

                $marketingCampaignConsumerId = $marketingCampaignConsumer?->id;
            }

            if ($marketingCampaignConsumerId) {
                $event = match ($optOutType) {
                    self::STOP_OPT_TYPE => new UnsubscribedEvent($marketingCampaignConsumerId),
                    self::START_OPT_TYPE => new ResubscribedEvent($marketingCampaignConsumerId),
                    default => null,
                };
            }
        } else if ($marketingCampaignConsumerId) {
            $status = $request->input(self::MESSAGE_STATUS);

            $event = match ($status) {
                self::STATUS_DELIVERED => new DeliveredEvent($marketingCampaignConsumerId),
                self::STATUS_READ => new  OpenedEvent($marketingCampaignConsumerId),
                default => null,
            };
        }

        return $event;
    }
}
