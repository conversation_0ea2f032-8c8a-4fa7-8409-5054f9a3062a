<?php

namespace App\Services\Odin\ConsumerProductVerification\IpQuality;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\VerificationServiceTypes;
use App\Models\Odin\ConsumerProduct;
use App\Models\Prospects\NewBuyerProspect;
use App\Services\Odin\ConsumerProductVerification\ConsumerProductVerificationServiceInterface;
use App\Services\Odin\ConsumerProductVerification\IPQualityScoreResult;
use GuzzleHttp\Client;
use GuzzleHttp\Promise;
use Illuminate\Support\Facades\Cache;

class IpQualityScoreService implements ConsumerProductVerificationServiceInterface
{
    const EMAIL_DELIVERABILITY               = 'deliverability';
    const EMAIL_FRAUD_SCORE                  = 'fraud_score';
    const EMAIL_FREQUENT_COMPLAINER          = 'frequent_complainer';
    const EMAIL_USER_ACTIVITY                = 'user_activity';
    const EMAIL_ASSOCIATED_NAMES             = 'associated_names';
    const EMAIL_ASSOCIATED_NUMBERS           = 'associated_phone_numbers';
    const EMAIL_FIRST_SEEN                   = 'first_seen';
    const PHONE_VALID                        = 'valid';
    const PHONE_ACTIVE_STATUS                = 'active_status';
    const PHONE_CITY                         = 'city';
    const PHONE_ZIP_CODE                     = 'zip_code';
    const PHONE_REGION                       = 'region';
    const PHONE_PREPAID                      = 'prepaid';
    const PHONE_NAME                         = 'name';
    const PHONE_LINE_TYPE                    = 'line_type';
    const PHONE_USER_ACTIVITY                = 'user_activity';
    const PHONE_ASSOCIATED_EMAILS            = 'associated_email_addresses';
    const PHONE_IDENTITY_DATA                = 'identity_data';
    const TRANSACTION_DETAILS                = 'transaction_details';
    const TRANSACTION_RISK_SCORE             = 'risk_score';
    const TRANSACTION_RISK_FACTOR            = 'risk_factors';
    const TRANSACTION_EMAIL_NAME_IDENTITY    = 'email_name_identity_match';
    const TRANSACTION_NAME_ADDRESS_IDENTITY  = 'name_address_identity_match';
    const TRANSACTION_PHONE_NAME_IDENTITY    = 'phone_name_identity_match';
    const TRANSACTION_PHONE_ADDRESS_IDENTITY = 'phone_address_identity_match';
    const TRANSACTION_PHONE_EMAIL_IDENTITY   = 'phone_email_identity_match';
    const TRANSACTION_FRAUD_SCORE            = 'fraud_score';
    const TRANSACTION_RECENT_ABUSE           = 'recent_abuse';

    const IP_QUALITY_EMAIL_DATA_KEYS = [
        self::EMAIL_DELIVERABILITY,
        self::EMAIL_FRAUD_SCORE,
        self::EMAIL_FREQUENT_COMPLAINER,
        self::EMAIL_USER_ACTIVITY,
        self::EMAIL_ASSOCIATED_NAMES,
        self::EMAIL_ASSOCIATED_NUMBERS,
        self::EMAIL_FIRST_SEEN
    ];

    const IP_QUALITY_PHONE_DATA_KEYS = [
        self::PHONE_VALID,
        self::PHONE_ACTIVE_STATUS,
        self::PHONE_CITY,
        self::PHONE_ZIP_CODE,
        self::PHONE_REGION,
        self::PHONE_PREPAID,
        self::PHONE_NAME,
        self::PHONE_LINE_TYPE,
        self::PHONE_USER_ACTIVITY,
        self::PHONE_ASSOCIATED_EMAILS,
        self::PHONE_IDENTITY_DATA,
    ];

    const int RESULTS_CACHE_SECONDS = 3600 * 24; // 1 day

    /**
     * @inheritDoc
     */
    public function verifyConsumerProduct(ConsumerProduct $consumerProduct): IPQualityScoreResult
    {
        return Cache::remember('consumer-product-ip-quality-score-' . $consumerProduct->id, self::RESULTS_CACHE_SECONDS, function () use ($consumerProduct) {

            $guzzleClient = new Client(['base_uri' => config('services.ip_quality_score.base_url')]);
            $apiKey       = config('services.ip_quality_score.api_key');

            $phone = $consumerProduct->consumer->phone;
            // Sanitize the numbers since the IPQuality service accepts it within the route
            $phone = preg_replace('/[^0-9]/', '', $phone);

            $responses = Promise\Utils::unwrap(["phone" => $guzzleClient->getAsync("phone/$apiKey/$phone?country[]=US")]);
            $phoneData = json_decode($responses["phone"]->getBody()->getContents(), true);

            $result              = new IPQualityScoreResult();
            $result->driver      = VerificationServiceTypes::IP_QUALITY_SCORE->value;
            $result->email       = [];
            $result->phone       = $phoneData ?? [];
            $result->transaction = [];

            return $result;
        });
    }

    public function verifyNewBuyerProspect(NewBuyerProspect $newBuyerProspect): IPQualityScoreResult
    {
        return Cache::remember('new-buyer-prospect-ip-quality-score-' . $newBuyerProspect->id, self::RESULTS_CACHE_SECONDS, function () use ($newBuyerProspect) {
            $guzzleClient = new Client(['base_uri' => config('services.ip_quality_score.base_url')]);
            $apiKey       = config('services.ip_quality_score.api_key');

            $phone = preg_replace('/[^0-9]/', '', $newBuyerProspect->company_phone);
            $responses = Promise\Utils::unwrap(["phone" => $guzzleClient->getAsync("phone/$apiKey/$phone?country[]=US")]);
            $phoneData = json_decode($responses["phone"]->getBody()->getContents(), true);
            $result              = new IPQualityScoreResult();
            $result->driver      = VerificationServiceTypes::IP_QUALITY_SCORE->value;
            $result->email       = [];
            $result->phone       = $phoneData ?? [];
            $result->transaction = [];
            return $result;
        });
    }
}
