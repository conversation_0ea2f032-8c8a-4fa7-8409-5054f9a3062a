<?php

namespace App\Services\Advertising\Campaigns;

use App\Contracts\Services\Advertising\AdvertisingLocationsServiceContract;
use App\Contracts\Services\Advertising\AdvertisingServiceContract;
use App\DataModels\Advertising\AccountCampaigns;
use App\DataModels\Advertising\AdCampaignsPaginatorDataModel;
use App\DataModels\Advertising\Campaign;
use App\DataModels\Advertising\CampaignLocations;
use App\DataModels\Advertising\DashboardTargetLocation;
use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingCostMetric;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\AdvertisingCampaign;
use App\Models\AdvertisingCampaignHistoryLog;
use App\Models\GoogleAdsGeoTarget;
use App\Models\Legacy\Location;
use App\Models\LockedAdvertisingCampaignLocation;
use App\Models\Odin\Industry;
use App\Models\TieredAdvertisingAccount;
use App\Models\TieredAdvertisingCampaign;
use App\Models\TieredAdvertisingCounty;
use App\Models\TieredAdvertisingLog;
use App\Services\Advertising\AdvertisingCampaignService;
use App\Services\Advertising\AdvertisingLoggingService;
use App\Services\Advertising\Authentication\AdvertisingAuthServiceFactory;
use App\Services\Advertising\Locations\AdvertisingLocationsServiceFactory;
use App\Services\Advertising\Locations\GoogleAdsGeoTargetService;
use App\Services\Advertising\TieredAdvertisingService;
use App\Services\DatabaseHelperService;
use App\Transformers\Advertising\AdvertisingCampaignsTransformer;
use Exception;
use Google\Ads\GoogleAds\Lib\V19\GoogleAdsClient;
use Google\Ads\GoogleAds\Util\FieldMasks;
use Google\Ads\GoogleAds\Util\V19\ResourceNames;
use Google\Ads\GoogleAds\V19\Common\LocationInfo;
use Google\Ads\GoogleAds\V19\Common\MaximizeConversions;
use Google\Ads\GoogleAds\V19\Resources\BatchJob;
use Google\Ads\GoogleAds\V19\Resources\CampaignCriterion;
use Google\Ads\GoogleAds\V19\Resources\CustomerClient;
use Google\Ads\GoogleAds\V19\Services\AddBatchJobOperationsRequest;
use Google\Ads\GoogleAds\V19\Services\BatchJobOperation;
use Google\Ads\GoogleAds\V19\Services\BatchJobResult;
use Google\Ads\GoogleAds\V19\Services\Client\BatchJobServiceClient;
use Google\Ads\GoogleAds\V19\Services\Client\CampaignBudgetServiceClient;
use Google\Ads\GoogleAds\V19\Services\CampaignCriterionOperation;
use Google\Ads\GoogleAds\V19\Services\Client\CampaignCriterionServiceClient;
use Google\Ads\GoogleAds\V19\Services\Client\CampaignServiceClient;
use Google\Ads\GoogleAds\V19\Services\GoogleAdsRow;
use Google\Ads\GoogleAds\V19\Services\Client\GoogleAdsServiceClient;
use Google\Ads\GoogleAds\V19\Services\ListBatchJobResultsRequest;
use Google\Ads\GoogleAds\V19\Services\MutateBatchJobRequest;
use Google\Ads\GoogleAds\V19\Services\MutateCampaignsRequest;
use Google\Ads\GoogleAds\V19\Services\MutateOperation;
use Google\Ads\GoogleAds\V19\Services\RunBatchJobRequest;
use Google\Ads\GoogleAds\V19\Services\SearchGoogleAdsRequest;
use Google\Ads\GoogleAds\V19\Services\SearchGoogleAdsStreamRequest;
use Google\ApiCore\ApiException;
use Google\ApiCore\ValidationException;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use JetBrains\PhpStorm\ArrayShape;
use Google\Ads\GoogleAds\V19\Resources\Campaign as GoogleCampaign;
use Google\Ads\GoogleAds\V19\Services\CampaignOperation;
use Throwable;

class GoogleAdsService implements AdvertisingServiceContract
{
    const RESULTS_LIMIT = 500;
    const REQUEST_MUTATE_OPERATIONS_LIMIT = 10000; //The max amount of mutate requests Google Ads API allows us to send per request
    const ADD_BATCH_JOB_OPS_LIMIT = 1000; //The recommended max amount of batch operations to add per add batch operations request
    const BATCH_RESULTS_PAGE_SIZE = 25;
    const DOLLAR_TO_MICROS = 1000000; // Multiply by 1,000,000 to convert to micros

    const FUNC_RETURN_CAMPAIGNS = 'campaigns';
    const FUNC_RETURN_TOTAL_NUMBER_OF_RESULTS = 'total_number_of_results';
    const FUNC_RETURN_CAMPAIGN_BUDGET_IDS = 'campaign_budget_ids';

    const FUNC_RETURN_OPERATIONS = 'operations';
    const FUNC_RETURN_OPERATIONS_INFO = 'operations_info';
    const FUNC_RETURN_SUCCESSFUL_CAMPAIGN_IDS = 'successful_campaign_ids';

    const CAMPAIGN_STATUS_ENABLED = 'enabled';
    const CAMPAIGN_STATUS_PAUSED = 'paused';
    const CAMPAIGN_STATUS_REMOVED = 'removed';

    const FUNC_ACCOUNT_CAMPAIGN_LOCATIONS = 'acct_campaign_locations';
    const FUNC_CRITERIA_ID_TO_ASSIGNMENT_DATA = 'criteria_id_to_assignment_data';

    private GoogleAdsClient $googleAdsClient;
    private GoogleAdsServiceClient $googleAdsServiceClient;
    private GoogleAdsGeoTargetService $googleAdsGeoTargetService;

    /**
     * @param AdvertisingCampaignService $advertisingCampaignService
     * @param TieredAdvertisingService $tieredAdvertisingService
     * @throws Exception
     */
    public function __construct(
        private AdvertisingCampaignService $advertisingCampaignService,
        private TieredAdvertisingService $tieredAdvertisingService,
    )
    {
        $this->googleAdsClient = AdvertisingAuthServiceFactory::make(AdvertisingPlatform::GOOGLE->value)->getClient();
        $this->googleAdsServiceClient = $this->googleAdsClient->getGoogleAdsServiceClient();
        $this->googleAdsGeoTargetService = AdvertisingLocationsServiceFactory::make(AdvertisingPlatform::GOOGLE->value);
    }

    /**
     * @return Collection
     */
    public function getCampaignStatusOptions(): Collection
    {
        return collect([
            self::CAMPAIGN_STATUS_ENABLED => "Enabled",
            self::CAMPAIGN_STATUS_PAUSED => "Paused",
            self::CAMPAIGN_STATUS_REMOVED => "Removed"
        ]);
    }

    /** @inheritDoc */
    public function getCampaignsPaginated(
        $accountId,
        int $page,
        int $perPage = 10,
        ?int $searchStateLocationId = null,
        ?string $tcpaCpc = null,
        ?string $status = null,
        ?string $name = null,
        ?array &$pageTokens = null
    ): AdCampaignsPaginatorDataModel
    {
        if(empty($pageTokens)) {
            $pageTokens = [1 => ''];
            $page = 1;
        }

        $pageNo = $page;

        try {
            $campaignsQuery = $this->buildCampaignsQuery($accountId, $searchStateLocationId, $tcpaCpc, $status, $name);

            if(is_null($campaignsQuery)) {
                $paginator = new LengthAwarePaginator([], 0, $perPage, $pageNo);

                return new AdCampaignsPaginatorDataModel(
                    $pageNo,
                    $perPage,
                    $paginator->lastPage() ?? 0,
                    $paginator->firstItem() ?? 0,
                    $paginator->lastItem() ?? 0,
                    $paginator->total() ?? 0,
                    $paginator->getCollection()->toArray() ?? [],
                    $paginator->linkCollection()->toArray() ?? [],
                    $paginator->path(),
                    $paginator->url(1),
                    $paginator->url($paginator->lastPage()),
                    $paginator->nextPageUrl() ?? '',
                    $paginator->previousPageUrl() ?? ''
                );
            }

            $pageNo = $this->getCampaignResultsPage($accountId, $campaignsQuery, $pageNo, $perPage, $pageTokens);

            [
                self::FUNC_RETURN_CAMPAIGNS => $campaigns,
                self::FUNC_RETURN_TOTAL_NUMBER_OF_RESULTS => $totalNumberOfResults,
                self::FUNC_RETURN_CAMPAIGN_BUDGET_IDS => $campaignBudgetIds
            ] = $this->getCampaignResultsForPage($accountId, $campaignsQuery, $pageNo, $perPage, $pageTokens);

            if($campaigns->isNotEmpty()) {
                $this->attachCampaignGeoTargets($accountId, $campaigns);
            }
            if(!empty($campaignBudgetIds)) {
                $this->getCampaignBudgets($accountId, $campaigns, $campaignBudgetIds);
            }

            $this->advertisingCampaignService->attachAutomationStatusToPlatformCampaigns($campaigns, AdvertisingPlatform::GOOGLE->value, $accountId);
            $this->advertisingCampaignService->attachAutomationParametersToPlatformCampaigns($campaigns, AdvertisingPlatform::GOOGLE->value, $accountId);

            $paginator = new LengthAwarePaginator(
                app(AdvertisingCampaignsTransformer::class)->transformCampaigns($campaigns),
                $totalNumberOfResults,
                $perPage,
                $pageNo
            );

            $paginatedResults = new AdCampaignsPaginatorDataModel(
                $pageNo,
                $perPage,
                $paginator->lastPage() ?? 0,
                $paginator->firstItem() ?? 0,
                $paginator->lastItem() ?? 0,
                $paginator->total() ?? 0,
                $paginator->getCollection()->toArray() ?? [],
                $paginator->linkCollection()->toArray() ?? [],
                $paginator->path(),
                $paginator->url(1),
                $paginator->url($paginator->lastPage()),
                $paginator->nextPageUrl() ?? '',
                $paginator->previousPageUrl() ?? ''
            );
        }
        catch(Throwable $e) {
            $errorLocation = __METHOD__.": Line ".$e->getLine().". ";

            AdvertisingLoggingService::writeLog(
                $errorLocation.substr($e->getMessage(), 0, 255),
                [
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            logger()->error($errorLocation.$e->getMessage());
            logger()->error($e->getTraceAsString());

            throw $e;
        }

        return $paginatedResults;
    }

    /**
     * @param $accountId
     * @param int|null $searchStateLocationId
     * @param string|null $costMetric
     * @param string|null $status
     * @param string|null $name
     * @return string|null
     * @throws ApiException
     */
    private function buildCampaignsQuery(
        $accountId,
        ?int $searchStateLocationId = null,
        ?string $costMetric = null,
        ?string $status = null,
        ?string $name = null
    ): ?string
    {
        $campaignsQuery = "SELECT campaign.id, campaign.name, campaign.campaign_budget, campaign.status FROM campaign";

        if($searchStateLocationId > 0) {
            $searchCampaignIds = [];

            $stateCriteriaId = GoogleAdsGeoTarget::query()
                ->where(GoogleAdsGeoTarget::FIELD_TARGET_TYPE, GoogleAdsGeoTarget::TARGET_TYPE_STATE)
                ->where(GoogleAdsGeoTarget::FIELD_LOCATION_ID, $searchStateLocationId)
                ->first()
                ->{GoogleAdsGeoTarget::FIELD_CRITERIA_ID};

            $countyGeoTargetIds = GoogleAdsGeoTarget::query()
                ->where(GoogleAdsGeoTarget::FIELD_TARGET_TYPE, GoogleAdsGeoTarget::TARGET_TYPE_COUNTY)
                ->where(GoogleAdsGeoTarget::FIELD_PARENT_ID, $stateCriteriaId)
                ->pluck(GoogleAdsGeoTarget::FIELD_CRITERIA_ID)
                ->map(function($criteriaId) {
                    return ResourceNames::forGeoTargetConstant($criteriaId);
                })
                ->join("','");

            $searchStateQuery = sprintf(
                "SELECT %s FROM %s WHERE %s AND %s",
                "campaign.id",
                "campaign_criterion",
                "campaign_criterion.type = 'LOCATION'",
                "campaign_criterion.location.geo_target_constant IN ('{$countyGeoTargetIds}')"
            );

            $searchStateStream = $this->googleAdsServiceClient->searchStream(
                SearchGoogleAdsStreamRequest::build($accountId, $searchStateQuery)
            );

            // Iterates over all rows in all messages
            /** @var GoogleAdsRow $googleAdsRow */
            foreach ($searchStateStream->iterateAllElements() as $googleAdsRow) {
                $searchCampaignIds[] = $googleAdsRow->getCampaign()->getId();
            }

            if(!empty($searchCampaignIds)) {
                $whereClauses[] = "campaign.id IN ('".implode("','", $searchCampaignIds)."')";
            }
            else {
                return null;
            }
        }
        if(in_array($costMetric, array_keys(AdvertisingCostMetric::all()), true)) {
            $whereClauses[] = "campaign.name LIKE '%{$costMetric}%'";
        }
        if(in_array($status, array_keys($this->getCampaignStatusOptions()->toArray()), true)) {
            $searchStatus = match($status) {
                self::CAMPAIGN_STATUS_ENABLED => "ENABLED",
                self::CAMPAIGN_STATUS_PAUSED => "PAUSED",
                self::CAMPAIGN_STATUS_REMOVED => "REMOVED"
            };

            $whereClauses[] = "campaign.status = '{$searchStatus}'";
        }
        if(!empty($name)) {
            $whereClauses[] = "campaign.name LIKE '%{$name}%'";
        }

        if(!empty($whereClauses)) {
            $campaignsQuery .= " WHERE ".implode(' AND ', $whereClauses);
        }

        return $campaignsQuery;
    }

    /**
     * @param $accountId
     * @param string $campaignsQuery
     * @param int $pageNo
     * @param int $perPage
     * @param array $pageTokens
     * @return int
     * @throws ApiException
     */
    private function getCampaignResultsPage($accountId, string $campaignsQuery, int $pageNo, int $perPage, array &$pageTokens): int
    {
        while (count($pageTokens) < $pageNo) {
            $currentPage = count($pageTokens);

            // Fetches the next unknown page.
            $response = $this->googleAdsServiceClient->search(
                SearchGoogleAdsRequest::build($accountId, $campaignsQuery)
                    ->setPageSize($perPage)
                    // Requests to return the total results count. This is necessary to
                    // determine how many pages of results exist.
                    ->setReturnTotalResultsCount(true)
                    // There is no need to go over the pages we already know the page tokens for.
                    // Fetches the last page we know the page token for so that we can retrieve the
                    // token of the page that comes after it.
                    ->setPageToken($pageTokens[$currentPage] ?? '')
            );

            if ($response->getPage()->getNextPageToken()) {
                // Stores the page token of the page that comes after the one we just fetched if
                // any so that it can be reused later if necessary.
                $pageTokens[$currentPage + 1] = $response->getPage()->getNextPageToken();
            }
            else if((int) ceil($response->getPage()->getResponseObject()->getTotalResultsCount() / $perPage) === $pageNo) {
                break;
            }
            else {
                // Otherwise changes the requested page number for the latest page that we have
                // fetched until now, the requested page number was invalid.
                $pageNo = count($pageTokens);
            }
        }

        return $pageNo;
    }

    /**
     * @param $accountId
     * @param string $campaignsQuery
     * @param int $pageNo
     * @param int $perPage
     * @param array $pageTokens
     * @return array
     * @throws ApiException|ValidationException
     */
    #[ArrayShape([self::FUNC_RETURN_CAMPAIGNS => "mixed", self::FUNC_RETURN_TOTAL_NUMBER_OF_RESULTS => "mixed", self::FUNC_RETURN_CAMPAIGN_BUDGET_IDS => "array"])]
    private function getCampaignResultsForPage($accountId, string $campaignsQuery, int $pageNo, int $perPage, array &$pageTokens): array
    {
        // Fetches the actual page that we want to display the results of.
        $response = $this->googleAdsServiceClient->search(
            SearchGoogleAdsRequest::build($accountId, $campaignsQuery)
                ->setPageSize($perPage)
                // Requests to return the total results count. This is necessary to
                // determine how many pages of results exist.
                ->setReturnTotalResultsCount(true)
                // The page token of the requested page is in the page token list because of the
                // processing done in the previous loop.
                ->setPageToken($pageTokens[$pageNo] ?? $pageTokens[count($pageTokens)])
        );

        if ($response->getPage()->getNextPageToken()) {
            // Stores the page token of the page that comes after the one we just fetched if any so
            // that it can be reused later if necessary.
            $pageTokens[$pageNo + 1] = $response->getPage()->getNextPageToken();
        }

        // Determines the total number of results to display.
        // The total results count does not take into consideration the LIMIT clause of the query
        // so we need to find the minimal value between the limit and the total results count.
        $totalNumberOfResults = min(
            self::RESULTS_LIMIT,
            $response->getPage()->getResponseObject()->getTotalResultsCount()
        );

        // Extracts the results for the requested page.
        $campaigns = collect();
        $campaignBudgetIds = [];
        /** @var GoogleAdsRow $googleAdsRow */
        foreach ($response->getPage()->getIterator() as $googleAdsRow) {
            // Store each campaign as a PHP array
            $decodedCampaign = json_decode($googleAdsRow->serializeToJsonString(), true)['campaign'];

            $decodedCampaign['locations'] = [
                'include' => [],
                'exclude' => []
            ];

            $campaigns->put($decodedCampaign['id'], $decodedCampaign);

            $campaignCriterionCampaignIds[ResourceNames::forCampaign($accountId, $decodedCampaign['id'])] = $decodedCampaign['id'];
            $campaignBudgetIds[CampaignBudgetServiceClient::parseName($decodedCampaign['campaignBudget'])['campaign_budget_id']] = $decodedCampaign['id'];
        }

        return [
            self::FUNC_RETURN_CAMPAIGNS => $campaigns,
            self::FUNC_RETURN_TOTAL_NUMBER_OF_RESULTS => $totalNumberOfResults,
            self::FUNC_RETURN_CAMPAIGN_BUDGET_IDS => $campaignBudgetIds
        ];
    }

    /**
     * @param $accountId
     * @param Collection $campaigns
     * @return Collection
     * @throws Exception
     */
    private function attachCampaignGeoTargets($accountId, Collection &$campaigns): Collection
    {
        $campaignLocations = $this->googleAdsGeoTargetService->fetchCampaignLocationsFromPlatformAPI($accountId, $campaigns->keys()->toArray());

        $geoTargetInfo = GoogleAdsGeoTarget::query()
                            ->with([
                                GoogleAdsGeoTarget::RELATION_LOCATION => function($relation) {
                                    $relation->select([
                                        Location::ID,
                                        Location::TYPE
                                    ]);
                                }
                            ])
                            ->whereIn(
                                GoogleAdsGeoTarget::FIELD_CRITERIA_ID,
                                $campaignLocations[AdvertisingLocationsServiceContract::FUNC_RETURN_PLATFORM_LOCATION_IDS]->flatten()->unique()->toArray()
                            )
                            ->select([
                                GoogleAdsGeoTarget::FIELD_CRITERIA_ID,
                                GoogleAdsGeoTarget::FIELD_LOCATION_ID,
                                GoogleAdsGeoTarget::FIELD_CANONICAL_NAME
                            ])
                            ->get()
                            ->keyBy(GoogleAdsGeoTarget::FIELD_CRITERIA_ID)
                            ->toArray();

        $lockedLocations = LockedAdvertisingCampaignLocation::query()
                                ->where(LockedAdvertisingCampaignLocation::FIELD_PLATFORM, AdvertisingPlatform::GOOGLE->value)
                                ->where(LockedAdvertisingCampaignLocation::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                                ->whereIn(LockedAdvertisingCampaignLocation::FIELD_PLATFORM_CAMPAIGN_ID, $campaigns->keys()->toArray())
                                ->get()
                                ->groupBy([LockedAdvertisingCampaignLocation::FIELD_PLATFORM_CAMPAIGN_ID, LockedAdvertisingCampaignLocation::FIELD_LOCATION_ID])
                                ->toArray();

        foreach($campaigns as $campaignId => &$campaign) {
            foreach($campaignLocations[AdvertisingLocationsServiceContract::FUNC_RETURN_CAMPAIGN_LOCATIONS]->get($campaignId, collect()) as $geoTargetId => $targeted) {
                $geoTargetLocationId = $geoTargetInfo[$geoTargetId][GoogleAdsGeoTarget::FIELD_LOCATION_ID] ?? 0;
                $locationType = $geoTargetInfo[$geoTargetId][GoogleAdsGeoTarget::RELATION_LOCATION][Location::TYPE] ?? null;
                $name = $geoTargetInfo[$geoTargetId][GoogleAdsGeoTarget::FIELD_CANONICAL_NAME] ?? "$geoTargetId (Google Ads ID; Location not in SR database)";
                $locked = (int) ($lockedLocations[$campaignId][$geoTargetLocationId][0][LockedAdvertisingCampaignLocation::FIELD_TARGETED] ?? LockedAdvertisingCampaignLocation::TARGETED_NONE);

                $dashboardTargetLocation = (new DashboardTargetLocation($geoTargetLocationId, $name, $locked, $locationType))->toArray();

                if ($targeted) {
                    $campaign['locations']['include'][$geoTargetId] = $dashboardTargetLocation;
                }
                else {
                    $campaign['locations']['exclude'][$geoTargetId] = $dashboardTargetLocation;
                }
            }

            $campaigns->put($campaignId, $campaign);
        }

        return $campaigns;
    }

    /**
     * @param $accountId
     * @param Collection $campaigns
     * @param array $campaignBudgetIds
     * @return Collection
     * @throws ApiException
     */
    private function getCampaignBudgets($accountId, Collection &$campaigns, array $campaignBudgetIds): Collection
    {
        $campaignBudgetIdsQuery = implode(",", array_keys($campaignBudgetIds));

        $budgetsQuery = sprintf(
            "SELECT %s FROM %s WHERE %s",
            "campaign_budget.id, campaign_budget.amount_micros, campaign_budget.resource_name",
            "campaign_budget",
            "campaign_budget.id IN ({$campaignBudgetIdsQuery})"
        );

        $budgetStream = $this->googleAdsServiceClient->searchStream(
            SearchGoogleAdsStreamRequest::build($accountId, $budgetsQuery)
        );

        // Iterates over all rows in all messages
        /** @var GoogleAdsRow $googleAdsRow */
        foreach ($budgetStream->iterateAllElements() as $googleAdsRow) {
            $campaignBudget = $googleAdsRow->getCampaignBudget();

            $campaignId = $campaignBudgetIds[$campaignBudget->getId()];

            $campaign = $campaigns->get($campaignId);

            $campaign['campaign_budget_amount'] = $campaignBudget->getAmountMicros() / 1000000; //Divide by 1 million to convert to dollars

            $campaigns->put($campaignId, $campaign);
        }

        return $campaigns;
    }

    /** @inheritDoc */
    public function getAutomatedCampaignsWithLocations(int $logId, array $retryCampaigns = []): ?AccountCampaigns
    {
        AdvertisingLoggingService::writeLog("Retrieving automated campaigns for locations update", ["id" => $logId, "platform" => AdvertisingPlatform::GOOGLE->value]);

        $accountCampaigns = $this->advertisingCampaignService->getAutomatedCampaignsWithParametersByPlatform(AdvertisingPlatform::GOOGLE->value);

        if(is_null($accountCampaigns)) {
            AdvertisingLoggingService::writeLog("No ad campaigns to update", ["id" => $logId, "platform" => AdvertisingPlatform::GOOGLE->value]);
            return null;
        }

        AdvertisingLoggingService::writeLog(
            "Attaching locations to automated campaigns",
            [
                "id" => $logId,
                "platform" => AdvertisingPlatform::GOOGLE->value,
                "automated_campaigns" => $accountCampaigns->getAccountCampaigns()->mapWithKeys(function($campaigns, $accountId) { return [$accountId => $campaigns->keys()->toArray()]; })->toArray()
            ]
        );

        foreach($accountCampaigns as $accountId => &$campaigns) {
            $geoTargetCriteriaIds = $this->googleAdsGeoTargetService->fetchCampaignLocationsFromPlatformAPI($accountId, $campaigns->keys()->toArray())[AdvertisingLocationsServiceContract::FUNC_RETURN_PLATFORM_LOCATION_IDS];

            $geoTargetLocations = GoogleAdsGeoTarget::query()
                ->whereIn(GoogleAdsGeoTarget::FIELD_CRITERIA_ID, $geoTargetCriteriaIds->flatten()->unique()->toArray())
                ->pluck(GoogleAdsGeoTarget::FIELD_CRITERIA_ID, GoogleAdsGeoTarget::FIELD_LOCATION_ID)
                ->toArray();

            foreach($campaigns as $campaignId => &$campaign) {
                $campaign->setLocations(
                    array_flip(
                        array_intersect(
                            $geoTargetLocations,
                            $geoTargetCriteriaIds[$campaignId]->toArray()
                        )
                    )
                );
            }
        }

        return $accountCampaigns;
    }

    /** @inheritDoc */
    public function populateCampaignLocationsToUpdate($accountId, CampaignLocations &$campaignLocations, Collection $campaigns, array $excludeLocations, int $logId): CampaignLocations
    {
        $locationPlatformIds = $this->googleAdsGeoTargetService->getPlatformIdsByLocation(collect($excludeLocations)->flatten()->toArray());

        $missingPlatformLocations = [];
        foreach($excludeLocations as $campaignId => $excludeLocationIds) {
            $missingPlatformLocations[$campaignId] = [];

            foreach($excludeLocationIds as $excludeLocationId) {
                if(!empty($locationPlatformIds[$excludeLocationId])) {
                    $campaignLocations->setCampaignLocation($accountId, $campaignId, $locationPlatformIds[$excludeLocationId], true);
                }
                else {
                    $missingPlatformLocations[$campaignId] = $excludeLocationId;
                }
            }

            $includePlatformIds = $campaigns->get($campaignId)->{Campaign::LOCATIONS}->flip()->except($excludeLocationIds)->values()->toArray();

            foreach($includePlatformIds as $includePlatformId) {
                $campaignLocations->setCampaignLocation($accountId, $campaignId, $includePlatformId, false);
            }
        }

        AdvertisingLoggingService::writeLog(
            "Missing platform locations",
            [
                "id" => $logId,
                "platform" => AdvertisingPlatform::GOOGLE->value,
                "account_id" => $accountId,
                "missing_locations" => $missingPlatformLocations
            ]
        );

        return $campaignLocations;
    }

    /** @inheritDoc */
    public function updateCampaignLocations(int $logId, CampaignLocations $campaignLocations, bool $isRetry = false): bool
    {
        $success = false;

        try {
            $jobs = [];

            $currentTimestamp = Carbon::now()->timestamp;
            foreach($campaignLocations as $accountId => $campaigns) {
                [
                    self::FUNC_RETURN_OPERATIONS => $operations,
                    self::FUNC_RETURN_OPERATIONS_INFO => $operationsInfo,
                    self::FUNC_RETURN_SUCCESSFUL_CAMPAIGN_IDS => $successfulCampaignIds
                ]
                = $this->buildUpdateOperations($accountId, $campaigns, $logId);

                AdvertisingLoggingService::writeLog(
                    "Built update operations",
                    [
                        "id" => $logId,
                        "platform" => AdvertisingPlatform::GOOGLE->value,
                        "account_id" => $accountId,
                        "no_operations_campaigns" => $successfulCampaignIds
                    ]
                );

                $jobs = $this->createUpdateBatchJobs($accountId, $operations);

                AdvertisingLoggingService::writeLog(
                    "Update batch jobs created",
                    [
                        "id" => $logId,
                        "platform" => AdvertisingPlatform::GOOGLE->value,
                        "account_id" => $accountId
                    ]
                );

                $this->processUpdateBatchJobResults($accountId, $jobs, $operationsInfo, $successfulCampaignIds, $currentTimestamp, $logId);

                AdvertisingLoggingService::writeLog(
                    "Processed batch jobs",
                    [
                        "id" => $logId,
                        "platform" => AdvertisingPlatform::GOOGLE->value,
                        "account_id" => $accountId,
                        "successful_campaign_ids" => $successfulCampaignIds
                    ]
                );

                AdvertisingCampaign::query()
                    ->where(AdvertisingCampaign::FIELD_PLATFORM, AdvertisingPlatform::GOOGLE->value)
                    ->where(AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                    ->whereIn(AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID, array_unique($successfulCampaignIds))
                    ->update([
                        AdvertisingCampaign::FIELD_LAST_RUN_TIMESTAMP => $currentTimestamp
                    ]);
            }

            $success = !in_array(false, data_get($jobs, '*.results.*'), true);
        }
        catch(Exception $e) {
            $errMsg = __METHOD__.": Line ".$e->getLine().". ".$e->getMessage();

            AdvertisingLoggingService::writeLog($errMsg, ["id" => $logId, "platform" => AdvertisingPlatform::GOOGLE->value]);

            logger()->error($errMsg);
            logger()->error($e->getTraceAsString());

            throw $e;
        }

        AdvertisingLoggingService::writeLog("Campaign locations updated", ["id" => $logId, "platform" => AdvertisingPlatform::GOOGLE->value]);

        return $success;
    }

    /**
     * @param int $logId
     * @param $accountId
     * @param Collection $campaigns
     * @return array
     */
    private function buildUpdateOperations($accountId, Collection $campaigns, int $logId): array
    {
        $campaignCriterionTargetedStatuses = $this->googleAdsGeoTargetService->fetchCampaignLocationsFromPlatformAPI($accountId, $campaigns->keys()->toArray())[AdvertisingLocationsServiceContract::FUNC_RETURN_CAMPAIGN_LOCATIONS];

        $accountCriteriaIds = $campaigns
                                ->map(function($criteriaIds) {
                                    return $criteriaIds->keys();
                                })
                                ->flatten()
                                ->toArray();

        $geoTargetTypes = GoogleAdsGeoTarget::query()
            ->whereIntegerInRaw(GoogleAdsGeoTarget::FIELD_CRITERIA_ID, $accountCriteriaIds)
            ->pluck(GoogleAdsGeoTarget::FIELD_TARGET_TYPE, GoogleAdsGeoTarget::FIELD_CRITERIA_ID)
            ->toArray();

        $batch = 0; //zero index batches
        $operations = [
            $batch => [
                'campaign_ids' => [],
                'operations' => []
            ]
        ];
        $operationsInfo = [
            $batch => []
        ];
        $operationIdx = -1;
        $successfulCampaignIds = [];
        foreach($campaigns as $googleCampaignId => $locations) {
            AdvertisingLoggingService::writeLog(
                "Building campaign location update operations",
                [
                    "id" => $logId,
                    "platform" => AdvertisingPlatform::GOOGLE->value,
                    "account_id" => $accountId,
                    "campaign_id" => $googleCampaignId,
                    "campaign_locations" => $locations->toArray()
                ]
            );

            $campaignOperations = [];
            $campaignOperationsInfo = [];

            foreach($locations as $criteriaId => $exclude) {
                if ($geoTargetTypes[$criteriaId] === GoogleAdsGeoTarget::TARGET_TYPE_COUNTY) {
                    $targeted = isset($campaignCriterionTargetedStatuses[$googleCampaignId][$criteriaId]) ? $campaignCriterionTargetedStatuses[$googleCampaignId][$criteriaId] : false;

                    $addToExclude = $exclude && $targeted;
                    $addToInclude = !$exclude && !$targeted;

                    if($addToExclude
                    || $addToInclude) {
                        $currentNegativeState = $addToInclude;

                        $operationIdx++;
                        $campaignOperations[$operationIdx] = new MutateOperation([
                            'campaign_criterion_operation' => new CampaignCriterionOperation(['remove' => ResourceNames::forCampaignCriterion($accountId, $googleCampaignId, $criteriaId)])
                        ]);
                        $campaignOperationsInfo[$operationIdx] = [
                            'action' => 'remove',
                            'target_type' => GoogleAdsGeoTarget::TARGET_TYPE_COUNTY,
                            'criteria_id' => $criteriaId,
                            'campaign_id' => $googleCampaignId,
                            'account_id' => $accountId,
                            'message' => $currentNegativeState ? "Remove county from exclusion list" : "Remove county from inclusion list"
                        ];

                        $campaignCriterion = new CampaignCriterion([
                            'location' => new LocationInfo([
                                'geo_target_constant' => ResourceNames::forGeoTargetConstant((int) $criteriaId)
                            ]),
                            'negative' => !$currentNegativeState,
                            'campaign' => ResourceNames::forCampaign($accountId, $googleCampaignId)
                        ]);

                        $operationIdx++;
                        $campaignOperations[$operationIdx] = new MutateOperation([
                            'campaign_criterion_operation' => new CampaignCriterionOperation(['create' => $campaignCriterion])
                        ]);
                        $campaignOperationsInfo[$operationIdx] = [
                            'action' => 'create',
                            'target_type' => GoogleAdsGeoTarget::TARGET_TYPE_COUNTY,
                            'criteria_id' => $criteriaId,
                            'campaign_id' => $googleCampaignId,
                            'account_id' => $accountId,
                            'message' => $currentNegativeState ? "Add county to inclusion list" : "Add county to exclusion list"
                        ];
                    }
                }
                else if($geoTargetTypes[$criteriaId] === GoogleAdsGeoTarget::TARGET_TYPE_POSTAL_CODE) {
                    $targeted = isset($campaignCriterionTargetedStatuses[$googleCampaignId][$criteriaId]) ? $campaignCriterionTargetedStatuses[$googleCampaignId][$criteriaId] : true;

                    if($exclude && $targeted) {
                        //add to exclude
                        $campaignCriterion = new CampaignCriterion([
                            'location' => new LocationInfo([
                                'geo_target_constant' => ResourceNames::forGeoTargetConstant((int) $criteriaId)
                            ]),
                            'negative' => true,
                            'campaign' => ResourceNames::forCampaign($accountId, $googleCampaignId)
                        ]);

                        $operationIdx++;
                        $campaignOperations[$operationIdx] = new MutateOperation([
                            'campaign_criterion_operation' => new CampaignCriterionOperation(['create' => $campaignCriterion])
                        ]);
                        $campaignOperationsInfo[$operationIdx] = [
                            'action' => 'create',
                            'target_type' => GoogleAdsGeoTarget::TARGET_TYPE_POSTAL_CODE,
                            'criteria_id' => $criteriaId,
                            'campaign_id' => $googleCampaignId,
                            'account_id' => $accountId,
                            'message' => "Add zipcode to exclusion list"
                        ];
                    }
                    else if(!$exclude && !$targeted) {
                        //remove from exclude
                        $operationIdx++;
                        $campaignOperations[$operationIdx] = new MutateOperation([
                            'campaign_criterion_operation' => new CampaignCriterionOperation(['remove' => ResourceNames::forCampaignCriterion($accountId, $googleCampaignId, $criteriaId)])
                        ]);
                        $campaignOperationsInfo[$operationIdx] = [
                            'action' => 'remove',
                            'target_type' => GoogleAdsGeoTarget::TARGET_TYPE_POSTAL_CODE,
                            'criteria_id' => $criteriaId,
                            'campaign_id' => $googleCampaignId,
                            'account_id' => $accountId,
                            'message' => "Remove zipcode from exclusion list"
                        ];
                    }
                }
            }

            $operationsCount = count($campaignOperations);

            if((count($operations[$batch]) + $operationsCount) >= self::REQUEST_MUTATE_OPERATIONS_LIMIT) {
                $batch++;
                $operationIdx = -1;
                $operations[$batch] = [
                    'campaign_ids' => [],
                    'operations' => []
                ];
                $operationsInfo[$batch] = [];
            }

            if($operationsCount > 0) {
                $operations[$batch]['campaign_ids'][] = $googleCampaignId;
            }
            else {
                //Consider campaigns with no operations to be successful so we can log the run
                $successfulCampaignIds[] = $googleCampaignId;
            }

            $operations[$batch]['operations'] = array_merge($operations[$batch]['operations'], $campaignOperations);
            $operationsInfo[$batch] = array_merge($operationsInfo[$batch], $campaignOperationsInfo);
        }

        return [
            self::FUNC_RETURN_OPERATIONS => $operations,
            self::FUNC_RETURN_OPERATIONS_INFO => $operationsInfo,
            self::FUNC_RETURN_SUCCESSFUL_CAMPAIGN_IDS => $successfulCampaignIds
        ];
    }

    /**
     * @param $accountId
     * @param array $operations
     * @return array
     * @throws ApiException
     */
    private function createUpdateBatchJobs($accountId, array $operations): array
    {
        /** @var BatchJobServiceClient $batchJobServiceClient */
        $batchJobServiceClient = $this->googleAdsClient->getBatchJobServiceClient();

        $jobs[$accountId] = [
            'results' => [],
            'operation_batches' => []
        ];
        foreach($operations as $batchIdx => $batchOperations) {
            if(!empty($batchOperations['operations'])) {
                $batchJobOperation = new BatchJobOperation();
                $batchJobOperation->setCreate(new BatchJob());

                $batchJobResourceName = $batchJobServiceClient
                    ->mutateBatchJob(
                        MutateBatchJobRequest::build($accountId, $batchJobOperation)
                    )
                    ->getResult()
                    ->getResourceName();

                $chunkedBatchOps = array_chunk($batchOperations['operations'], self::ADD_BATCH_JOB_OPS_LIMIT, true);
                $nextSequenceToken = '';
                foreach($chunkedBatchOps as $chunk) {
                    $addBatchJobOperationResponse = $batchJobServiceClient->addBatchJobOperations(
                        AddBatchJobOperationsRequest::build(
                            $batchJobResourceName,
                            $nextSequenceToken,
                            $chunk
                        )
                    );

                    $nextSequenceToken = $addBatchJobOperationResponse->getNextSequenceToken();
                }

                $jobs[$accountId]['operation_batches'][$batchJobResourceName] = [
                    'operation_response' => $batchJobServiceClient->runBatchJob(RunBatchJobRequest::build($batchJobResourceName)),
                    'campaign_ids' => $batchOperations['campaign_ids'],
                    'batch_index' => $batchIdx
                ];
            }
        }

        return $jobs;
    }

    /**
     * @param $accountId
     * @param array $jobs
     * @param array $operationsInfo
     * @param array $successfulCampaignIds
     * @param int $currentTimestamp
     * @param int $logId
     * @throws ApiException
     * @throws ValidationException
     */
    private function processUpdateBatchJobResults($accountId, array &$jobs, array $operationsInfo, array &$successfulCampaignIds, int $currentTimestamp, int $logId): void
    {
        $batchJobServiceClient = $this->googleAdsClient->getBatchJobServiceClient();

        foreach($jobs[$accountId]['operation_batches'] as $bjrn => $operationBatch) {
            $operationBatch['operation_response']->pollUntilComplete([
                'initialPollDelayMillis' => 10000, //10 seconds
                'totalPollTimeoutMillis' => 480000 //8 minutes
            ]);

            $batchJobResults = $batchJobServiceClient->listBatchJobResults(ListBatchJobResultsRequest::build($bjrn)->setPageSize(self::BATCH_RESULTS_PAGE_SIZE));
            $success = true;

            /** @var BatchJobResult $batchJobResult */
            foreach($batchJobResults->iterateAllElements() as $batchJobResult) {
                $resourceName = $batchJobResult->getMutateOperationResponse()?->getCampaignCriterionResult()?->getResourceName();

                if($resourceName) {
                    $successfulCampaignIds[] = CampaignCriterionServiceClient::parseName($resourceName)['campaign_id'];
                }
                else {
                    $success = false;
                }

                if($batchJobResult->getStatus()) {
                    $logErr = json_encode([
                        'message' => $batchJobResult->getStatus()->getMessage(),
                        'params' => $operationsInfo[$operationBatch['batch_index']][$batchJobResult->getOperationIndex()]
                    ]);

                    AdvertisingLoggingService::writeLog(__METHOD__.": $logErr", ['id' => $logId, 'platform' => AdvertisingPlatform::GOOGLE->value]);
                    logger()->error(__METHOD__.": $logErr");
                }
            }

            $jobs[$accountId]['results'][] = $success;

            $logMsg = $success ? "Campaign locations updated" : "Campaign locations update failed";

            $logEntries = [];
            foreach($operationBatch['campaign_ids'] as $campaignId) {
                $logEntries[] = [
                    AdvertisingCampaignHistoryLog::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE->value,
                    AdvertisingCampaignHistoryLog::FIELD_PLATFORM_ACCOUNT_ID => $accountId,
                    AdvertisingCampaignHistoryLog::FIELD_PLATFORM_CAMPAIGN_ID => $campaignId,
                    AdvertisingCampaignHistoryLog::FIELD_MESSAGE => $logMsg,
                    AdvertisingCampaignHistoryLog::FIELD_TYPE => AdvertisingCampaignHistoryLog::TYPE_LOCATIONS_CHANGE,
                    AdvertisingCampaignHistoryLog::CREATED_AT => $currentTimestamp,
                    AdvertisingCampaignHistoryLog::UPDATED_AT => $currentTimestamp
                ];
            }

            AdvertisingLoggingService::bulkInsertLogEntries($logEntries);
        }
    }

    /**
     * Get tiered campaign information, get county assignment information, update campaigns with new county assignments
     *
     * @param int $industryId
     * @param int|null $instanceId
     * @return bool
     */
    public function updateTieredAdsCampaignLocations(int $industryId, ?int $instanceId = null): bool
    {
        $start = microtime(true);
        $logId = (int) $start;

        $industryModel = Industry::find($industryId);

        // Get positive counties and negative zip codes
        $campaignCountyAssignments = $this->tieredAdvertisingService->getCampaignCountyAssignments(AdvertisingPlatform::GOOGLE->value, $industryId, $instanceId);

        $geoTargetData = GoogleAdsGeoTarget::query()
            ->orWhere(GoogleAdsGeoTarget::FIELD_TARGET_TYPE, GoogleAdsGeoTarget::TARGET_TYPE_POSTAL_CODE)
            ->orWhere(GoogleAdsGeoTarget::FIELD_TARGET_TYPE, GoogleAdsGeoTarget::TARGET_TYPE_COUNTY)
            ->get([
                GoogleAdsGeoTarget::FIELD_NAME,
                GoogleAdsGeoTarget::FIELD_TARGET_TYPE,
                GoogleAdsGeoTarget::FIELD_CRITERIA_ID,
                GoogleAdsGeoTarget::FIELD_CRITERIA_ID,
                GoogleAdsGeoTarget::FIELD_TARGET_TYPE,
                GoogleAdsGeoTarget::FIELD_LOCATION_ID,
            ]);

        $googleAdsTargetTypes = $geoTargetData->pluck(GoogleAdsGeoTarget::FIELD_TARGET_TYPE, GoogleAdsGeoTarget::FIELD_CRITERIA_ID);
        $zipCodeToGoogleCriteriaId = $geoTargetData->where(GoogleAdsGeoTarget::FIELD_TARGET_TYPE, GoogleAdsGeoTarget::TARGET_TYPE_POSTAL_CODE)
            ->pluck(GoogleAdsGeoTarget::FIELD_CRITERIA_ID, GoogleAdsGeoTarget::FIELD_NAME);
        $criteriaToLocationId = $geoTargetData->pluck(GoogleAdsGeoTarget::FIELD_LOCATION_ID, GoogleAdsGeoTarget::FIELD_CRITERIA_ID);

        $platformAcctIdToA2Id = TieredAdvertisingAccount::query()
            ->get([
                TieredAdvertisingAccount::FIELD_ID,
                TieredAdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID,
            ])->pluck(TieredAdvertisingAccount::FIELD_ID, TieredAdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID);

        $platformCampaignIdToA2Id = TieredAdvertisingCampaign::query()
            ->get([
                TieredAdvertisingCampaign::FIELD_ID,
                TieredAdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID,
            ])->pluck(TieredAdvertisingCampaign::FIELD_ID, TieredAdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID);

        // Build campaign location updates array (array with positive and negative locations defined by account and campaign)
        [
            self::FUNC_ACCOUNT_CAMPAIGN_LOCATIONS => $accountCampaignLocations,
            self::FUNC_CRITERIA_ID_TO_ASSIGNMENT_DATA => $criteriaIdToAssignmentData,
        ] = $this->buildTieredAdvertisingLocationUpdates(
            campaignCountyAssignments: $campaignCountyAssignments,
            zipCodeToGoogleCriteriaId: $zipCodeToGoogleCriteriaId
        );

        // Convert array into batched array of google api operations to move counties around
        [
            self::FUNC_RETURN_OPERATIONS => $operations,
            self::FUNC_RETURN_OPERATIONS_INFO => $operationsInfo,
            self::FUNC_RETURN_SUCCESSFUL_CAMPAIGN_IDS => $successfulCampaignIds
        ] = $this->buildTieredAdvertisingUpdateOperations(
            accountCampaignLocations: $accountCampaignLocations,
            googleAdsTargetTypes: $googleAdsTargetTypes,
            criteriaToLocationId: $criteriaToLocationId,
            criteriaToAssignmentData: $criteriaIdToAssignmentData,
            platformAcctIdToA2Id: $platformAcctIdToA2Id,
            platformCampaignIdToA2Id: $platformCampaignIdToA2Id,
            industryId: $industryModel->{Industry::FIELD_ID},
        );

        // Run batches to move counties
        $success = $this->batchUpdateTieredAdvertisingLocations(
            accountCampaignLocations: $accountCampaignLocations,
            operations: $operations,
            operationsInfo: $operationsInfo,
            successfulCampaignIds: $successfulCampaignIds,
            logId: $logId,
        );

        // Update last time location updated field in campaigns
        $this->tieredAdvertisingService->setLocationsUpdatedDate(AdvertisingPlatform::GOOGLE->value, $industryId, $instanceId);

        return $success;
    }

    /**
     * This builds the array defining google account ids => campaign ids => criteria ids => enabled or disabled
     * ex. [
     * '**********' => [
     *     '***********' => [
     *         '9057192' => true, // Denver - would be enabled
     *         '9059506' => true, // Harris - would be enabled
     *         '9059806' => true, // King - would be enabled
     *         '9057037' => false, // Maricopa - would be disabled
     *        ],
     *     ],
     *  ];
     * Anything not included in this array will be removed from location targeting
     *
     * @param Collection $campaignCountyAssignments
     * @param Collection $zipCodeToGoogleCriteriaId
     * @return array
     */
    public function buildTieredAdvertisingLocationUpdates(
        Collection $campaignCountyAssignments,
        Collection $zipCodeToGoogleCriteriaId,
    ): array
    {
        $accountCampaignLocations = [];
        $criteriaIdToAssignmentInfo = collect();

        // Get world locations to exclude
        $worldLocations = GoogleAdsGeoTarget::query()
            ->whereIn(GoogleAdsGeoTarget::FIELD_TARGET_TYPE, [GoogleAdsGeoTarget::TARGET_TYPE_COUNTRY])
            ->whereNot(GoogleAdsGeoTarget::FIELD_CANONICAL_NAME, 'United States')
            ->get([GoogleAdsGeoTarget::FIELD_CRITERIA_ID])->pluck(GoogleAdsGeoTarget::FIELD_CRITERIA_ID);

        $counties = GoogleAdsGeoTarget::query()
            ->join(Location::TABLE, Location::TABLE.'.'.Location::ID, '=', GoogleAdsGeoTarget::TABLE.'.'.GoogleAdsGeoTarget::FIELD_LOCATION_ID)
            ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_COUNTY)
            ->get([
                Location::TABLE.'.'.Location::STATE_ABBREVIATION,
                Location::TABLE.'.'.Location::COUNTY,
                GoogleAdsGeoTarget::TABLE.'.'.GoogleAdsGeoTarget::FIELD_CRITERIA_ID,
            ]);

        $states = GoogleAdsGeoTarget::query()
            ->join(Location::TABLE, Location::TABLE.'.'.Location::ID, '=', GoogleAdsGeoTarget::TABLE.'.'.GoogleAdsGeoTarget::FIELD_LOCATION_ID)
            ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_STATE)
            ->get([
                Location::TABLE.'.'.Location::STATE_ABBREVIATION,
                GoogleAdsGeoTarget::TABLE.'.'.GoogleAdsGeoTarget::FIELD_CRITERIA_ID,
            ])->pluck(GoogleAdsGeoTarget::FIELD_CRITERIA_ID, Location::STATE_ABBREVIATION);

        foreach ($campaignCountyAssignments as $accountId => $campaignCounties) {
            $cleanedAcctId = preg_replace('/\D/', '', $accountId);
            $accountCampaignLocations[$cleanedAcctId] = [];

            foreach ($campaignCounties as $campaignId => $countyAssignments) {
                $partialStates = [];
                $cleanedCampaignId = preg_replace('/\D/', '', $campaignId);
                $accountCampaignLocations[$cleanedAcctId][$cleanedCampaignId] = [];

                foreach ($countyAssignments as $countyAssignment) {
                    $accountCampaignLocations[$cleanedAcctId][$cleanedCampaignId][$countyAssignment->{GoogleAdsGeoTarget::FIELD_CRITERIA_ID}] = true;

                    // Add state to partially covered states list
                    $partialStates[] = $countyAssignment->{Location::STATE_ABBREVIATION};

                    // Add non-covered zip codes as negative locations
                    $negativeZipCodes = json_decode($countyAssignment->{TieredAdvertisingCounty::FIELD_NEGATIVE_ZIP_CODES});
                    if ($negativeZipCodes && count($negativeZipCodes) > 0) {
                        foreach ($negativeZipCodes as $negativeZipCode) {
                            $criteriaId = $zipCodeToGoogleCriteriaId[$negativeZipCode] ?? null;

                            // Google Ads does not have all zip codes as geo-targets, about 31,000 of 41,000 total
                            if ($criteriaId)
                                $accountCampaignLocations[$cleanedAcctId][$cleanedCampaignId][$criteriaId] = false;
                        }
                    }

                    // Store related data in log
                    $data = [
                        TieredAdvertisingLog::DATA_TCPA_BID => $countyAssignment->{TieredAdvertisingCampaign::FIELD_TCPA_BID},
                        TieredAdvertisingLog::DATA_NEGATIVE_ZIPS => $countyAssignment->{TieredAdvertisingCounty::FIELD_NEGATIVE_ZIP_CODES},
                        TieredAdvertisingLog::DATA_LOWER_BOUND => $countyAssignment->{TieredAdvertisingCampaign::FIELD_LOWER_BOUND},
                        TieredAdvertisingLog::DATA_UPPER_BOUND => $countyAssignment->{TieredAdvertisingCampaign::FIELD_UPPER_BOUND},
                        TieredAdvertisingLog::DATA_TIER => $countyAssignment->{TieredAdvertisingCampaign::FIELD_TIER},
                    ];

                    // Add data from ERPL calculation
                    $erplData = $countyAssignment->{TieredAdvertisingCounty::FIELD_DATA};
                    if ($erplData)
                        $data = array_merge($data, $erplData);

                    // Will be logged later for google assignment
                    $criteriaIdToAssignmentInfo[$countyAssignment->{GoogleAdsGeoTarget::FIELD_CRITERIA_ID}] = $data;
                }

                // Add negative countries
                foreach ($worldLocations as $worldLocationCriteriaId) {
                    if (!array_key_exists($worldLocationCriteriaId, $accountCampaignLocations[$cleanedAcctId][$cleanedCampaignId])) {
                        $accountCampaignLocations[$cleanedAcctId][$cleanedCampaignId][$worldLocationCriteriaId] = false;
                    }
                }

                // Add negative states
                foreach ($states as $stateAbbr => $stateCriteriaId) {
                    if (!in_array($stateAbbr, $partialStates)) {
                        $accountCampaignLocations[$cleanedAcctId][$cleanedCampaignId][$stateCriteriaId] = false;
                    }
                }

                // Add negative counties
                foreach ($counties as $county) {
                    if (in_array($county->{Location::STATE_ABBREVIATION}, $partialStates) &&
                        !array_key_exists($county->{GoogleAdsGeoTarget::FIELD_CRITERIA_ID}, $accountCampaignLocations[$cleanedAcctId][$cleanedCampaignId])) {
                        $accountCampaignLocations[$cleanedAcctId][$cleanedCampaignId][$county->{GoogleAdsGeoTarget::FIELD_CRITERIA_ID}] = false;
                    }
                }
            }
        }

        return [
            self::FUNC_ACCOUNT_CAMPAIGN_LOCATIONS => $accountCampaignLocations,
            self::FUNC_CRITERIA_ID_TO_ASSIGNMENT_DATA => $criteriaIdToAssignmentInfo,
        ];
    }

    /**
     * Uses the locations enabled array to build the google ads api operations to transition each campaign to the new state
     *
     * @param array $accountCampaignLocations
     * @param Collection $googleAdsTargetTypes
     * @param Collection $criteriaToLocationId
     * @param Collection $criteriaToAssignmentData
     * @param Collection $platformAcctIdToA2Id
     * @param Collection $platformCampaignIdToA2Id
     * @param int $industryId
     * @return array
     */
    private function buildTieredAdvertisingUpdateOperations(
        array $accountCampaignLocations,
        Collection $googleAdsTargetTypes,
        Collection $criteriaToLocationId,
        Collection $criteriaToAssignmentData,
        Collection $platformAcctIdToA2Id,
        Collection $platformCampaignIdToA2Id,
        int $industryId,
    ): array
    {
        $operationIdx = -1;
        $batch = 0;
        $operations = [
            $batch => [
                'campaign_ids' => [],
                'operations' => []
            ]
        ];
        $operationsInfo = [
            $batch => []
        ];
        $successfulCampaignIds = [];
        $tieredAdsLogs = [];

        // Loop through accounts
        foreach ($accountCampaignLocations as $accountId => $newCampaignLocations) {
            // Get current locations for account's campaigns
            $oldCampaignLocations = $this->googleAdsGeoTargetService->fetchCampaignLocationsFromPlatformAPI($accountId, array_keys($newCampaignLocations))[AdvertisingLocationsServiceContract::FUNC_RETURN_CAMPAIGN_LOCATIONS];

            // Loop through all campaigns
            foreach (array_keys($newCampaignLocations) as $googleCampaignId) {
                $campaignOperations = [];
                $campaignOperationsInfo = [];

                $oldCampaignLocationsArray = $oldCampaignLocations[$googleCampaignId] ?? [];
                $newCampaignLocationsArray = $newCampaignLocations[$googleCampaignId] ?? [];

                // Loop through locations in each campaign
                foreach ($oldCampaignLocationsArray as $criteriaId => $oldEnabledStatus) {
                    // Get status of this location in update
                    $newEnabledStatus = $newCampaignLocations[$googleCampaignId][$criteriaId] ?? null;

                    // Remove from locations if:
                    // - Location is not included at all in new locations, means that we will ignore it from campaign completely
                    // - Location is enabled in new locations and was disabled previously
                    // - Location is disabled in new locations and was enabled previously
                    if ($newEnabledStatus === null || ($newEnabledStatus && !$oldEnabledStatus) || ($newEnabledStatus === false && $oldEnabledStatus)) {
                        $operationIdx++;
                        $campaignOperations[$operationIdx] = new MutateOperation([
                            'campaign_criterion_operation' => new CampaignCriterionOperation(['remove' => ResourceNames::forCampaignCriterion($accountId, $googleCampaignId, $criteriaId)])
                        ]);
                        $campaignOperationsInfo[$operationIdx] = [
                            'action'      => 'remove',
                            'target_type' => $googleAdsTargetTypes[$criteriaId] ?? GoogleAdsGeoTarget::TARGET_TYPE_COUNTY,
                            'criteria_id' => $criteriaId,
                            'campaign_id' => $googleCampaignId,
                            'account_id'  => $accountId,
                            'message'     => $oldEnabledStatus ? "Remove county from inclusion list" : "Remove county from exclusion list"
                        ];

                        // Create log entry for county removal
                        $tieredAdsLogs[] = [
                            TieredAdvertisingLog::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE->value,
                            TieredAdvertisingLog::FIELD_INDUSTRY_ID => $industryId,
                            TieredAdvertisingLog::FIELD_LOCATION_ID => $criteriaToLocationId[$criteriaId] ?? null,
                            TieredAdvertisingLog::FIELD_ACCOUNT_ID => $platformAcctIdToA2Id[$accountId] ?? null,
                            TieredAdvertisingLog::FIELD_CAMPAIGN_ID => $platformCampaignIdToA2Id[$googleCampaignId] ?? null,
                            TieredAdvertisingLog::FIELD_TYPE => TieredAdvertisingLog::TYPE_GOOGLE_LOCATION_REMOVE,
                            TieredAdvertisingLog::FIELD_MESSAGE => "", // No message for this, type and other columns convey all necessary information
                            TieredAdvertisingLog::FIELD_DATA => null, // No data for removals
                            TieredAdvertisingLog::CREATED_AT => now(),
                            TieredAdvertisingLog::UPDATED_AT => null,
                        ];
                    }
                }

                // Loop through locations in each campaign
                foreach ($newCampaignLocationsArray as $criteriaId => $newEnabledStatus) {
                    $oldEnabledStatus = $oldCampaignLocations[$googleCampaignId][$criteriaId] ?? null;

                    // Add to locations if:
                    // - Old enabled status is different from new enabled status
                    if ($newEnabledStatus !== $oldEnabledStatus) {
                        $campaignCriterion = new CampaignCriterion([
                            'location' => new LocationInfo([
                                'geo_target_constant' => ResourceNames::forGeoTargetConstant((int) $criteriaId)
                            ]),
                            'negative' => !$newEnabledStatus,
                            'campaign' => ResourceNames::forCampaign($accountId, $googleCampaignId)
                        ]);
                        $operationIdx++;
                        $campaignOperations[$operationIdx] = new MutateOperation([
                            'campaign_criterion_operation' => new CampaignCriterionOperation(['create' => $campaignCriterion])
                        ]);
                        $targetType = $googleAdsTargetTypes[$criteriaId] ?? GoogleAdsGeoTarget::TARGET_TYPE_COUNTY;
                        $campaignOperationsInfo[$operationIdx] = [
                            'action' => 'create',
                            'target_type' => $targetType,
                            'criteria_id' => $criteriaId,
                            'campaign_id' => $googleCampaignId,
                            'account_id' => $accountId,
                            'message' => $newEnabledStatus ? "Add $targetType to inclusion list" : "Add $targetType to exclusion list",
                        ];

                        // Create log entry for county removal
                        $tieredAdsLogs[] = [
                            TieredAdvertisingLog::FIELD_PLATFORM => AdvertisingPlatform::GOOGLE->value,
                            TieredAdvertisingLog::FIELD_INDUSTRY_ID => $industryId,
                            TieredAdvertisingLog::FIELD_LOCATION_ID => $criteriaToLocationId[$criteriaId] ?? null,
                            TieredAdvertisingLog::FIELD_ACCOUNT_ID => $platformAcctIdToA2Id[$accountId] ?? null,
                            TieredAdvertisingLog::FIELD_CAMPAIGN_ID => $platformCampaignIdToA2Id[$googleCampaignId] ?? null,
                            TieredAdvertisingLog::FIELD_TYPE => TieredAdvertisingLog::TYPE_GOOGLE_LOCATION_ADD,
                            TieredAdvertisingLog::FIELD_MESSAGE => "", // No message for this, type and other columns convey all necessary information
                            TieredAdvertisingLog::FIELD_DATA => json_encode($criteriaToAssignmentData[$criteriaId] ?? []),
                            TieredAdvertisingLog::CREATED_AT => now(),
                            TieredAdvertisingLog::UPDATED_AT => null,
                        ];
                    }
                }
                $operationsCount = count($campaignOperations);

                if((count($operations[$batch]) + $operationsCount) >= self::REQUEST_MUTATE_OPERATIONS_LIMIT) {
                    $batch++;
                    $operationIdx = -1;
                    $operations[$batch] = [
                        'campaign_ids' => [],
                        'operations' => []
                    ];
                    $operationsInfo[$batch] = [];
                }

                if($operationsCount > 0) {
                    $operations[$batch]['campaign_ids'][] = $googleCampaignId;
                }
                else {
                    //Consider campaigns with no operations to be successful so we can log the run
                    $successfulCampaignIds[] = $googleCampaignId;
                }

                $operations[$batch]['operations'] = array_merge($operations[$batch]['operations'], $campaignOperations);
                $operationsInfo[$batch] = array_merge($operationsInfo[$batch], $campaignOperationsInfo);
            }
        }

        // Save tiered advertising logs
        if (count($tieredAdsLogs) > 0)
            $this->tieredAdvertisingService->saveTieredAdvertisingLogs($tieredAdsLogs);

        return [
            self::FUNC_RETURN_OPERATIONS => $operations,
            self::FUNC_RETURN_OPERATIONS_INFO => $operationsInfo,
            self::FUNC_RETURN_SUCCESSFUL_CAMPAIGN_IDS => $successfulCampaignIds
        ];
    }

    /**
     * @param array $accountCampaignLocations
     * @param array $operations
     * @param array $operationsInfo
     * @param array $successfulCampaignIds
     * @param int $logId
     * @return bool
     * @throws ApiException
     * @throws ValidationException
     */
    public function batchUpdateTieredAdvertisingLocations(
        array $accountCampaignLocations,
        array $operations,
        array $operationsInfo,
        array $successfulCampaignIds,
        int $logId,
    ): bool
    {
        try {
            $jobs = [];

            $currentTimestamp = Carbon::now()->timestamp;
            foreach(array_keys($accountCampaignLocations) as $accountId) {
                $jobs = $this->createUpdateBatchJobs($accountId, $operations);

                AdvertisingLoggingService::writeLog(
                    "Tiered Advertising Update batch jobs created",
                    [
                        "id" => $logId,
                        "platform" => AdvertisingPlatform::GOOGLE->value,
                        "account_id" => $accountId
                    ]
                );

                $this->processUpdateBatchJobResults($accountId, $jobs, $operationsInfo, $successfulCampaignIds, $currentTimestamp, $logId);

                AdvertisingLoggingService::writeLog(
                    "Processed Tiered Advertising batch jobs",
                    [
                        "id" => $logId,
                        "platform" => AdvertisingPlatform::GOOGLE->value,
                        "account_id" => $accountId,
                        "successful_campaign_ids" => $successfulCampaignIds
                    ]
                );

                AdvertisingCampaign::query()
                    ->where(AdvertisingCampaign::FIELD_PLATFORM, AdvertisingPlatform::GOOGLE->value)
                    ->where(AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                    ->whereIn(AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID, array_unique($successfulCampaignIds))
                    ->update([
                        AdvertisingCampaign::FIELD_LAST_RUN_TIMESTAMP => $currentTimestamp
                    ]);
            }

            $success = !in_array(false, data_get($jobs, '*.results.*'), true);
        }
        catch(Exception $e) {
            $errMsg = __METHOD__.": Line ".$e->getLine().". ".$e->getMessage();

            AdvertisingLoggingService::writeLog($errMsg, ["id" => $logId, "platform" => AdvertisingPlatform::GOOGLE->value]);

            logger()->error($errMsg);
            logger()->error($e->getTraceAsString());

            throw $e;
        }
        return $success;
    }

    /**
     * Verifies communication with the given account id and retrieves basic information
     * @param Advertiser $advertiser
     * @param string $accountId
     * @return ?string
     */
    public function getAccountName(Advertiser $advertiser, string $accountId): ?string
    {
        $accountId = preg_replace('/\D/', '', $accountId);

        $accountQuery = "SELECT customer_client.client_customer, customer_client.level, ".
            "customer_client.manager, customer_client.descriptive_name, ".
            "customer_client.currency_code, customer_client.time_zone, ".
            "customer_client.id FROM customer_client ".
            "WHERE customer_client.id = '$accountId'";

        try {
            $stream = $this->googleAdsServiceClient->searchStream(
                SearchGoogleAdsStreamRequest::build(config('services.google.ads.login_customer_id'), $accountQuery)
            );

            $name = null;
            foreach ($stream->iterateAllElements() as $googleAdsRow) {
                /** @var CustomerClient $customerClient */
                $customerClient = $googleAdsRow->getCustomerClient();
                $name           = $customerClient->getDescriptiveName();
            }

            return $name;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Verifies communication with the given campaign id using the account id
     * @param Advertiser $advertiser
     * @param string $accountId
     * @param string $campaignId
     * @return ?string
     */
    public function getCampaignName(Advertiser $advertiser, string $accountId, string $campaignId): ?string
    {
        $accountId = preg_replace('/\D/', '', $accountId);
        $campaignId = preg_replace('/\D/', '', $campaignId);

        $campaignQuery = "SELECT campaign.id, campaign.name, campaign.status "
            . "FROM campaign "
            . "WHERE campaign.id = $campaignId";

        try {
            $stream = $this->googleAdsServiceClient->searchStream(
                SearchGoogleAdsStreamRequest::build($accountId, $campaignQuery)
            );

            $name = null;
            foreach ($stream->iterateAllElements() as $googleAdsRow) {
                /** @var GoogleCampaign $campaign */
                $campaign = $googleAdsRow->getCampaign();
                $name = $campaign->getName();
            }

            return $name;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Used in tiered advertising, sets the tcpa bid for the given campaign
     * @param Advertiser $advertiser
     * @param string $accountId
     * @param string $campaignId
     * @param float $tcpaBid
     * @return bool
     */
    public function setTcpaBid(Advertiser $advertiser, string $accountId, string $campaignId, float $tcpaBid): bool
    {
        $accountId = preg_replace('/\D/', '', $accountId);
        $campaignId = preg_replace('/\D/', '', $campaignId);

        /** @var CampaignServiceClient $batchJobServiceClient */
        $campaignServiceClient = $this->googleAdsClient->getCampaignServiceClient();

        $tcpaBidMicros = (int)($tcpaBid * self::DOLLAR_TO_MICROS);

        // Bidding strategy is maxmimize conversions with set TCPA
        $maximizeConversions = new MaximizeConversions([
            'target_cpa_micros' => $tcpaBidMicros,
        ]);

        // Campaign object, identifier is resource_name with customers/<account id>/campaigns/<campaign_id>
        $campaign = new GoogleCampaign([
            'resource_name' => $campaignServiceClient->campaignName($accountId, $campaignId),
            'maximize_conversions' => $maximizeConversions,
        ]);

        // Campaign operation describes update to only maximize_conversions
        $campaignOperation = new CampaignOperation([
            'update' => $campaign,
            'update_mask' => FieldMasks::allSetFieldsOf($campaign),
        ]);

        // Request to update
        $mutateCampaignsRequest = new MutateCampaignsRequest([
            'customer_id' => $accountId,
            'operations' => [$campaignOperation],
        ]);

        try {
            // Send the request to mutate the campaign
            $response = $campaignServiceClient->mutateCampaigns($mutateCampaignsRequest);

            if ($response->hasPartialFailureError())
                return false;
        } catch (Exception $e) {
            $errMsg = __METHOD__.": Line ".$e->getLine().". ".$e->getMessage();

            logger()->error($errMsg);
            logger()->error($e->getTraceAsString());

            return false;
        }

        return true;
    }
}
