<?php

namespace App\Services\PhoneNumber;

use App\Enums\Odin\StateAbbreviation;
use App\Models\Odin\Consumer;
use App\Services\ContactCacheService;
use App\Services\HelperService;
use Carbon\Carbon;
use Carbon\CarbonTimeZone;

class PhoneNumberService
{
    public function __construct(
        protected ContactCacheService $contactCacheService,
    )
    {
    }

    public function recentlyContacted(
        string $phone
    ): bool
    {
        $formattedPhone = $this->formatPhoneNumber(phone: $phone);

        return $this->contactCacheService->recentlyContacted(
            contactVector: $formattedPhone
        );
    }

    public function markRecentlyContacted(
        string $phone
    ): void
    {
        $phone = $this->formatPhoneNumber(phone: $phone);

        $this->contactCacheService->markRecentlyContacted(
            contactVector: $phone,
        );
    }

    public function formatPhoneNumber(
        string $phone,
    ): ?string
    {
        return $phone; //todo
    }

    /**
     * @param Consumer $consumer
     * @return bool
     */
    public function validateConsumerPhone(
        Consumer $consumer,
    ): bool
    {
        $acceptedClassifications = [
            Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS,
            Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_CALL,
            Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_LEAD_PROCESSING,
            Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_REVALIDATION_SMS,
        ];

        return in_array($consumer->classification, $acceptedClassifications);
    }

    public function validSendingTime(
        ?string $stateAbbreviation = null,
    ): bool
    {
        $end = '21:00:00';

        if ($stateAbbreviation && StateAbbreviation::tryFrom($stateAbbreviation)) {
            $timezone = StateAbbreviation::timeZone($stateAbbreviation);
            $start = '08:00:00';
        } else {
            $timezone = StateAbbreviation::timeZone(StateAbbreviation::NY->value);
            $start = '11:00:00';
        }

        $start = Carbon::parse($start, $timezone);
        $end = Carbon::parse($end, $timezone);
        $now = Carbon::now($timezone);

        return $now->between($start, $end);
    }
}
