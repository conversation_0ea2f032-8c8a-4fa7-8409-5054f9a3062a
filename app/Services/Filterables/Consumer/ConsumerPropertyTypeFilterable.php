<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerPropertyTypeFilterable extends SelectFilterableOption
{
    protected array $options = [
        'Residential' => "1",
        'Commercial' => "2",
    ];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = 'Property Type';
        $this->id = 'consumer-property-type';
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return filled($value)
        ? $builder->whereHas(Consumer::RELATION_CONSUMER_PRODUCT, function (Builder $query) use ($value) {
            $query->whereHas(ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA, function (Builder $query) use ($value) {
                $query->whereJsonContains('payload->commercial_residential', $value);
            });
        })
        : $builder;
    }
}
