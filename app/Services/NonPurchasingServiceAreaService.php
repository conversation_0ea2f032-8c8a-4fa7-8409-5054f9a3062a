<?php

namespace App\Services;

use App\Enums\Odin\Industry;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\NonPurchasingCompanyLocationRepository;
use App\Repositories\TaskRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class NonPurchasingServiceAreaService
{
    public function __construct(
        protected TaskRepository $taskRepository,
        protected CompanyFilteringService $companyFilteringService,
        protected LocationRepository $locationRepository,
        protected NonPurchasingCompanyLocationRepository $nonPurchasingCompanyLocationRepository,
    ) { }

    public function getCompanyCountInCounty(string $countyKey, string $stateKey, string $industry): ?int
    {
        try {
            $countyLocation = $this->locationRepository->getCounty($stateKey, $countyKey);
            if ($countyLocation instanceof Location) {
                $query = $this->getCompaniesAgainstNonPurchasingLocationQuery($countyLocation, $industry, true);

                return $query->count();
            }
        } catch (ModelNotFoundException $exception) {
            throw new ModelNotFoundException("There was an issue with building the query to get non-purchasing location companies. Exception Message: {$exception->getMessage()}");
        }

        return null;
    }

    public function getCompaniesInCounty(string $countyKey, string $stateKey, string $industry, bool $filterByAccountManager,
        Request $request, ?int $pagination = null): Collection|LengthAwarePaginator
    {
        try {
            $countyLocation = $this->locationRepository->getCounty($stateKey, $countyKey);
            if ($countyLocation instanceof Location) {
                $query = $this->getCompaniesAgainstNonPurchasingLocationQuery($countyLocation, $industry, $filterByAccountManager);

                return $this->getCompaniesWithAttributes($query, $request, $pagination);
            }
        } catch (ModelNotFoundException $exception) {
            throw new ModelNotFoundException("There was an issue with building the query to get non-purchasing location companies. Exception Message: {$exception->getMessage()}");
        }

        return collect();
    }

    /**
     * @param Location $countyLocation
     * @param string $industry
     * @param bool $filterByAccountManager
     * @return Builder
     */
    public function getCompaniesAgainstNonPurchasingLocationQuery(Location $countyLocation, string $industry, bool $filterByAccountManager): Builder
    {
        $accountManagerUser = $filterByAccountManager  && Auth::user()->hasRole('account-manager')
            ? Auth::user()
            : null;

        $industryId = Industry::tryFrom($industry)?->model()?->id ?? 1;

        $nonPurchasingLocationCompanyIds = $accountManagerUser
            ? $this->nonPurchasingCompanyLocationRepository->getCompanyIdsByLocationForAccountManager($countyLocation->id, $accountManagerUser->id)
            : $this->nonPurchasingCompanyLocationRepository->getCompanyIdsByLocation($countyLocation->id);

        return Company::query()
            ->join(CompanyIndustry::TABLE, CompanyIndustry::TABLE .'.'. CompanyIndustry::FIELD_COMPANY_ID, Company::TABLE .'.'. Company::FIELD_ID)
            ->where(CompanyIndustry::FIELD_INDUSTRY_ID, $industryId)
            ->whereIn(Company::TABLE .'.'. Company::FIELD_ID, $nonPurchasingLocationCompanyIds);
    }

    private function getCompaniesWithAttributes(Builder $query, Request $request, ?int $pagination = null): Collection|LengthAwarePaginator
    {
        $requestVariables = $this->companyFilteringService->getAmountOfPurchasedLeadsFilterRequestVariables();

        return $this->companyFilteringService->appendAttributes($query, $requestVariables, null, $request, $pagination);
    }
}
