<?php

namespace App\Services;

use App\Enums\Calendar\DemoStatus;
use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\Demo;
use App\Models\Call;
use App\Models\Conference\Conference;
use App\Models\Conference\ConferenceParticipant;
use App\Models\Mailbox\MailboxEmail;
use App\Models\Mailbox\MailboxUserEmail;
use App\Models\Phone;
use App\Models\Text;
use App\Models\User;
use App\Models\UserPhone;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class SalesOverviewService
{
    protected array $sortColumnsMap = [
        'user_id'         => 'user_id',
        'user_name'       => 'user_name',
        'calls_total'     => 'calls_total',
        'calls_in'        => 'calls_in',
        'calls_out'       => 'calls_out',
        'texts_total'     => 'texts_total',
        'texts_in'        => 'texts_in',
        'texts_out'       => 'texts_out',
        'emails_total'    => 'emails_total',
        'emails_in'       => 'emails_in',
        'emails_out'      => 'emails_out',
        'completed_demos' => 'completed_demos',
        'total'           => 'total',
    ];

    // (•ᴥ• )づ temporary
    public array $relevantUserIds = [
        27, // Geoffrey Nagy
        107, // Geoffs Review Account
        31, // Kurt Peterson
        498, // Kurt Peterson 2
        35, // <PERSON> Fisher
        500, // Scott Fisher 2
        34, // Nathan Tilles
        503, // <PERSON>es 2
        29, // Julie <PERSON>
        499, // Julie Fisher 2
        492, // Sean <PERSON>
        504, // Sean Ellington 2
        26, // Garrett Free<PERSON>
        19, // Matt Marsella
        524, // Brad Schwartz
        525, // Tarina Aleamoni
        519, // Kevin Dement
        59, // Bill Berndt
        158, // Claire Lukas
        520, // Jessica Gray
        521, // Lucas Gavrila
        531, // Fallon Thoman
        515, // Monica Roach
        529, // Mac McKay
        528, // Paul Criddle
        517, // Daniel Lockard
        530, // Donte Landry
        516, // Brittany Cain
        447, // Brett Turner
        82, // Joseph Panuccio
        522, // David DuVall
        46, // Ben Thomas
        21, // Joe Dametto
        22, // Joshua Panuccio
        62, // Megan Berndt
        537, // Anthony Savedes
        538, // Andrew Lynn
        514, // Laura Nord
        37, // Andy Sendy
    ];

    public function __construct(
        protected DemoService $demoService
    )
    {

    }

    /**
     * @param QueryBuilder $builder
     * @param array $sortBy
     * @return QueryBuilder
     */
    public function sortBy(QueryBuilder $builder, array $sortBy): QueryBuilder
    {
        $fieldDirections = collect($sortBy)
            ->map(fn($item) => explode(':', $item));

        foreach ($fieldDirections as [$field, $direction]) {
            $colField = Arr::get($this->sortColumnsMap, $field);

            if (filled($colField)) {
                $builder->orderBy($colField, $direction);
            }
        }

        return $builder;
    }

    /**
     * @param string|null $startDate
     * @param string|null $endDate
     * @param int|null $userId
     * @return Builder
     */
    public function getTotalCallPerUserQuery(
        ?string $startDate = null,
        ?string $endDate = null,
        ?int $userId = null,
    ): Builder
    {
        return Call::query()
            ->select([
                DB::raw(UserPhone::TABLE . "." . UserPhone::FIELD_USER_ID . " AS user_id"),
                DB::raw("COUNT(DISTINCT CASE WHEN " . Call::TABLE . "." . Call::FIELD_DIRECTION . " = 'inbound' THEN " . Call::TABLE . "." . Call::FIELD_ID . " END) AS calls_in"),
                DB::raw("COUNT(DISTINCT CASE WHEN " . Call::TABLE . "." . Call::FIELD_DIRECTION . " = 'outbound' THEN " . Call::TABLE . "." . Call::FIELD_ID . " END) AS calls_out"),
                DB::raw("0 AS texts_in"),
                DB::raw("0 AS texts_out"),
                DB::raw("0 AS emails_in"),
                DB::raw("0 AS emails_out"),
                DB::raw("0 AS completed_demos"),
            ])
            ->from(Call::TABLE)
            ->join(UserPhone::TABLE, UserPhone::TABLE . '.' . UserPhone::FIELD_PHONE_ID, Call::TABLE . '.' . Call::FIELD_PHONE_ID)
            ->whereNull(UserPhone::TABLE . '.' . UserPhone::FIELD_DELETED_AT)
            ->when(filled($startDate), fn($query) => $query->where(Call::TABLE . '.' . Call::CREATED_AT, '>=', $startDate))
            ->when(filled($endDate), fn($query) => $query->where(Call::TABLE . '.' . Call::CREATED_AT, '<=', $endDate))
            ->when(filled($userId), fn($query) => $query->where(UserPhone::TABLE . '.' . UserPhone::FIELD_USER_ID, $userId))
            ->groupBy(UserPhone::TABLE . '.' . UserPhone::FIELD_USER_ID);
    }

    /**
     * @param string|null $startDate
     * @param string|null $endDate
     * @param int|null $userId
     * @return Builder
     */
    public function getTotalTextsPerUserQuery(
        ?string $startDate = null,
        ?string $endDate = null,
        ?int $userId = null
    ): Builder
    {
        return Text::query()
            ->select([
                DB::raw(UserPhone::TABLE . "." . UserPhone::FIELD_USER_ID . " AS user_id"),
                DB::raw("0 AS calls_in"),
                DB::raw("0 AS calls_out"),
                DB::raw("COUNT(DISTINCT CASE WHEN " . Text::TABLE . "." . Text::FIELD_DIRECTION . " = 'inbound' THEN " . Text::TABLE . "." . Text::FIELD_ID . " END) AS texts_in"),
                DB::raw("COUNT(DISTINCT CASE WHEN " . Text::TABLE . "." . Text::FIELD_DIRECTION . " = 'outbound' THEN " . Text::TABLE . "." . Text::FIELD_ID . " END) AS texts_out"),
                DB::raw("0 AS emails_in"),
                DB::raw("0 AS emails_out"),
                DB::raw("0 AS completed_demos"),
            ])
            ->from(Text::TABLE)
            ->join(UserPhone::TABLE, UserPhone::TABLE . '.' . UserPhone::FIELD_PHONE_ID, Text::TABLE . '.' . Text::FIELD_PHONE_ID)
            ->whereNull(UserPhone::TABLE . '.' . UserPhone::FIELD_DELETED_AT)
            ->when(filled($startDate), fn($query) => $query->where(Text::TABLE . '.' . Text::CREATED_AT, '>=', $startDate))
            ->when(filled($endDate), fn($query) => $query->where(Text::TABLE . '.' . Text::CREATED_AT, '<=', $endDate))
            ->when(filled($userId), fn($query) => $query->where(UserPhone::TABLE . '.' . UserPhone::FIELD_USER_ID, $userId))
            ->groupBy(UserPhone::TABLE . '.' . UserPhone::FIELD_USER_ID);
    }

    /**
     * @param string|null $startDate
     * @param string|null $endDate
     * @param int|null $userId
     * @return Builder
     */
    public function getTotalEmailsPerUserQuery(
        ?string $startDate = null,
        ?string $endDate = null,
        ?int $userId = null
    ): Builder
    {
        return MailboxUserEmail::query()
            ->select([
                DB::raw(MailboxUserEmail::TABLE . "." . MailboxUserEmail::FIELD_USER_ID . " AS user_id"),
                DB::raw("0 AS calls_in"),
                DB::raw("0 AS calls_out"),
                DB::raw("0 AS texts_in"),
                DB::raw("0 AS texts_out"),
                DB::raw("COUNT(DISTINCT CASE WHEN " . MailboxEmail::TABLE . "." . MailboxEmail::FIELD_FROM_USER_ID . " is null THEN " . MailboxUserEmail::TABLE . "." . MailboxUserEmail::FIELD_EMAIL_ID . " END) AS emails_in"),
                DB::raw("COUNT(DISTINCT CASE WHEN " . MailboxEmail::TABLE . "." . MailboxEmail::FIELD_FROM_USER_ID . " is not null THEN " . MailboxUserEmail::TABLE . "." . MailboxUserEmail::FIELD_EMAIL_ID . " END) AS emails_out"),
                DB::raw("0 AS completed_demos"),
            ])
            ->from(MailboxUserEmail::TABLE)
            ->join(MailboxEmail::TABLE, MailboxEmail::TABLE . '.' . MailboxEmail::FIELD_ID, MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_EMAIL_ID)
            ->when(filled($startDate), fn($query) => $query->where(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_SENT_AT, '>=', $startDate))
            ->when(filled($endDate), fn($query) => $query->where(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_SENT_AT, '<=', $endDate))
            ->when(filled($userId), fn($query) => $query->where(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_USER_ID, $userId))
            ->groupBy(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_USER_ID);
    }

    /**
     * @param string|null $startDate
     * @param string|null $endDate
     * @param int|null $userId
     * @return Builder
     */
    public function getTotalDemosPerUserQuery(
        ?string $startDate = null,
        ?string $endDate = null,
        ?int $userId = null
    ): Builder
    {
        return Demo::query()
            ->select([
                DB::raw(Demo::TABLE . "." . Demo::FIELD_USER_ID . " AS user_id"),
                DB::raw("0 AS calls_in"),
                DB::raw("0 AS calls_out"),
                DB::raw("0 AS texts_in"),
                DB::raw("0 AS texts_out"),
                DB::raw("0 AS emails_in"),
                DB::raw("0 AS emails_out"),
                DB::raw("COUNT(DISTINCT CASE WHEN " . Demo::TABLE . "." . Demo::FIELD_STATUS . " = '" . DemoStatus::COMPLETED->value . "' THEN " . Demo::TABLE . "." . Demo::FIELD_CALENDAR_EVENT_ID . " END) AS completed_demos"),
            ])
            ->from(Demo::TABLE)
            ->join(CalendarEvent::TABLE, CalendarEvent::TABLE . '.' . CalendarEvent::FIELD_ID, Demo::TABLE . "." . Demo::FIELD_CALENDAR_EVENT_ID)
            ->join(Conference::TABLE, Conference::TABLE . '.' . Conference::FIELD_CALENDAR_EVENT_ID, CalendarEvent::TABLE . '.' . CalendarEvent::FIELD_ID)
            ->join(ConferenceParticipant::TABLE, ConferenceParticipant::TABLE . '.' . ConferenceParticipant::FIELD_CONFERENCE_ID, Conference::TABLE . '.' . Conference::FIELD_ID)
            ->when(filled($startDate), fn($query) => $query->where(Conference::TABLE . '.' . Conference::FIELD_START_TIME, '>=', $startDate))
            ->when(filled($endDate), fn($query) => $query->where(Conference::TABLE . '.' . Conference::FIELD_END_TIME, '<=', $endDate))
            ->when(filled($userId), fn($query) => $query->where(Conference::TABLE . '.' . Conference::FIELD_USER_ID, $userId))
            ->havingRaw('COUNT(' . ConferenceParticipant::TABLE .'.' . ConferenceParticipant::FIELD_ID .') > 1')
            ->where(Conference::TABLE . '.' . Conference::FIELD_DURATION_IN_SECONDS, '>=', DemoService::TEN_MINUTES_IN_SECONDS)
            ->groupBy(Demo::TABLE . '.' . Demo::FIELD_CALENDAR_EVENT_ID)
            ->groupBy(Demo::TABLE . '.' . Demo::FIELD_USER_ID);
    }

    /**
     * @param array|null $sortBy
     * @param array|null $dateRange
     * @param int|null $userId
     * @return QueryBuilder
     */
    public function getSalesOverviewQuery(
        ?array $sortBy = [],
        ?array $dateRange = [],
        ?int $userId = null
    ): QueryBuilder
    {
        [$startDate, $endDate] = $dateRange;

        $baseQuery = $this->getTotalCallPerUserQuery(
            startDate: $startDate,
            endDate  : $endDate,
            userId   : $userId,
        )
            ->unionAll($this->getTotalTextsPerUserQuery(
                startDate: $startDate,
                endDate  : $endDate,
                userId   : $userId,
            ))
            ->unionAll($this->getTotalEmailsPerUserQuery(
                startDate: $startDate,
                endDate  : $endDate,
                userId   : $userId,
            ))
            ->unionAll($this->getTotalDemosPerUserQuery(
                startDate: $startDate,
                endDate  : $endDate,
                userId   : $userId,
            ));


        $query = DB::query()
            ->select([
                DB::raw('SUM(totals.calls_in) AS calls_in'),
                DB::raw('SUM(totals.calls_out) AS calls_out'),
                DB::raw('SUM(totals.texts_in) AS texts_in'),
                DB::raw('SUM(totals.texts_out) AS texts_out'),
                DB::raw('SUM(totals.emails_in) AS emails_in'),
                DB::raw('SUM(totals.emails_out) AS emails_out'),
                DB::raw('SUM(totals.completed_demos) AS completed_demos'),
                'totals.user_id',
                'users.name as user_name',
                DB::raw('SUM(totals.calls_in + totals.calls_out) AS calls_total'),
                DB::raw('SUM(totals.texts_in + totals.texts_out) AS texts_total'),
                DB::raw('SUM(totals.emails_in + totals.emails_out) AS emails_total'),
                DB::raw('SUM(totals.calls_in + totals.calls_out + totals.texts_in + totals.texts_out + totals.emails_in + totals.emails_out) AS total'),
            ])
            ->fromSub($baseQuery, 'totals')
            ->join(User::TABLE, User::TABLE . '.' . User::FIELD_ID, '=', 'totals.user_id')
            ->whereNull(User::TABLE . '.' . User::FIELD_DELETED_AT)
            ->whereIn(User::TABLE . '.' . User::FIELD_ID, $this->relevantUserIds)
            ->groupBy('totals.user_id', 'users.name');

        if (empty($sortBy)) {
            $this->sortByElements(
                query    : $query,
                fieldName: 'users.id',
                values   : $this->relevantUserIds
            );
        } else {
            $this->sortBy($query, $sortBy);
        }

        return $query;
    }

    /**
     * @param $query
     * @param string $fieldName
     * @param array $values
     * @return QueryBuilder
     */
    public function sortByElements($query, string $fieldName, array $values): QueryBuilder
    {
        if (empty($values)) {
            return $query;
        }

        $cases = [];
        $position = 1;

        foreach ($values as $value) {
            $escapedValue = str_replace("'", "''", $value);
            $cases[] = "WHEN '{$escapedValue}' THEN {$position}";
            $position++;
        }

        $caseStatements = implode("\n    ", $cases);
        $elseValue = $position; // Any unmatched values get the lowest priority

        $raw = "CASE {$fieldName}\n    {$caseStatements}\n    ELSE {$elseValue}\nEND";

        return $query->orderByRaw($raw);
    }

    /**
     * @param int $userId
     * @param array $dateRange
     * @param string $direction
     *
     * @return Builder
     */
    public function getQueryForCalls(int $userId, array $dateRange, string $direction): Builder
    {
        [$startDate, $endDate] = $dateRange;

        return Call::query()
            ->selectRaw(Call::TABLE . '.*')
            ->join(Phone::TABLE, Phone::TABLE . '.' . Phone::FIELD_ID, Call::TABLE . '.' . Call::FIELD_PHONE_ID)
            ->join(UserPhone::TABLE, UserPhone::TABLE . '.' . UserPhone::FIELD_PHONE_ID, Phone::TABLE . '.' . Phone::FIELD_ID)
            ->where(UserPhone::TABLE . '.' . UserPhone::FIELD_USER_ID, $userId)
            ->where(Call::TABLE . '.' . Call::FIELD_DIRECTION, $direction)
            ->whereNull(UserPhone::TABLE . '.' . UserPhone::FIELD_DELETED_AT)
            ->when(!empty($startDate), fn($query) => $query->where(Call::TABLE . '.' . Call::CREATED_AT, '>=', $startDate))
            ->when(!empty($endDate), fn($query) => $query->where(Call::TABLE . '.' . Call::CREATED_AT, '<=', $endDate))
            ->groupBy(Call::TABLE . '.' . Call::FIELD_ID);
    }

    /**
     * @param int $userId
     * @param array $dateRange
     * @param string $direction
     *
     * @return Builder
     */
    public function getQueryForTexts(int $userId, array $dateRange, string $direction): Builder
    {
        [$startDate, $endDate] = $dateRange;

        return Text::query()
            ->selectRaw(Text::TABLE . '.*')
            ->join(Phone::TABLE, Phone::TABLE . '.' . Phone::FIELD_ID, Text::TABLE . '.' . Text::FIELD_PHONE_ID)
            ->join(UserPhone::TABLE, UserPhone::TABLE . '.' . UserPhone::FIELD_PHONE_ID, Phone::TABLE . '.' . Phone::FIELD_ID)
            ->where(UserPhone::TABLE . '.' . UserPhone::FIELD_USER_ID, $userId)
            ->whereNull(UserPhone::TABLE . '.' . UserPhone::FIELD_DELETED_AT)
            ->where(Text::TABLE . '.' . Text::FIELD_DIRECTION, $direction)
            ->when(!empty($startDate), fn($query) => $query->where(Text::TABLE . '.' . Text::CREATED_AT, '>=', $startDate))
            ->when(!empty($endDate), fn($query) => $query->where(Text::TABLE . '.' . Text::CREATED_AT, '<=', $endDate))
            ->groupBy(Text::TABLE . '.' . Text::FIELD_ID);
    }

    /**
     * @param int $userId
     * @param array $dateRange
     * @param string $direction
     *
     * @return Builder
     */
    public function getQueryForEmails(int $userId, array $dateRange, string $direction): Builder
    {
        [$startDate, $endDate] = $dateRange;

        $query = MailboxEmail::query()
            ->selectRaw(MailboxEmail::TABLE . '.*')
            ->join(MailboxUserEmail::TABLE, MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_EMAIL_ID, MailboxEmail::TABLE . '.' . MailboxEmail::FIELD_ID)
            ->where(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_USER_ID, $userId)
            ->when(filled($startDate), fn($query) => $query->where(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_SENT_AT, '>=', $startDate))
            ->when(filled($endDate), fn($query) => $query->where(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_SENT_AT, '<=', $endDate))
            ->groupBy(MailboxEmail::TABLE . '.' . MailboxEmail::FIELD_ID)
            ->orderByDesc(MailboxEmail::TABLE . '.' . MailboxEmail::FIELD_SENT_AT);

        match ($direction) {
            'outbound' => $query->whereNotNull(MailboxEmail::TABLE . '.' . MailboxEmail::FIELD_FROM_USER_ID),
            'inbound'  => $query->whereNull(MailboxEmail::TABLE . '.' . MailboxEmail::FIELD_FROM_USER_ID),
        };

        return $query;
    }

    /**
     * @param int $userId
     * @param array $dateRange
     * @return Builder
     */
    public function getDemos(
        int $userId,
        array $dateRange
    ): Builder
    {
        [$startDate, $endDate] = $dateRange;

        return $this->demoService->getListDemosQuery(
            userId   : $userId,
            startDate: $startDate,
            endDate  : $endDate
        );
    }
}
