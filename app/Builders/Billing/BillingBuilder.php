<?php

namespace App\Builders\Billing;

use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Models\User;
use App\Repositories\TeamRepository;
use App\Services\DatabaseHelperService;
use Closure;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use \Illuminate\Database\Query\Builder as QueryBuilder;

abstract class BillingBuilder
{
    protected Collection $joins;
    protected array $sortColumnsMap = [];
    protected array $roles          = [
        'account_manager'              => 'account-manager',
        'business_development_manager' => 'business-development-manager',
        'onboarding_manager'           => 'onboarding-manager',
    ];


    /**
     * @param Builder|QueryBuilder $query
     */
    public function __construct(protected Builder|QueryBuilder $query)
    {
        $this->joins = new Collection();
    }

    /**
     * @param array $fields
     * @param bool $add
     * @return $this
     */
    public function select(array $fields, bool $add = true): static
    {
        if ($add) {
            foreach ($fields as $field) {
                $this->query->addSelect($field);
            }
        } else {
            $this->select($fields);
        }


        return $this;
    }

    /**
     * @param array|null $sortBy
     * @return $this
     */
    public function sortBy(?array $sortBy = null): self
    {
        $sortBy = $sortBy ?: [];

        $fieldDirections = collect($sortBy)
            ->map(fn($item) => explode(':', $item));

        foreach ($fieldDirections as [$field, $direction]) {
            $colField = Arr::get($this->sortColumnsMap, $field);

            if (filled($colField)) {
                $this->query->orderBy($colField, $direction);
            }
        }

        return $this;
    }

    /**
     * @return self
     */
    abstract public static function query(): self;

    /**
     * @param string|null $relation
     * @param Closure|null $callback
     * @return $this
     */
    public function with(?string $relation = null, ?Closure $callback = null): static
    {
        if (filled($relation)) {
            $this->query->with([$relation => function ($query) use ($callback) {
                if (filled($callback)) {
                    $callback($query);
                }
            }]);
        }

        return $this;
    }


    /**
     * @param int|null $companyId
     * @return $this
     */
    public function forCompanyId(?int $companyId = null): self
    {
        if (filled($companyId)) {
            $this->query->where(DatabaseHelperService::database() . '.' . Company::TABLE . '.' . Company::FIELD_ID, $companyId);
        }
        return $this;
    }

    /**
     * @return $this
     */
    public function joinCompany(): self
    {
        $this->safeJoin('company', fn() => $this->query->leftJoin(
            DatabaseHelperService::database() . '.' . Company::TABLE,
            Company::TABLE . '.' . Company::FIELD_ID,
            Invoice::TABLE . '.' . Invoice::FIELD_COMPANY_ID,
        ));

        return $this;
    }

    /**
     * @param string $name
     * @param Closure $callback
     * @return void
     */
    public function safeJoin(string $name, Closure $callback): void
    {
        if (!$this->joins->has($name)) {
            $callback();

            $this->joins->put($name, true);
        }
    }

    /**
     * @return $this
     */
    public function joinCompanyUserRelationships(): self
    {
        $this->joinCompany();

        foreach ($this->roles as $key => $role) {
            $tableAlias = "company_$key";
            $userTableAlias = $tableAlias . "_user";

            $this->safeJoin($tableAlias, function () use ($tableAlias, $userTableAlias, $role, $key) {
                $latestManagerTableAlias = "latest_active_$key";

                $this->query->leftJoinSub(
                    CompanyUserRelationship::query()
                        ->selectRaw('MAX(id) as id, company_id')
                        ->groupBy([
                            CompanyUserRelationship::FIELD_COMPANY_ID,
                        ])
                        ->whereNull(CompanyUserRelationship::FIELD_DELETED_AT)
                        ->whereHas('role', fn (Builder $query) => $query->where('name', $role)),
                    $latestManagerTableAlias,
                    $latestManagerTableAlias . '.company_id',
                    '=',
                    Company::TABLE . '.' . Company::FIELD_ID
                );

                $this->query->leftJoin(
                    CompanyUserRelationship::TABLE . ' as ' . $tableAlias,
                    $tableAlias .'.' . CompanyUserRelationship::FIELD_ID,
                    $latestManagerTableAlias . '.id',
                );

                $this->query->leftJoin(User::TABLE . " as $userTableAlias", $tableAlias . '.user_id', $userTableAlias . '.id');

                $tableAlias = "company_$key";
                $userTableAlias = $tableAlias . "_user";

                $this->select([
                    $tableAlias . ".id as " . $tableAlias . "_id",
                    $userTableAlias . ".id as " . $userTableAlias . "_id",
                    $userTableAlias . ".name as " . $userTableAlias . "_name",
                ]);
            });
        }

        return $this;
    }

    /**
     * @param User|null $user
     * @return $this
     */
    public function filterByRole(?User $user = null): self
    {
        $userIsFinanceController = $user?->hasRole('finance-controller');

        if ($userIsFinanceController) {
            $teamMembers = app(TeamRepository::class)->getTeamMembersForUser($user)->pluck(User::FIELD_ID);

            $this->joinCompanyUserRelationships();

            $this->query->where(function ($query) use ($user, $teamMembers) {
                foreach ($this->roles as $key => $role) {
                    $tableAlias = "company_$key";
                    $userTableAlias = $tableAlias . "_user";

                    $query->orWhereIn($userTableAlias . ".id", $teamMembers->isNotEmpty() ? $teamMembers->toArray() : [$user->id]);
                }
            });
        }

        return $this;
    }

    /**
     * @param array $filters
     * @return $this
     */
    public function applyFilters(array $filters): self
    {
        return $this;
    }


    /**
     * @param string $grouping
     * @return $this
     */
    public function groupBy(string $grouping): self
    {
        $column = match ($grouping) {
            'invoice'                      => InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID,
            'company'                      => InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_COMPANY_ID,
            'onboarding_manager'           => "company_onboarding_manager_user.id",
            'account_manager'              => "company_account_manager_user.id",
            'business_development_manager' => "company_business_development_manager_user.id",
            default                        => null
        };

        $this->query->groupByRaw($column);

        return $this;
    }

    /**
     * @return Builder|QueryBuilder
     */
    public function getQuery(): Builder|QueryBuilder
    {
        return $this->query;
    }

    /**
     * @param int|null $onboardingManagerUserId
     * @param int|null $accountManagerUserId
     * @param int|null $businessDevelopmentManagerUserId
     * @return $this
     */
    public function forCompanyUserRelationship(
        ?int $onboardingManagerUserId = null,
        ?int $accountManagerUserId = null,
        ?int $businessDevelopmentManagerUserId = null,
    ): self
    {
        $whereClauses = collect();

        if ($onboardingManagerUserId) {
            $whereClauses->add([
                'type'    => 'onboarding_manager',
                'user_id' => $onboardingManagerUserId
            ]);
        }

        if ($accountManagerUserId) {
            $whereClauses->add([
                'type'    => 'account_manager',
                'user_id' => $accountManagerUserId
            ]);
        }

        if ($businessDevelopmentManagerUserId) {
            $whereClauses->add([
                'type'    => 'business_development_manager',
                'user_id' => $businessDevelopmentManagerUserId
            ]);
        }

        if ($whereClauses->isNotEmpty()) {
            $this->joinCompanyUserRelationships();

            $this->query->where(function ($query) use ($whereClauses) {
                foreach ($whereClauses as $clause) {
                    $query->where(
                        "company_" . $clause['type'] . "_user" . ".id",
                        $clause['user_id']
                    );
                }
            });
        }

        return $this;
    }
}
