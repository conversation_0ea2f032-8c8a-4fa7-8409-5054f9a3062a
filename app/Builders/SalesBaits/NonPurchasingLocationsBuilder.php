<?php

namespace App\Builders\SalesBaits;

use App\Builders\SalesBaits\Concerns\SalesBaitBuilderGenericQueries;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\Location;
use App\Models\Odin\NonPurchasingCompanyLocation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class NonPurchasingLocationsBuilder
{
    use SalesBaitBuilderGenericQueries;

    const DEFAULT_SALES_BAIT_STATUSES = [EloquentCompany::STATUS_INACTIVE, EloquentCompany::STATUS_PENDING, EloquentCompany::STATUS_PRESALES, EloquentCompany::STATUS_REGISTERING];

    public function __construct(
        protected ?EloquentQuote $lead = null,
        protected array          $statuses = self::DEFAULT_SALES_BAIT_STATUSES,
        protected bool           $includePausedActiveBuyers = true,
        protected bool           $excludeCompaniesWithCampaigns = true,
        protected bool           $excludeRestricted = true
    ) {}

    /**
     * Returns a new instance of the non purchasing locations builder.
     *
     * @param EloquentQuote|null $lead
     * @param array $statuses
     * @param bool $includePausedActiveBuyers
     * @param bool $excludeRestricted
     * @return NonPurchasingLocationsBuilder
     */
    public static function query(
        ?EloquentQuote $lead = null,
        array          $statuses = self::DEFAULT_SALES_BAIT_STATUSES,
        bool           $includePausedActiveBuyers = true,
        bool           $excludeCompaniesWithCampaigns = true,
        bool           $excludeRestricted = true
    ): NonPurchasingLocationsBuilder
    {
        return new self($lead, $statuses, $includePausedActiveBuyers, $excludeCompaniesWithCampaigns, $excludeRestricted);
    }

    /**
     * Sets the lead that this builder should use for calculations
     *
     * @param EloquentQuote|null $lead
     * @return $this
     */
    public function forLead(?EloquentQuote $lead): NonPurchasingLocationsBuilder
    {
        $this->lead = $lead;

        return $this;
    }

    /**
     * Defines whether this builder should exclude companies that have been restricted from receiving sales baits.
     *
     * @param bool $exclude
     * @return $this
     */
    public function excludeRestrictedCompanies(bool $exclude = true): NonPurchasingLocationsBuilder
    {
        $this->excludeRestricted = $exclude;

        return $this;
    }

    /**
     * Defines the statuses of companies that this builder should use.
     *
     * @param array $statuses
     * @return $this
     */
    public function forCompanyStatus(array $statuses = self::DEFAULT_SALES_BAIT_STATUSES): NonPurchasingLocationsBuilder
    {
        $this->statuses = $statuses;

        return $this;
    }

    /**
     * Defines whether this builder should include companies that are active, but have paused campaigns.
     *
     * @param bool $include
     * @return $this
     */
    public function includeActiveBuyersWithPausedCampaigns(bool $include = true): NonPurchasingLocationsBuilder
    {
        $this->includePausedActiveBuyers = $include;

        return $this;
    }

    /**
     * Defines whether this builder should exclude companies that have campaigns and only use ones with NPLs.
     * ]
     * @param bool $exclude
     * @return $this
     */
    public function excludeCompaniesThatHaveCampaigns(bool $exclude = true): NonPurchasingLocationsBuilder
    {
        $this->excludeCompaniesWithCampaigns = $exclude;

        return $this;
    }

    /**
     * Returns the companies for this builder.
     *
     * @return Collection<EloquentCompany>
     */
    public function get(): Collection
    {
        if ($this->lead == null)
            throw new \RuntimeException("SalesBait Non Purchasing Builder expected a lead. See NonPurchasingLocationsBuilder::setLead");

        if ($this->lead->address === null || strlen(trim($this->lead->address->zipcode)) === 0)
            throw new \RuntimeException("Lead provided didn't include a valid address and/or zip code.");

        return $this->getQuery()->get();
    }

    /**
     * Returns the base query builder for this builder.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $companyQuery = EloquentCompany::query()->select(DB::raw(EloquentCompany::TABLE . '.*'));
        $companyQuery = $this->applySharedCompanyMappings($companyQuery);

        /** @var Location $location */
        $location = Location::query()
            ->where(Location::ZIP_CODE, $this->lead->address->zipcode)
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->firstOrFail();

        $companyQuery->join(
            NonPurchasingCompanyLocation::TABLE,
            NonPurchasingCompanyLocation::TABLE . '.' . NonPurchasingCompanyLocation::FIELD_COMPANY_ID,
            '=',
            EloquentCompany::TABLE . '.' . EloquentCompany::ID
        )->join(
            Location::TABLE,
            Location::TABLE . '.' . Location::ID,
            '=',
            NonPurchasingCompanyLocation::TABLE . '.' . NonPurchasingCompanyLocation::FIELD_LOCATION_ID
        )->where(Location::TABLE . '.' . Location::CITY_KEY, $location->city_key)
            ->groupBy(EloquentCompany::COMPANY_ID);

        if ($this->excludeCompaniesWithCampaigns) {
            $companyQuery->whereDoesntHave(EloquentCompany::RELATION_CAMPAIGNS);
        }

        return $companyQuery;
    }
}
