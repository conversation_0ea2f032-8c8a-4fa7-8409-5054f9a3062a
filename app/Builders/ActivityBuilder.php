<?php

namespace App\Builders;

use App\Enums\ActivityType;
use App\Models\Action;
use App\Models\ActivityFeed;
use App\Models\Call;
use App\Models\Email;
use App\Models\Mailbox\MailboxEmail;
use App\Models\Mailbox\MailboxEmailRecipient;
use App\Models\Sales\Task;
use App\Models\Text;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class ActivityBuilder
{
    /**
     * @param int|null $companyId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param int|null $userId
     * @param ActivityType|null $type
     * @param string|null $searchQuery
     * @param string|null $sortByColumn
     * @param string|null $sortDirection
     */
    public function __construct(
        protected ?int          $companyId = null,
        protected ?Carbon       $startDate = null,
        protected ?Carbon       $endDate = null,
        protected ?int          $userId = null,
        protected ?ActivityType $type = null,
        protected ?string       $searchQuery = null,
        protected ?string       $sortByColumn = null,
        protected ?string       $sortDirection = 'desc',
        protected bool          $includeCadenceActivity = true,
        protected ?array        $relations = [],
    )
    {
    }

    /**
     * Handles applying company filter for querying activities.
     *
     * @param int|null $companyId
     * @return $this
     */
    public function setCompanyId(?int $companyId = null): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    /**
     * Handles applying date filter for querying activities on/after the given date.
     *
     * @param Carbon|null $date
     * @return $this
     */
    public function setPeriodStartingDate(?Carbon $date = null): self
    {
        $this->startDate = $date;

        return $this;
    }

    /**
     * Handles applying date filter for querying activities on/before the given date.
     *
     * @param Carbon|null $date
     * @return $this
     */
    public function setPeriodEndingDate(?Carbon $date = null): self
    {
        $this->endDate = $date;

        return $this;
    }

    /**
     * Handles applying user filter for querying activities.
     *
     * @param int|null $userId
     * @return $this
     */
    public function setUserId(?int $userId = null): self
    {
        $this->userId = $userId;

        return $this;
    }

    /**
     * Handles applying activity type filter for querying data.
     *
     * @param ActivityType|null $type
     * @return $this
     */
    public function setActivityType(?ActivityType $type = null): self
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Handles applying filter to search keyword(s) for querying data.
     *
     * @param string|null $query
     * @return $this
     */
    public function setSearchQuery(?string $query = null): self
    {
        $this->searchQuery = $query;

        return $this;
    }

    /**
     * Handles the sort order of the resulting query.
     *
     * @param string|null $column
     * @param string|null $direction
     * @return $this
     */
    public function sortBy(?string $column = null, ?string $direction = 'desc'): self
    {
        $this->sortByColumn = $column;
        $this->sortDirection = $direction;

        return $this;
    }

    /**
     * @param bool $includeCadenceActivity
     * @return self
     */
    public function includeCadenceActivity(bool $includeCadenceActivity): self
    {
        $this->includeCadenceActivity = $includeCadenceActivity;
        return $this;
    }

    public function withRelations(?array $relations = []): self
    {
        $this->relations = $relations;
        return $this;
    }

    /**
     * Runs the query builder, and returns a list of tasks.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()
            ->get();
    }

    /**
     * Returns the query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = ActivityFeed::query();

        $query->withCount(ActivityFeed::RELATION_CONVERSATIONS);

        if (is_numeric($this->companyId) && $this->companyId > 0)
            $query->where(ActivityFeed::FIELD_COMPANY_ID, $this->companyId);

        if (is_numeric($this->userId) && $this->userId > 0)
            $query->where(ActivityFeed::FIELD_USER_ID, $this->userId);

        if ($this->searchQuery !== null && strlen(trim($this->searchQuery)) > 0) {
            $query->where(function (Builder $query) {
                return $this->appendSearchQueryToBuilder($this->searchQuery, $query);
            });
        }

        if ($this->type !== null && in_array($this->type->value, ActivityType::allTypes())) {
            $query->whereHasMorph(
                ActivityFeed::RELATION_ACTIVITY_ITEM,
                [$this->type->value]
            );
            $this->appendMorphRelationshipData($query, $this->type);

        } else if ($this->type === null) {
            $this->appendMorphRelationshipData($query, null);
        }

        if ($this->startDate instanceof Carbon) {
            $query->whereDate(ActivityFeed::FIELD_CREATED_AT, '>=', $this->startDate);

            if (gettype($this->sortByColumn) === 'string')
                $query->orderBy($this->sortByColumn, $this->sortDirection ?? 'DESC');
        } else {
            $query->latest();
        }

        if ($this->endDate instanceof Carbon)
            $query->whereDate(ActivityFeed::FIELD_CREATED_AT, '<=', $this->endDate);

        if (!$this->includeCadenceActivity)
            $query->whereNull(ActivityFeed::FIELD_COMPANY_CADENCE_GROUP_ACTION_ID);

        $query->when($this->relations, fn($query) => $query->with($this->relations));

        $query->whereNull(ActivityFeed::FIELD_DELETED_AT);

        return $query;
    }

    /**
     * Creates an instance of activity builder.
     *
     * @return ActivityBuilder
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param string $searchQuery
     * @param Builder $query
     * @return Builder
     */
    protected function appendSearchQueryToBuilder(string $searchQuery, Builder $query): Builder
    {
        $query->whereHasMorph(ActivityFeed::RELATION_ACTIVITY_ITEM, ActivityType::TASK->value, function (Builder $query
        ) use ($searchQuery) {
            return $query->where(Task::FIELD_SUBJECT, 'LIKE', '%' . $searchQuery . '%');
        });

        $query->orWhereHasMorph(ActivityFeed::RELATION_ACTIVITY_ITEM, ActivityType::CALL->value, function (
            Builder $query
        ) use ($searchQuery) {
            return $query
                ->where(Call::FIELD_RESULT, 'LIKE', '%' . $searchQuery . '%')
                ->orWhere(Call::FIELD_OTHER_NUMBER, 'LIKE', '%' . $searchQuery . '%');
        });

        $query->orWhereHasMorph(ActivityFeed::RELATION_ACTIVITY_ITEM, ActivityType::TEXT->value, function (
            Builder $query
        ) use ($searchQuery) {
            return $query
                ->where(Text::FIELD_MESSAGE_BODY, 'LIKE', '%' . $searchQuery . '%')
                ->orWhere(Text::FIELD_OTHER_NUMBER, 'LIKE', '%' . $searchQuery . '%');
        });

        $query->orWhereHasMorph(ActivityFeed::RELATION_ACTIVITY_ITEM, ActivityType::ACTION->value, function (
            Builder $query
        ) use ($searchQuery) {
            return $query
                ->where(Action::FIELD_SUBJECT, 'LIKE', '%' . $searchQuery . '%')
                ->orWhere(Action::FIELD_MESSAGE, 'LIKE', '%' . $searchQuery . '%');
        });

        $query->orWhereHasMorph(ActivityFeed::RELATION_ACTIVITY_ITEM, ActivityType::MAILBOX_EMAIL->value, function (
            Builder $query
        ) use ($searchQuery) {
            return $query
                ->where(MailboxEmail::FIELD_SUBJECT, 'LIKE', '%' . $searchQuery . '%')
                ->orWhere(MailboxEmail::FIELD_CONTENT, 'LIKE', '%' . $searchQuery . '%');
        });

        return $query;
    }

    /**
     * Fetch any required columns from Activity item_types' relationships
     *
     * @param Builder $query
     * @param ActivityType|null $type
     * @return void
     */
    protected function appendMorphRelationshipData(Builder $query, ?ActivityType $type): void
    {
        $itemTypeRelationships = [
            ActivityType::ACTION->value        =>
                [Action::class => ['category:id,name']],
            ActivityType::TEXT->value          =>
                [Text::class => ['phone:id,phone']],
            ActivityType::EMAIL->value         =>
                [Email::class => []], // todo
            ActivityType::MAILBOX_EMAIL->value =>
                [MailboxEmail::class => [
                    MailboxEmail::RELATION_RECIPIENTS . '.' . MailboxEmailRecipient::RELATION_IDENTIFIED_CONTACT,
                    MailboxEmail::RELATION_FROM_IDENTIFIED_CONTACT,
                    MailboxEmail::RELATION_ATTACHMENTS
                ]], // todo
            ActivityType::CALL->value          =>
                [Call::class => ['callRecording:id,recording_link,call_id']],
            ActivityType::TASK->value          =>
                [Task::class => ['assignedUser:id,name,email', 'taskCategory:id,name', 'taskType:id,name', 'taskNotes' => fn(
                    HasMany $query
                ) => $query->with(['user:id,name'])]],
        ];

        $requiredRelationships = !$type
            ? array_reduce($itemTypeRelationships, 'array_merge', array())
            : $itemTypeRelationships[$type->value];

        $query->with('item', fn(MorphTo $morphTo) => $morphTo
            ->morphWith($requiredRelationships)
        );
    }

    /**
     * Runs the query builder, and return the count of the query
     *
     * @return int
     */
    public function count(): int
    {
        return $this->getQuery()
            ->count();
    }

    /**
     * Returns a paginated list of this builder.
     *
     * @param int $items
     * @param array $columns
     * @param string $pageName
     * @param int|null $page
     * @return LengthAwarePaginator
     */
    public function paginate(
        int    $items,
        array  $columns = ['*'],
        string $pageName = 'page',
        ?int   $page = null
    ): LengthAwarePaginator
    {
        return $this->getQuery()
            ->paginate($items, $columns, $pageName, $page);
    }
}
