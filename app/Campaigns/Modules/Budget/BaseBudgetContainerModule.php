<?php

namespace App\Campaigns\Modules\Budget;

use App\Campaigns\Modules\BaseModule;
use App\Contracts\Campaigns\HasFrontendComponent;
use App\DataModels\Campaigns\ConsumerProject;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\Modules\Budget\ContainerType;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Repositories\Campaigns\Modules\Budget\BudgetContainerRepository;
use App\Repositories\Campaigns\Modules\Budget\BudgetRepository;
use App\Services\Campaigns\Modules\Budget\BudgetUsageService;
use Carbon\Carbon;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
abstract class BaseBudgetContainerModule extends BaseModule implements HasFrontendComponent
{
    const PAYLOAD_PARENT_KEY = 'budget';
    const PAYLOAD_BUDGETS    = 'budgets';
    const PAYLOAD_REQUIRED   = 'required';
    const PAYLOAD_OPTIONAL   = "optional";

    /**
     * This is where you specify the available budget types (e.g. verified, unverified, email only)
     * as well as there initial property values (e.g. name, enabled status, type, initial budget value, etc..)
     *
     * @return array
     */
    protected abstract static function getBudgetSchemasWithDefaults(): array;

    /**
     * Return a front-end friendly configuration of the product's budgets
     *
     * @return array
     */
    public abstract static function getBudgetConfigurations(): array;

    /**
     * This must be unique to each container class in order to allow for multiple budget types
     * e.g. LeadBudgetContainer and AppointmentBudgetContainer
     *
     * @return ContainerType
     */
    protected abstract static function getContainerType(): ContainerType;

    /** @inheritDoc */
    public function getModel(CompanyCampaign $campaign): BudgetContainer
    {
        /** @var BudgetContainerRepository $repository */
        $repository = app(BudgetContainerRepository::class);
        return $repository->firstOrInitializeByCampaignId($campaign->id, static::getContainerType(), static::getBudgetSchemasWithDefaults());
    }

    /** @inheritDoc */
    public function save(CompanyCampaign $campaign, Collection $payload): bool
    {
        if (!empty(($payload[self::PAYLOAD_PARENT_KEY][self::PAYLOAD_BUDGETS]))) {
            $budgets = $this->getModel($campaign)?->budgets;

            /** @var BudgetRepository $repository */
            $repository = app(BudgetRepository::class);

            foreach ($payload[self::PAYLOAD_PARENT_KEY][self::PAYLOAD_BUDGETS] as $budgetParams) {
                $consolidatedBudgetParams = [
                    ...$budgetParams,
                    Budget::FIELD_LAST_MODIFIED_BY_COMPANY_USER_ID => $payload['saved_by_company_user_id'] ?? null
                ];

                $budget = $budgets->where(Budget::FIELD_KEY, $consolidatedBudgetParams[Budget::FIELD_KEY])->first();
                if ($budget) {
                    $repository->updateBudget($budget, $consolidatedBudgetParams);
                }
            }
        }

        return true;
    }

    /**
     * @inheritDoc
     */
    public function statusChange(CompanyCampaign $campaign, CampaignStatus $old, CampaignStatus $new): bool
    {
        if ($old !== $new && $new === CampaignStatus::ACTIVE) {
            $utc = app(BudgetRepository::class)->getCampaignUTCOffset($campaign);

            $campaign->budgetContainer->budgets()->update([
                Budget::FIELD_LAST_MODIFIED_AT => Carbon::now(),
                Budget::FIELD_LAST_MODIFIED_UTC => $utc
            ]);
        }

        return true;
    }

    /** @inheritDoc */
    public function filter(CompanyCampaign $campaign, ConsumerProject $project): bool
    {
        $budgetContainer = $this->getModel($campaign);
        $activeBudgets   = $budgetContainer->budgets->where(Budget::FIELD_STATUS, Budget::STATUS_ENABLED);

        if ($project->unverifiedSaleOnly()) {
            $activeBudgets = $activeBudgets->where(Budget::FIELD_PRODUCT_CONFIGURATION, BudgetProductConfigurationEnum::LEAD_UNVERIFIED);
        }

        if ($activeBudgets->count() === 0)
            return false; // campaign has no active budgets

        /** @var BudgetUsageService $budgetUsageService */
        $budgetUsageService = app(BudgetUsageService::class);

        foreach ($activeBudgets as $budget) {
            if (!$budgetUsageService->isBudgetExceeded($budget))
                return true;
        }

        return false; // all active budgets have been exceeded
    }

    protected array $standardFrontendBudgetKeys = [
        Budget::FIELD_TYPE   => [
            self::INPUT_KEY_VALIDATION => "numeric|min:0|max:2",
        ],
        Budget::FIELD_STATUS => [
            self::INPUT_KEY_VALIDATION => 'boolean',
        ],
        Budget::FIELD_VALUE  => [
            self::INPUT_KEY_VALIDATION => 'numeric|min:0',
        ],
        Budget::FIELD_KEY  => [
            self::INPUT_KEY_VALIDATION => 'string',
        ],
    ];
}
