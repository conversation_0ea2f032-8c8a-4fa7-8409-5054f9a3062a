<?php

namespace App\Enums;


/**
 * This file will act as the truth for permissions
 *
 * Use a CRUD structure for new permissions
 * eg. PAGE_CHILDREN_EDIT   = page-url-or-class/optional-children/edit
 *     PAGE_CHILDREN_DELETE = page-url-or-class/optional-children/delete
 */
enum PermissionType: string
{
    case DASHBOARD                                 = 'dashboard';
    case ADMIN                                     = 'admin';
    case LEAD_PROCESSING                           = 'lead-processing';
    case LEAD_PROCESSING_MANAGEMENT                = 'lead-processing-management';
    case INDUSTRY_CONFIGURATION                    = 'industry-configuration';
    case EMAIL_TEMPLATE                            = 'email-templates';
    case COMPANY                                   = 'companies';
    case COMPANY_CONFIGURE                         = 'company/configure';
    case COMPANY_UPDATE_INDUSTRIES                 = 'company/update-industries';
    case COMPANY_MERGE                             = 'company/merge';
    case ADVERTISING                               = 'advertising';
    case ADVERTISING_ADMIN                         = 'advertising-admin';
    case TASK                                      = 'tasks';
    case CMS                                       = 'cms';
    case TEAM_MANAGEMENT                           = 'team-management';
    case MANAGE_BUNDLE                             = 'manage-bundles';
    case MANAGE_BUNDLE_INVOICE                     = 'manage-bundle-invoices';
    case VIEW_BUNDLE                               = 'view-bundles';
    case FLOW_MANAGEMENT                           = 'flow-management';
    case FLOW_MANAGEMENT_DELETE                    = 'flow-management/delete';
    case COMPANY_QUALITY_SCORE_MANAGEMENT          = 'company-quality-score-management';
    case OPPORTUNITY_NOTIFICATION                  = 'opportunity-notifications';
    case RULESET_MANAGEMENT                        = 'ruleset-management';
    case SILO_MANAGEMENT_VIEW                      = 'silo-management/view';
    case SILO_MANAGEMENT_CREATE                    = 'silo-management/create';
    case SILO_MANAGEMENT_UPDATE                    = 'silo-management/update';
    case SILO_MANAGEMENT_DELETE                    = 'silo-management/delete';
    case PERMISSION_MANAGEMENT_VIEW                = 'permission-management/view';
    case PERMISSION_MANAGEMENT_EDIT                = 'permission-management/edit';
    case CONSUMER_PRODUCT_VIEW                     = 'consumer-product/view';
    case CONSUMER_PRODUCT_VIEW_LONGER_THAN_90_DAYS = 'consumer-product/view-leads-longer-than-90-days';
    case COMPANY_LEAD_UPDATE_CHARGEABLE_STATUS     = 'companies/leads/update-chargeable-status';
    case COMPANY_EXPORT_DATA                       = 'company/export-data';
    case SALES_MANAGEMENT                          = 'sales-management';
    case EXPERT_REVIEWS_VIEW                       = 'expert-reviews/view';
    case EXPERT_REVIEWS_CREATE                     = 'expert-reviews/create';
    case EXPERT_REVIEWS_UPDATE                     = 'expert-reviews/update';
    case EXPERT_REVIEWS_DELETE                     = 'expert-reviews/delete';
    case USER_MANAGEMENT                           = 'user-management';
    case PERMISSION_TEST_LEADS                     = 'test-leads';
    case CAN_REALLOCATE_TEAM                       = 'can-reallocate-team';
    case CAN_REALLOCATE_ALL                        = 'can-reallocate-all';
    case VIEW_LOOKER_REPORT                        = 'view-looker-report';
    case CONTRACT_MANAGEMENT_VIEW                  = 'contract-management/view';
    case CONTRACT_MANAGEMENT_EDIT                  = 'contract-management/edit';
    case CONTRACT_MANAGEMENT_SEND                  = 'contract-management/send';
    case CONTRACT_MANAGEMENT_UPLOAD                = 'contract-management/upload';
    case CONTRACT_MANAGEMENT_BYPASS                = 'contract-management/bypass';
    case CAMPAIGNS_VIEW                            = 'campaigns/view';
    case CAMPAIGNS_CREATE                          = 'campaigns/create';
    case CAMPAIGNS_UPDATE                          = 'campaigns/update';
    case CAMPAIGNS_DELETE                          = 'campaigns/delete';
    case CAMPAIGNS_ENABLE_DISABLE_BIDDING          = 'campaigns/enable-disable-bidding';
    case CAMPAIGNS_EXCLUDE_FROM_AD_AUTOMATION      = 'campaigns/exclude-from-ad-automation';
    case CAMPAIGNS_MANAGE_FILTERS                  = 'campaigns/manage-filters';
    case COMPANY_LINK_VIEW                         = 'company-link/view';
    case COMPANY_LINK_CREATE                       = 'company-link/create';
    case COMPANY_LINK_DELETE                       = 'company-link/delete';
    case EMAIL_TEMPLATE_DELETE                     = 'email-templates/delete';
    case COMPANY_USERS_VIEW                        = 'company-users/view';
    case MAILBOX_LIST_EMAILS                       = 'mailbox/list-emails';
    case MAILBOX_SEND_EMAILS                       = 'mailbox/send-emails';
    case SALES_SUMMARY_DASHBOARD                   = 'sales-summary-dashboard';
    case VIEW_LEAD_PII                             = 'view-lead-pii';
    case ZIP_CODE_TARGETING_EXCEPTION              = 'can-enable-zip-code-targeted-campaigns';
    case PRIVACY_MANAGEMENT_VIEW                   = 'privacy-management/view';
    case PRIVACY_MANAGEMENT_REQUEST_CREATE         = 'privacy-management/request/create';
    case PRIVACY_MANAGEMENT_REQUEST_SEARCH         = 'privacy-management/request/search';
    case PRIVACY_MANAGEMENT_REQUEST_REDACT         = 'privacy-management/request/redact';
    case COMPANY_CAMPAIGN_DELIVERY_LOGS_VIEW       = 'company-campaign-delivery-log/view';
    case RELATIONSHIP_MANAGER_VIEW                 = 'relationship-manager/view';
    case RELATIONSHIP_MANAGER_EDIT                 = 'relationship-manager/edit';
    case RELATIONSHIP_MANAGER_CREATE               = 'relationship-manager/create';
    case RELATIONSHIP_MANAGER_DELETE               = 'relationship-manager/delete';
    case ACTIVITY_LOGS_CAMPAIGNS_VIEW              = 'activity-logs/campaigns/view';
    case LEADS_REPORT_VIEW                         = 'leads-report/view';
    case LEADS_REPORT_VIEW_RM                      = 'leads-report/view/rm';
    case LEADS_REPORT_VIEW_SM                      = 'leads-report/view/sm';
    case LEAD_REFUNDS_VIEW                         = 'lead-refunds/view';
    case LEAD_ALLOCATION_AND_ADJUSTMENT            = 'lead-allocation-and-adjustment';
    case BILLING_PROFILES_VIEW                     = 'billing-profiles/view';
    case BILLING_PROFILES_CREATE                   = 'billing-profiles/create';
    case BILLING_PROFILES_UPDATE                   = 'billing-profiles/update';
    case BILLING_PROFILE_POLICIES_VIEW             = 'billing-profile-policies/view';
    case BILLING_PROFILE_POLICIES_SAVE             = 'billing-profile-policies/save';
    case BILLING_MANAGEMENT_VIEW                   = 'billing-management/view';
    case BILLING_VIEW_INVOICE                      = 'billing/view-invoice';
    case BILLING_VIEW_INVOICE_REPORT               = 'billing/view-invoice-report';
    case BILLING_CREATE_INVOICE                    = 'billing/create-invoice';
    case BILLING_CANCEL_INVOICE                    = 'billing/cancel-invoice';
    case BILLING_VIEW_COMPANY_CREDITS              = 'billing/credits/company/view';
    case BILLING_APPLY_COMPANY_CREDITS             = 'billing/credits/company/apply';
    case BILLING_EXTEND_COMPANY_CREDITS            = 'billing/credits/company/extend';
    case BILLING_EXPIRE_COMPANY_CREDITS            = 'billing/credits/company/expire';
    case BILLING_CREATE_CREDIT_TYPE                = 'billing/credits-types/create';
    case BILLING_VIEW_CREDIT_TYPE                  = 'billing/credits-types/view';
    case BILLING_UPDATE_CREDIT_TYPE                = 'billing/credits-types/update';
    case BILLING_REPORTS_VIEW                      = 'billing/reports/view';
    case BILLING_OVERVIEW_VIEW                     = 'billing/overview/view';
    case BILLING_INVOICES_VIEW                     = 'billing/invoices/view';
    case BILLING_ACTION_REQUESTS_VIEW              = 'billing/action-requests/view';
    case BILLING_BILLING_PROFILES_VIEW             = 'billing/billing-profiles/view';
    case BILLING_REPORTS_EXPORT                    = 'billing/reports/export';
    case BILLING_REPORTS_RECEIVABLE_VIEW           = 'billing/reports/receivable/view';
    case BILLING_REPORTS_REVENUE_VIEW              = 'billing/reports/revenue/view';
    case BILLING_REPORTS_AGED_VIEW                 = 'billing/reports/aged/view';
    case BILLING_REPORTS_BALANCE_VIEW              = 'billing/reports/balance/view';
    case BILLING_REPORTS_TRANSACTIONS_VIEW         = 'billing/reports/transactions/view';
    case BILLING_REPORTS_REFUNDS_VIEW              = 'billing/reports/refunds/view';
    case BILLING_REPORTS_COLLECTIONS_VIEW          = 'billing/reports/collections/view';
    case BILLING_REPORTS_WRITE_OFFS_VIEW           = 'billing/reports/write-offs/view';
    case BILLING_REPORTS_CHARGEBACKS_VIEW          = 'billing/reports/chargebacks/view';
    case BILLING_REPORTS_CREDIT_MOVEMENTS_VIEW     = 'billing/reports/credit-movements/view';
    case BILLING_REPORTS_COMPANY_OVERVIEW_VIEW     = 'billing/reports/company-overview/view';
    case BILLING_REPORTS_CREDIT_OUTSTANDING_VIEW   = 'billing/reports/credit-outstanding/view';
    case BILLING_INVOICE_TEMPLATES_VIEW            = 'billing/invoice-templates/view';
    case BILLING_INVOICE_TEMPLATES_SAVE            = 'billing/invoice-templates/save';
    case BILLING_INVOICE_TRANSACTIONS_VIEW         = 'billing/invoice-transactions/view';
    case BILLING_ACTION_APPROVALS_VIEW             = 'billing/action_approvals/view';
    case BILLING_ACTION_APPROVALS_REQUEST          = 'billing/action_approvals/request';
    case BILLING_ACTION_APPROVALS_REVIEW           = 'billing/action_approvals/review';
    case BILLING_REPORTS_OVERDUE_INVOICES_VIEW     = 'billing/reports/overdue-invoices/view';
    case CONSUMER_REVIEWS_MANAGE                   = 'consumer-reviews/manage';

    case COMPANY_BASIC_STATUS_EDIT                 = 'company/basic-status/edit';
    case COMPANY_ADMIN_STATUS_EDIT                 = 'company/admin-status/edit';
    case COMPANY_PRE_ASSIGN_AM                     = 'company/pre-assign-am';
    case CONSUMER_SEARCH_EXPORT                    = 'consumer-search/export';
    case COMPANY_CAMPAIGN_CUSTOM_PRICING_LOGS_VIEW = 'company-campaign-custom-pricing-log/view';
    case RECYCLE_LEADS                             = 'recycle-leads';
    case AFFILIATES                                = 'affiliates';
    case AFFILIATES_SHADOW                         = 'affiliates/shadow';
    case AFFILIATES_CREATE                         = 'affiliates/create';
    case AFFILIATES_UPDATE                         = 'affiliates/update';
    case AFFILIATES_DELETE                         = 'affiliates/delete';
    case TEMPLATE_MANAGEMENT                       = 'template-management';
    case SMS_TEMPLATE_VIEW                         = 'sms-template/view';
    case SMS_TEMPLATE_EDIT                         = 'sms-template/edit';
    case MARKETING_CAMPAIGNS_CREATE                = 'marketing-campaigns/create';
    case MARKETING_CAMPAIGNS_EDIT                  = 'marketing-campaigns/edit';
    case MARKETING_CAMPAIGN_VIEW                   = 'marketing-campaigns/view';
    case CONSUMER_SEARCH                           = 'consumer-search';
    case SALES_OVERVIEW_VIEW                       = 'sales-overview/view';
    case BUSINESS_DEVELOPMENT_MANAGEMENT           = 'business-development-management';
    case PROSPECTOR                                = 'prospecting';
    case MINIMUM_PRICE_MANAGEMENT_VIEW             = 'minimum-price-management/view';
    case MINIMUM_PRICE_MANAGEMENT_EDIT             = 'minimum-price-management/edit';
    case GLOBAL_CONFIGURATIONS_VIEW                = 'global-configurations-view';
    case GLOBAL_CONFIGURATIONS_EDIT                = 'global-configurations-edit';
    case INDUSTRY_MANAGEMENT                       = 'industry-management';
    //todo apply these permissions to the industry management routes
    case INDUSTRY_MANAGEMENT_WEBSITES_VIEW   = 'industry-management/websites/view';
    case INDUSTRY_MANAGEMENT_WEBSITES_EDIT   = 'industry-management/websites/edit';
    case INDUSTRY_MANAGEMENT_WEBSITES_DELETE = 'industry-management/websites/delete';
    case IMPERSONATE_USERS                   = 'impersonate-users';

    case COMPANY_OVERVIEW_LEADS_CHANNEL_BREAKDOWN_VIEW = 'company/overview/lead-channel-breakdown';
    case COMPANY_DELETE                                = 'company/delete';
    case AGED_LEAD_QUEUE_REFRESH_QUEUE                 = 'aged-lead-queue/refresh-queue';
    case COMPANY_MANAGE_UNDER_REVIEW_LEADS             = 'company/manage/under-review-leads';
    case COMPANY_METRICS_VIEW                          = 'company/metrics/view';
    case COMPANY_METRICS_EDIT                          = 'company/metrics/edit';
    case COMPANY_METRICS_SEND                          = 'company/metrics/send';
    case COMPANY_COMMISSION_VIEW                       = 'company/commission/view';
    case COMPANY_USER_RELATIONSHIPS_EDIT               = 'company-user-relationships/edit';
    case FLOW_ENGINES_CONFIGURABLE_VARIABLES_VIEW      = 'flow-engines/configurable-variables/view';
    case FLOW_ENGINES_CONFIGURABLE_VARIABLES_SAVE      = 'flow-engines/configurable-variables/save';
    case FLOW_ENGINES_CONFIGURABLE_VARIABLES_DELETE    = 'flow-engines/configurable-variables/delete';

    //QA Automation
    case QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_VIEW   = 'qa-automation/industry-service/view';
    case QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_EDIT   = 'qa-automation/industry-service/edit';
    case QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_DELETE = 'qa-automation/industry-service/delete';
    case QA_AUTOMATION_RULE_MANAGEMENT_VIEW               = 'qa-automation/rules/view';
    case QA_AUTOMATION_RULE_MANAGEMENT_EDIT               = 'qa-automation/rules/edit';
    case QA_AUTOMATION_RULE_MANAGEMENT_DELETE             = 'qa-automation/rules/delete';
    case REVENUE_STREAMS_REPORT_VIEW                      = 'reports/revenue-streams/view';

    /**
     * @return string
     */
    public function getPermissionString(): string
    {
        return "permission:{$this->value}";
    }
}
