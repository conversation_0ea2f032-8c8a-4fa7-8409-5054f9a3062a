<?php

namespace App\Enums\Company;

enum CompanyRelationshipEnum: int
{
    CASE REFERRAL = 1;

    public function label(): string
    {
        return match ($this) {
            self::REFERRAL => 'Referral',
        };
    }

    public static function relationshipOptions(): array
    {
        return collect(self::cases())->map(fn(self $enum) => (['id' => $enum->value, 'name' => $enum->label()]))->toArray();
    }
}
