<?php

namespace App\Enums\Campaigns;

use App\Campaigns\Modules\Budget\AppointmentBudgetContainerModule;
use App\Campaigns\Modules\Budget\ExclusiveOnlyLeadBudgetContainerModule;
use App\Campaigns\Modules\Budget\LeadBudgetContainerModule;
use App\Campaigns\Modules\Budget\SolarLeadBudgetContainerModule;
use App\Campaigns\Modules\Budget\UnverifiedOnlyLeadBudgetContainerModule;
use App\Contracts\Campaigns\CampaignDefinitionContract;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\SaleTypes;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Registries\CampaignRegistry;
use Illuminate\Contracts\Container\BindingResolutionException;

/**
 * When adding new campaign types/overrides, please ensure that we're mapping
 * them correctly in the campaign registry.
 *
 * @see CampaignRegistry::getCampaignDefinition()
 */
enum CampaignType: int
{
    // Base products
    case LEAD_CAMPAIGN        = 0;
    case APPOINTMENT_CAMPAIGN = 1;
    case DIRECT_LEADS         = 4;

    // Industry overrides
    case SOLAR_LEAD_CAMPAIGN        = 2;
    case SOLAR_APPOINTMENT_CAMPAIGN = 3;

    // Budget overrides
    case EXCLUSIVE_ONLY_LEAD_CAMPAIGN  = 5;
    case UNVERIFIED_ONLY_LEAD_CAMPAIGN = 6;

    /**
     * Returns the definition class for this campaign type.
     *
     * @return CampaignDefinitionContract|null
     * @throws BindingResolutionException
     */
    public function definition(): ?CampaignDefinitionContract
    {
        /** @var CampaignRegistry $registry */
        $registry = app()->make(CampaignRegistry::class);

        return $registry->getCampaignDefinition($this);
    }

    /**
     * @return array
     */
    public function getBudgetConfiguration(): array
    {
        return match($this) {
            self::SOLAR_LEAD_CAMPAIGN                                    => SolarLeadBudgetContainerModule::getBudgetConfigurations(),
            self::SOLAR_APPOINTMENT_CAMPAIGN, self::APPOINTMENT_CAMPAIGN => AppointmentBudgetContainerModule::getBudgetConfigurations(),
            self::EXCLUSIVE_ONLY_LEAD_CAMPAIGN                           => ExclusiveOnlyLeadBudgetContainerModule::getBudgetConfigurations(),
            self::UNVERIFIED_ONLY_LEAD_CAMPAIGN                          => UnverifiedOnlyLeadBudgetContainerModule::getBudgetConfigurations(),
            default                                                      => LeadBudgetContainerModule::getBudgetConfigurations(),
        };
    }

    /**
     * @param string $returnType
     * @return array
     */
    public function getCampaignSaleTypes(string $returnType = SaleTypes::RETURN_TYPE_NAME): array
    {
        return SaleTypes::byCompanyCampaignType($this, $returnType);
    }

    /**
     * @param int $productId
     * @param int $serviceId
     * @return CampaignType
     */
    public static function fromProductAndServiceId(int $productId, int $serviceId): CampaignType
    {
        $product = ProductEnum::tryFrom(Product::query()->find($productId)?->name ?? "");
        $industry = IndustryEnum::tryFrom(IndustryService::query()->find($serviceId)?->industry->name ?? "");

        switch($industry) {
            case IndustryEnum::SOLAR: {
                return match(true) {
                    $product === ProductEnum::APPOINTMENT  => CampaignType::SOLAR_APPOINTMENT_CAMPAIGN,
                    $product === ProductEnum::DIRECT_LEADS => CampaignType::DIRECT_LEADS,
                    default                                => CampaignType::SOLAR_LEAD_CAMPAIGN,
                };
            }
            default: {
                    return match(true) {
                        $product === ProductEnum::APPOINTMENT => CampaignType::APPOINTMENT_CAMPAIGN,
                        $product === ProductEnum::DIRECT_LEADS => CampaignType::DIRECT_LEADS,
                        default                               => CampaignType::LEAD_CAMPAIGN,
                    };
            }
        }
    }

    public function label(): string
    {
        return match ($this) {
            self::LEAD_CAMPAIGN => 'Lead Campaign',
            self::APPOINTMENT_CAMPAIGN => 'Appointment Campaign',
            self::DIRECT_LEADS => 'Direct Lead Campaign',
            self::SOLAR_LEAD_CAMPAIGN => 'Solar Lead Campaign',
            self::SOLAR_APPOINTMENT_CAMPAIGN => 'Solar Appointment Campaign',
            self::EXCLUSIVE_ONLY_LEAD_CAMPAIGN => 'Exclusive Only Lead Campaign',
            self::UNVERIFIED_ONLY_LEAD_CAMPAIGN => 'Unverified Only Lead Campaign'
        };
    }
}
