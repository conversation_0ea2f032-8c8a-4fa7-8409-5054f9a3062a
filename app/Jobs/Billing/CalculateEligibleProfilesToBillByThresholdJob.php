<?php

namespace App\Jobs\Billing;

use App\Models\Billing\BillingProfile;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Billing\BillingProfileRepository;
use App\Services\Billing\BillingLogService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class CalculateEligibleProfilesToBillByThresholdJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const int CHUNK_SIZE                       = 1000;

    public function __construct()
    {
        $this->onQueue('billing');
    }

    /**
     * @return WithoutOverlapping[]
     */
    public function middleware(): array
    {
        return [
            (new WithoutOverlapping('calculate-eligible-profiles-to-bill-by-threshold-job'))
                ->releaseAfter(60) // Retry 60 seconds later if lock exists
                ->expireAfter(3600)  // Expire lock after 1 hour max
        ];
    }

    /**
     * @param BillingProfileRepository $billingProfileRepository
     * @return void
     */
    public function handle(
        BillingProfileRepository $billingProfileRepository,
    ): void
    {
        $billingProfileRepository->getTotalChargeableUninvoicedLeadsGroupedByBillingProfile(
            overThreshold: true,
        )->chunk(self::CHUNK_SIZE, function (Collection $collection) {
            foreach ($collection as $item) {
                $this->processBillingProfile($item);
            }
        });
    }

    /**
     * @param ProductAssignment $item
     * @return void
     */
    protected function processBillingProfile(ProductAssignment $item): void
    {
        try {
            $totalsMessage = "Threshold: $$item->threshold_in_dollars Total spent in leads: $$item->total_leads_cost_in_dollars. Leads count $item->count_leads";
            $campaignMessage = "Campaign Ids: $item->campaign_ids";

            BillingLogService::log(
                message    : "Threshold reached for billing profile id $item->billing_profile_id. $totalsMessage. $campaignMessage",
                namespace  : 'calculate_eligible_profiles_to_bill_by_threshold_job',
                relatedType: BillingProfile::class,
                relatedId  : $item->billing_profile_id,
                context    : [
                    "billing_profile_id"          => $item->billing_profile_id,
                    "company_id"                  => $item->company_id,
                    "threshold_in_dollars"        => $item->threshold_in_dollars,
                    "default"                     => $item->default,
                    "total_leads_cost_in_dollars" => $item->total_leads_cost_in_dollars,
                    "count_leads"                 => $item->count_leads,
                    "campaign_ids"                => $item->campaign_ids,
                ]
            );

            GenerateUninvoicedProductAssignmentsInvoiceJob::dispatchSync(
                $item->billing_profile_id,
                'threshold_exceeded'
            );
        } catch (Exception $exception) {
            BillingLogService::logException(
                exception  : $exception,
                namespace  : 'calculate_eligible_profiles_to_bill_by_threshold_job',
                relatedType: BillingProfile::class,
                relatedId  : $item->billing_profile_id,
                context    : [
                    "billing_profile_id"          => $item->billing_profile_id,
                    "company_id"                  => $item->company_id,
                    "threshold_in_dollars"        => $item->threshold_in_dollars,
                    "default"                     => $item->default,
                    "total_leads_cost_in_dollars" => $item->total_leads_cost_in_dollars,
                    "count_leads"                 => $item->count_leads,
                    "campaign_ids"                => $item->campaign_ids,
                ]
            );
        }
    }
}
