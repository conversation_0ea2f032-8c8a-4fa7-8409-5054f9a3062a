<?php

namespace App\Jobs\Companies;

use App\Enums\ActivityType;
use App\Models\ActivityFeed;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Models\Sales\Task;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ReleaseCompaniesBackToQueue implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        $olderThan5Days = fn ($query) => $query->whereDate('company_user_relationships.created_at', '<=', now()->subDays(5));

        Company::query()
            ->doesntHave(Company::RELATION_ACCOUNT_MANAGER)
            ->where(function (Builder $query) use ($olderThan5Days) {
                $query->whereHas(Company::RELATION_BUSINESS_DEVELOPMENT_MANAGER, $olderThan5Days)
                    ->orWhereHas(Company::RELATION_SALES_DEVELOPMENT_REPRESENTATIVE, $olderThan5Days);
            })->whereDoesntHave(Company::RELATION_ACTIVITY_FEEDS, function ($query) {
                $query->whereIn(ActivityFeed::FIELD_ITEM_TYPE, [
                    ActivityType::CALL->value,
                    ActivityType::TEXT->value,
                    ActivityType::EMAIL->value,
                ])->whereDate(ActivityFeed::FIELD_CREATED_AT, '>=', now()->subDays(5));
            })->whereDoesntHave(Company::RELATION_TASKS, function ($query) {
                $query->whereDate(Task::FIELD_AVAILABLE_AT, '>=', now()->subDays(5));
            })->whereDoesntHave(Company::RELATION_PRODUCT_ASSIGNMENTS, function ($query) {
                $query->whereDate(ProductAssignment::FIELD_CREATED_AT, '>=', now()->subDays(120));
            })->each(function ($company) {
                $company->unassignBusinessDevelopmentManager();
                $company->unassignSalesDevelopmentRepresentative();
            });
    }
}
