<?php

namespace App\Jobs\Prospects;

use App\Models\Prospects\NewBuyerProspect;
use App\Services\Odin\ConsumerProductVerification\IpQuality\IpQualityScoreService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class VerifyNewBuyerProspectJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public int $newBuyerProspectId) {}

    /**
     * Execute the job.
     *
     * @param IpQualityScoreService $ipQualityScoreService
     * @return void
     * @throws Exception
     */
    public function handle(IpQualityScoreService $ipQualityScoreService): void
    {
        $newBuyerProspect = NewBuyerProspect::findOrFail($this->newBuyerProspectId);
        $result = $ipQualityScoreService->verifyNewBuyerProspect($newBuyerProspect);
        $isRisky = $result->phone['risky'] ?? false;
        $newBuyerProspect->is_risky = (bool) $isRisky;
        $newBuyerProspect->save();
        $newBuyerProspect->refresh();
    }
}
