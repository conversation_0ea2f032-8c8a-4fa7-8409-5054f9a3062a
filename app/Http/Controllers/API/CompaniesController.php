<?php

namespace App\Http\Controllers\API;

use App\Builders\CompanyRevenueBuilder;
use App\Enums\CompanyCampaignSource;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\CompanyMediaAssetType;
use App\Enums\CompanySalesStatus;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Enums\TimePeriod;
use App\Events\AccountManagerAssignedEvent;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Requests\Company\UpdateCompanyManagerRequest;
use App\Http\Requests\DeleteCompanyMediaAssetRequest;
use App\Http\Requests\GetCampaignBudgetUsageRequest;
use App\Http\Requests\GetCompanyCampaignsRequest;
use App\Http\Requests\Odin\CreateBaseCompanyFormRequest;
use App\Http\Requests\Odin\CreateCompanyLinkRequest;
use App\Http\Requests\Odin\CreateTestLeadRequest;
use App\Http\Requests\Odin\DeleteCompanyLinkRequest;
use App\Http\Requests\Odin\StoreCompanyLocationRequest;
use App\Http\Requests\PatchCompanyBasicInfoRequest;
use App\Http\Requests\PatchCompanyConfigurableFields;
use App\Http\Requests\SearchCompanyContractsRequests;
use App\Http\Requests\SearchCompanyLeadsRequests;
use App\Http\Requests\StoreCompanyDetailsRequest;
use App\Http\Requests\StoreCompanyLogoRequest;
use App\Http\Requests\StoreCompanyMediaAssetsRequest;
use App\Http\Requests\StoreCompanyYoutubeAssetRequest;
use App\Http\Requests\Company\UpdateAmPreAssignmentRequest;
use App\Http\Requests\UpdateChargeableStatusRequest;
use App\Http\Requests\UpdateCompanySalesStatusRequest;
use App\Http\Resources\ActionResource;
use App\Http\Resources\Billing\CompanyRevenueGraphSummaryResource;
use App\Http\Resources\Companies\Sales\AccountManagerResource;
use App\Http\Resources\Odin\CompanyCampaignListOptionResource;
use App\Http\Resources\Odin\CompanyContractsResource;
use App\Http\Resources\Odin\CompanyLinkResource;
use App\Http\Resources\Odin\ProductCampaignFieldOptionsResource;
use App\Http\Resources\TestProduct\TestProductResourceCollection;
use App\Http\Resources\Billing\CompanyInvoiceSummaryResource;
use App\Http\Resources\Billing\LegacyInvoiceSummaryResource;
use App\Http\Resources\CompanyCampaign\CampaignOverviewResource;
use App\Http\Resources\ConsolidatedCompanyContractResource;
use App\Http\Resources\Dashboard\CompanyMediaAssetResource;
use App\Http\Resources\Dashboard\InvoiceAssociatedProductsResource;
use App\Models\Action;
use App\Models\ActionCategory;
use App\Models\CompanyContract;
use App\Models\CompanyUserRelationship;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyMediaAsset;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\Product as ProductModel;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ServiceProduct;
use App\Models\Role;
use App\Models\SuccessManager;
use App\Models\TestProduct;
use App\Models\User;
use App\Policies\CompanyDetailsPolicy;
use App\Repositories\ActionRepository;
use App\Repositories\AuditLogRepository;
use App\Repositories\Billing\CompanyBillingRepository;
use App\Repositories\CompanyContactRepository;
use App\Repositories\CompanyContractRepository;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Repositories\LeadCampaignRepository;
use App\Repositories\Legacy\CompanyAddressRepository;
use App\Repositories\Legacy\CompanyRepository as LegacyCompanyRepository;
use App\Repositories\Legacy\CompanySuccessManagerRepository as LegacyCompanySuccessManagerRepository;
use App\Repositories\Legacy\InvoicesRepository;
use App\Repositories\Legacy\QuoteCompanyRepository;
use App\Repositories\Odin\AddressRepository;
use App\Repositories\Odin\CompanyIndustryServiceRepository;
use App\Repositories\Odin\CompanyLocationRepository;
use App\Repositories\Odin\CompanyMediaAssetRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\Odin\DashboardShadowRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Repositories\Odin\ProductCampaignRepository;
use App\Repositories\Odin\ProductRejectionRepository;
use App\Repositories\Odin\ProductRepository;
use App\Repositories\Odin\RevenueRepository;
use App\Repositories\RevenueRepository as LegacyRevenueRepository;
use App\Services\Billing\InvoiceService;
use App\Services\Campaigns\CompanyCampaignService;
use App\Services\Companies\CompanyProfileService;
use App\Services\Companies\CompanyService;
use App\Services\Companies\Delete\CompanyDeleteService;
use App\Services\CompanyLocationService;
use App\Services\CompanyRegistration\CompanyRegistrationService;
use App\Services\CompanyRegistration\CompanyRegistrationSyncService;
use App\Services\CompanySlugService;
use App\Services\Google\GeocodingService;
use App\Services\Odin\Appointments\AppointmentService;
use App\Services\ProductPricing\BestRevenueScenario\BestRevenueScenarioLogsService;
use App\Services\ProductPricing\BestRevenueScenario\BestRevenueScenarioServiceFactory;
use App\Services\ProductRejectionCalculationService;
use App\Services\Prospects\ProspectDuplicateMatchingService;
use App\Services\TestLead\TestLeadService;
use App\Transformers\Campaign\CampaignTransformer;
use App\Transformers\CompanyInvoiceTransformer;
use App\Transformers\CompanyOverviewTransformer;
use App\Transformers\Legacy\CompanyLocationToLegacyAddressTransformer;
use App\Transformers\Odin\CompanyAssignedConsumerTransformer;
use App\Transformers\Odin\CompanyBestRevenueScenarioLogsTransformer;
use App\Transformers\Odin\CompanyDataTransformer;
use App\Transformers\Odin\CompanyLocationTransformer;
use App\Transformers\Odin\CompanyProfileTransformer;
use App\Transformers\Odin\CompanyTransformer;
use App\Transformers\Odin\IndustryServiceTransformer;
use App\Transformers\Sales\SuccessManagerTransformer;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\Client\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\PaginatedResourceResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\UnauthorizedException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Throwable;

class CompaniesController extends APIController
{
    const string MODULE_PERMISSION = "companies";

    const string REQUEST_CAMPAIGN                            = 'campaign';
    const string REQUEST_STATUS                              = 'status';
    const string REQUEST_WITH_DELETED                        = 'with_deleted';
    const string REQUEST_SOURCE                              = 'source';
    const string REQUEST_LEGACY_STATUS                       = 'legacy_status';
    const string REQUEST_SERVICE                             = 'service';
    const string REQUEST_LEAD_ID                             = 'lead_id';
    const string REQUEST_NAME                                = 'name';
    const string REQUEST_STATE                               = 'state';
    const string REQUEST_PAGE                                = 'page';
    const string REQUEST_PER_PAGE                            = 'per_page';
    const string REQUEST_DATE_FROM                           = 'date_from';
    const string REQUEST_DATE_TO                             = 'date_to';
    const string REQUEST_TYPE                                = 'type';
    const string REQUEST_EVENT                               = 'event';
    const string REQUEST_TIME_PERIOD                         = 'time_period';
    const string REQUEST_PRESCREENED                         = 'prescreened';
    const string REQUEST_COMPANY_ID                          = 'company_id';
    const string REQUEST_OTHER_COMPANY_ID                    = 'other_company_id';
    const string REQUEST_COMPANY_REFERENCE                   = 'company_reference';
    const string REQUEST_COMPANY_INDUSTRIES                  = 'industries';
    const string REQUEST_COMPANY_SERVICES                    = 'services';
    const string REQUEST_COMPANY_NAME                        = 'company_name';
    const string REQUEST_COMPANY_ENTITY_NAME                 = 'company_entity_name';
    const string REQUEST_COMPANY_WEBSITE                     = 'company_website';
    const string REQUEST_COMPANY_WATCHDOG_ID                 = 'company_watchdog_id';
    const string REQUEST_COMPANY_STATUSES                    = 'statuses';
    const string REQUEST_COMPANY_LOGO                        = 'logo';
    const string REQUEST_COMPANY_MEDIA_ASSETS                = 'media_assets';
    const string REQUEST_COMPANY_YOUTUBE_ASSET               = 'youtube_asset';
    const string REQUEST_PRODUCT                             = 'product';
    const string REQUEST_ADMIN_STATUS_CHANGE_REASON          = 'admin_status_change_reason';

    const string REQUEST_APPOINTMENTS_ACTIVE = 'appointments_active';
    const string REQUEST_CAMPAIGN_ID         = 'campaign_id';
    const string REQUEST_SALES_STATUS        = 'sales_status';
    const string START_TIMESTAMP             = 'start_timestamp';
    const int    ACTIVITY_LIMIT_PER_PAGE     = 10;

    const string ROUTE_ASSET_ID = 'assetId';

    const string RESPONSE_KEY_STATUS                    = 'status';
    const string RESPONSE_KEY_SIMILAR_COMPANIES         = 'similar_companies';
    const string RESPONSE_KEY_COMPANY_ID                = 'company_id';
    const string RESPONSE_KEY_COMPANY_LEGACY_ID         = 'legacy_company_id';
    const string RESPONSE_KEY_BRS_LOGS                  = 'brs_logs';
    const string RESPONSE_KEY_ALLOCATION_FAILURE_REASON = 'allocation_failure_reason';
    const string RESPONSE_KEY_ERROR                     = 'error';
    const string RESPONSE_KEY_LOGO                      = 'logo';
    const string RESPONSE_KEY_MEDIA_ASSETS              = 'media_assets';
    const string RESPONSE_KEY_YOUTUBE_ASSET             = 'youtube_asset';

    const string FIELD_LOCATION_ID          = 'location_id';
    const string FIELD_ADDRESS              = 'address';
    const string REQUEST_PRODUCT_ID         = 'product';
    const string REQUEST_LEAD_REFUND_STATUS = 'lead_refund_status';

    const string RESPONSE_COMPANY_CONTRACTS            = 'company_contracts';
    const string RESPONSE_AUDIT_LOGS                   = 'audit_logs';
    const string REQUEST_CONTRACT_NAME                 = 'contract_name';
    const string REQUEST_CONTRACT_TYPE                 = 'contract_type';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param ProductRejectionRepository $productRejectionRepository
     * @param ProductRepository $productRepository
     * @param CompanyRepository $companyRepository
     * @param QuoteCompanyRepository $quoteCompanyRepository
     * @param ConsumerRepository $consumerRepository
     * @param CompanyLocationRepository $companyLocationRepository
     * @param AddressRepository $addressRepository
     * @param CompanyContactRepository $companyContactRepository
     * @param GeocodingService $geocodingService
     * @param CompanySlugService $companySlugService
     * @param CompanyService $companyService
     * @param TestLeadService $testLeadService
     * @param LeadCampaignRepository $leadCampaignRepository
     * @param CompanyCampaignService $companyCampaignService
     * @param CompanyContractRepository $companyContractRepository
     * @param InvoiceService $invoiceService
     * @param InvoicesRepository $invoicesRepository
     * @param CompanyBillingRepository $companyBillingRepository
     */
    public function __construct(
        Request                           $request,
        JsonAPIResponseFactory            $apiResponseFactory,
        public ProductRejectionRepository $productRejectionRepository,
        public ProductRepository          $productRepository,
        public CompanyRepository          $companyRepository,
        public QuoteCompanyRepository     $quoteCompanyRepository,
        public ConsumerRepository         $consumerRepository,
        public CompanyLocationRepository  $companyLocationRepository,
        public AddressRepository          $addressRepository,
        public CompanyContactRepository   $companyContactRepository,
        protected GeocodingService        $geocodingService,
        public CompanySlugService         $companySlugService,
        public CompanyService             $companyService,
        public TestLeadService            $testLeadService,
        protected LeadCampaignRepository  $leadCampaignRepository,
        protected CompanyCampaignService  $companyCampaignService,
        protected CompanyContractRepository  $companyContractRepository,
        protected InvoiceService $invoiceService,
        protected InvoicesRepository $invoicesRepository,
        protected CompanyBillingRepository $companyBillingRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * Handles loading the company campaigns
     *
     * @param int $companyId
     * @param GetCompanyCampaignsRequest $request
     * @param CampaignTransformer $transformer
     * @param CompanyRepository $companyRepository
     * @return JsonResponse
     * @throws Exception
     */
    public function getCampaignsForCompany(
        int                        $companyId,
        GetCompanyCampaignsRequest $request,
        CampaignTransformer        $transformer,
        CompanyRepository          $companyRepository
    ): JsonResponse
    {
        $requestFilters = $request->safe()->collect();

        $campaignsPaginated = $companyRepository->getCompanyCampaignsPaginated(
            $companyId,
            $requestFilters->get(self::REQUEST_STATUS),
            $requestFilters->get(self::REQUEST_PAGE),
            $requestFilters->get(self::REQUEST_PER_PAGE) ?? self::ACTIVITY_LIMIT_PER_PAGE,
        );

        return $this->formatResponse([
            "campaigns" => $campaignsPaginated->through(fn(LeadCampaign $campaign) => $transformer->transformCampaign($campaign)),
        ]);
    }

    /**
     * @param int $companyId
     * @param ProductCampaignRepository $productCampaignRepository
     * @return JsonResponse
     */
    public function getCompanyCampaignsOptions(int $companyId, ProductCampaignRepository $productCampaignRepository): JsonResponse
    {
        $productId = null;

        if ($this->request->has(self::REQUEST_PRODUCT))
            $productId = Product::query()->where(Product::FIELD_NAME, $this->request->get(self::REQUEST_PRODUCT))->firstOrFail()->id;
        $status      = $this->request->get(self::REQUEST_STATUS);
        $withDeleted = $this->request->get(self::REQUEST_WITH_DELETED);

        if ($this->request->get(self::REQUEST_SOURCE) === 'all') {
            [
                'campaigns' => $campaigns
            ] = $this->companyService->getAllCampaignsForCompany(
                companyId  : $companyId,
                productId  : $productId,
                status     : $status,
                withDeleted: $withDeleted,
            );

            return $this->formatResponse([
                "campaigns" => CompanyCampaignListOptionResource::collection($campaigns)
            ]);
        }

        $productCampaigns = $productCampaignRepository->getCompanyCampaigns(
            $companyId,
            $this->request->get(self::REQUEST_STATUS),
            $productId
        );

        return $this->formatResponse([
            "campaigns" => ProductCampaignFieldOptionsResource::collection($productCampaigns)
        ]);
    }

    /**
     * @param int $companyId
     *
     * @return JsonResponse
     */
    public function getCompanyActions(Request $request, int $companyId): JsonResponse
    {
        /** @var Company $company */
        $company = Company::query()->findOrFail($companyId);

        return $this->formatResponse([
            "status"  => true,
            "actions" => $company->actions($request->get('search'), $request->get('categories', []))
                ->paginate(self::ACTIVITY_LIMIT_PER_PAGE, ['*'], 'page', $this->request->get('page'))
                ->through(fn(Action $action) => new ActionResource($action))
        ]);
    }

    public function getCompanyUserActions(int $companyId, int $companyUserId): JsonResponse
    {
        /** @var Company $company */
        $company = Company::query()->findOrFail($companyId);

        $companyUserActions = $this->companyRepository->getCompanyUserActions($company, $companyUserId, self::ACTIVITY_LIMIT_PER_PAGE);

        return $this->formatResponse([
            "status"  => true,
            "actions" => $companyUserActions
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getCompanyActionCategories(): JsonResponse
    {
        $categories = ActionCategory::all()->map(fn($item) => [
            'id'   => $item->{ActionCategory::FIELD_ID},
            'name' => $item->{ActionCategory::FIELD_NAME}
        ])->toArray();
        return $this->formatResponse([
            'status'     => true,
            'categories' => $categories
        ]);
    }

    public function getLegacyCompanyTypes(): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "types"  => collect(EloquentCompany::TYPE_DISPLAY_NAMES)->map(function (string $item, string $key) {
                return ['id' => $key, 'name' => $item];
            })->values()
        ]);
    }

    /**
     * @param int $companyId
     * @param int $actionId
     * @param ActionRepository $actionRepository
     * @return JsonResponse
     */
    public function toggleActionPin(int $companyId, int $actionId, ActionRepository $actionRepository): JsonResponse
    {
        $action = Action::query()->where(['id' => $actionId, 'for_id' => $companyId])->firstOrFail();

        return $this->formatResponse([
            'status' => $actionRepository->toggleActionPin($action->{Action::FIELD_ID})
        ]);
    }

    /**
     * @param int $companyId
     * @param CompanyInvoiceTransformer $transformer
     * @param CompanyRepository $companyRepository ,
     *
     * @return JsonResponse
     */
    public function getCompanyInvoices(
        int                       $companyId,
        CompanyInvoiceTransformer $transformer,
        CompanyRepository         $companyRepository
    ): JsonResponse
    {
        return $this->formatResponse([
            "invoices" => $companyRepository->getCompanyInvoices($companyId)
                ->paginate(5, ['*'], 'page', $this->request->get('page'))
                ->through(fn(EloquentInvoice $invoice) => $transformer->transformInvoice($invoice)),
        ]);
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     */
    public function getCompanyInvoicesSummary(int $companyId): JsonResponse
    {
        /** @var Company $company */
        $company = Company::query()->findOrFail($companyId);

        $isV2 = $this->companyBillingRepository->isV2($company);

        $legacySummary = $this->invoicesRepository->getCompanyInvoicesSummary($company->id);

        return $this->formatResponse([
            "invoices_summary" => [
                'v1' => new LegacyInvoiceSummaryResource($legacySummary),
                ...(
                    $isV2 ? ['v2' => new CompanyInvoiceSummaryResource($this->invoiceService->getCompanyInvoiceSummary($company))] : []
                ),
            ]
        ]);
    }

    /**
     * @param int $companyId
     * @param SearchCompanyLeadsRequests $request
     * @param CompanyAssignedConsumerTransformer $transformer
     * @return JsonResponse
     */
    public function getCompanyLeads(
        int                                $companyId,
        SearchCompanyLeadsRequests         $request,
        CompanyAssignedConsumerTransformer $transformer
    ): JsonResponse
    {
        $searchParams = $request->safe()->collect();

        return $this->formatResponse([
            "leads" => $this->consumerRepository->getConsumersByCompanyIdWithSearchFilters(
                $companyId,
                $searchParams->get(self::REQUEST_CAMPAIGN_ID),
                $searchParams->get(self::REQUEST_STATUS),
                $searchParams->get(self::REQUEST_SERVICE),
                $searchParams->get(self::REQUEST_LEAD_ID),
                $searchParams->get(self::REQUEST_NAME),
                $searchParams->get(self::REQUEST_STATE),
                $searchParams->get(self::REQUEST_DATE_FROM),
                $searchParams->get(self::REQUEST_DATE_TO),
                $searchParams->get(self::REQUEST_PRODUCT_ID),
                $searchParams->get(self::REQUEST_LEAD_REFUND_STATUS),
            )
                ->paginate(
                    perPage: $request->get('perPage', 10),
                    page: $searchParams->get(self::REQUEST_PAGE)
                )
                ->through(fn(Consumer $consumer) => $transformer->transform(
                    consumer           : $consumer,
                    companyId          : $companyId,
                    consumerProductId  : $consumer->consumer_product_id,
                    productAssignmentId: $consumer->product_assignment_id
                ))
        ]);
    }

    /**
     * @param CompanyOverviewTransformer $transformer
     * @return JsonResponse
     */
    public function getCompanyOverview(CompanyOverviewTransformer $transformer): JsonResponse
    {
        $companyId = $this->request->route()->parameter(self::REQUEST_COMPANY_ID);

        $validator = Validator::make(
            [self::REQUEST_COMPANY_ID => $companyId],
            [self::REQUEST_COMPANY_ID => "integer|required|exists:" . Company::TABLE . "," . Company::FIELD_ID]
        );

        if ($validator->fails()) {
            return $this->formatResponse([
                self::RESPONSE_KEY_STATUS => false,
            ]);
        }

        $eloquentCompany = $this->companyRepository->findLegacyCompanyByOdinIdOrFail($companyId);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => true,
            "overview"                => $transformer->transformCompanyOverview($eloquentCompany)
        ]);
    }

    /**
     * @param int $companyId
     * @param GetCampaignBudgetUsageRequest $request
     * @param CompanyCampaignService $companyCampaignService
     * @return JsonResponse
     */
    public function getCampaignBudgetUsage(
        int                           $companyId,
        GetCampaignBudgetUsageRequest $request,
        CompanyCampaignService        $companyCampaignService
    ): JsonResponse
    {
        $validated = $request->validated();

        $campaigns = $companyCampaignService->getCampaignOverviewPaginated($companyId, $validated);

        return $this->formatResponse([
            "status"    => true,
            "campaigns" => CampaignOverviewResource::paginate($campaigns)
        ]);
    }


    /**
     * @param int $companyId
     */
    public function getCompanyLinks(int $companyId)
    {
        $linkedCompanies = $this->companyRepository->getLinkedCompaniesPaginated($companyId);
        return CompanyLinkResource::collection($linkedCompanies);
    }

    /**
     * @param int $companyId
     * @param int $linkId
     * @return JsonResponse
     */
    public function deleteCompanyLink(int $companyId, int $linkId, DeleteCompanyLinkRequest $request)
    {
        $companyLink = $this->companyRepository->getCompanyLink($linkId);
        $success     = $companyLink->delete();

        return $this->formatResponse([
            self::REQUEST_STATUS => (bool)$success,
        ]);
    }

    /**
     * @param int $companyId
     * @param CreateCompanyLinkRequest $request
     * @return JsonResponse
     */
    public function createCompanyLink(int $companyId, CreateCompanyLinkRequest $request): JsonResponse
    {
        $validated = $request->validated();

        if ($this->companyRepository->checkCompanyLinkExists($validated[self::REQUEST_COMPANY_ID], $validated[self::REQUEST_OTHER_COMPANY_ID])) {
            return $this->formatResponse([
                self::REQUEST_STATUS     => false,
                self::RESPONSE_KEY_ERROR => "A link with this company already exists."
            ]);
        }

        return $this->formatResponse([
            self::REQUEST_STATUS => $this->companyRepository->createCompanyLink($validated),
        ]);
    }

    /**
     * @param Company $companyId
     * @return CompanyRevenueGraphSummaryResource
     */
    public function getRevenueOverview(
        Company $companyId,
    ): CompanyRevenueGraphSummaryResource
    {
        $summary = Cache::remember("revenue-overview-$companyId", now()->addHour(), function () use ($companyId) {
            $companyRevenueService = new \App\Builder\Billing\CompanyRevenueBuilder($companyId->id);
            return $companyRevenueService->getSummary();
        });

        return new CompanyRevenueGraphSummaryResource($summary);
    }

    /**
     * Gets the revenue graph data.
     *
     * @param int $companyId
     * @param RevenueRepository $revenueRepository ,
     * @param CompanyRepository $companyRepository
     * @return JsonResponse
     */
    public function getRevenueGraph(
        int               $companyId,
        RevenueRepository $revenueRepository,
        CompanyRepository $companyRepository
    ): JsonResponse
    {
        $company = $companyRepository->findOrFail($companyId);

        return $this->formatResponse([
            "graph_data" => $revenueRepository->getRevenueGraphData(
                $company,
                $this->request->get('period', CompanyRevenueBuilder::PERIOD_ALL_TIME),
                $this->request->get('duration', 1)
            )
        ]);
    }

    /**
     * @param int $companyId
     *
     * @return JsonResponse
     */
    public function getLeadsOverview(int $companyId): JsonResponse
    {
        $company        = $this->companyRepository->findOrFail($companyId);
        $startTimestamp = $this->request->get(self::START_TIMESTAMP, 0);

        return $this->formatResponse([
            'total_rejection'  => $this->productRejectionRepository->getLeadRejectionCountForCompany($company, $startTimestamp),
            'total_leads'      => $this->productRepository->getLeadCountForCompany($company, $startTimestamp),
            'count_by_channel' => $this->productRepository->getLeadCountForCompanyByChannel($company, $startTimestamp)
        ]);
    }

    /**
     * @param Company $company
     * @param Collection $requestData
     * @param CompanyRepository $companyRepository
     * @param bool $servicesUpdated
     * @return array
     */
    private function getUpdatedCompanyDetailsStatus(
        Company           $company,
        Collection        $requestData,
        CompanyRepository $companyRepository,
        bool              $servicesUpdated,
    ): array
    {
        $status = true;
        if ($company->prescreened() != $requestData->get('prescreened')) {
            $status = $this->companyRepository->updatePrescreened($company, $requestData->get('prescreened'));
        }

        try {
            $legacyCompany = $this->companyRepository->findLegacyCompanyByOdinIdOrFail($company->id);

            $companyPrescreenedTimestamp = $company->{Company::FIELD_PRESCREENED_AT}->timestamp;

            $legacyCompanyPrescreenedTimestamp = $legacyCompany->{EloquentCompany::TIMESTAMP_LAST_PRESCREENED};

            if ($companyPrescreenedTimestamp !== $legacyCompanyPrescreenedTimestamp) {
                $legacyCompany->{EloquentCompany::TIMESTAMP_LAST_PRESCREENED} = $company->{Company::FIELD_PRESCREENED_AT}
                    ? $company->{Company::FIELD_PRESCREENED_AT}->timestamp
                    : 0;

                $legacyStatus = $legacyCompany->save();
            } else {
                $legacyStatus = true;
            }
        } catch (Exception) {
            $legacyStatus = null;
        }

        $status &= $companyRepository->updateCompanyModel($company, $requestData->toArray()) && $servicesUpdated;

        if ($status === 1) {
            $status = true;
        } else {
            $status = false;
        }

        return [$status, $legacyStatus];
    }

    /**
     *
     * @param int $companyId
     * @param StoreCompanyDetailsRequest $request
     * @param CompanyRepository $companyRepository
     * @param CompanyIndustryServiceRepository $companyIndustryServiceRepository
     * @param LegacyCompanyRepository $legacyCompanyRepository
     * @return JsonResponse
     * @throws Exception
     */
    public function updateCompanyDetails(
        int                              $companyId,
        StoreCompanyDetailsRequest       $request,
        CompanyRepository                $companyRepository,
        CompanyIndustryServiceRepository $companyIndustryServiceRepository,
        LegacyCompanyRepository          $legacyCompanyRepository,
    ): JsonResponse
    {
        $company     = $companyRepository->findOrFail($companyId);
        $requestData = $request->safe()->collect()->except(['services']);
        $serviceIds  = $request->safe()->collect()->get('services');

        $companyDetailsPolicy = new CompanyDetailsPolicy();
        $companyDetailsPolicy->update($company, $request->user(), $serviceIds);

        $legacyCompanyRepository->updateBasicDetails($company->{Company::FIELD_REFERENCE}, [
            EloquentCompany::TYPE => $request->safe()->collect()->get('type')
        ]);

        $servicesUpdated = !$serviceIds
            || $companyIndustryServiceRepository->setupServicesForCompany($company, $serviceIds, true, true);

        [$status, $legacyStatus] = $this->getUpdatedCompanyDetailsStatus(
            $company,
            $requestData,
            $companyRepository,
            $servicesUpdated,
        );

        $this->companySlugService->setSlugForCompany($company, $request->get('slug'));

        return $this->formatResponse([
            'status'        => $status,
            'legacy_status' => $legacyStatus,
        ]);
    }

    /**
     * @param int $companyId
     * @param UpdateCompanySalesStatusRequest $request
     * @param CompanyProfileTransformer $profileTransformer
     * @return JsonResponse
     */
    public function updateSalesStatus(int                             $companyId,
                                      UpdateCompanySalesStatusRequest $request,
                                      CompanyProfileTransformer       $profileTransformer
    ): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($companyId);
        $newSalesStatus = $request->validated(self::REQUEST_SALES_STATUS);

        $success = $this->companyRepository->updateBasicDetails($company, $request->safe()->collect()->only(self::REQUEST_SALES_STATUS));

        if ($success && $newSalesStatus === CompanySalesStatus::PR_CHECK_REQUIRED->value && !$company->onboardingManager) {
            $company->assignViaRoundRobin(
                role: RoleType::ONBOARDING_MANAGER->value,
                notify: true,
                reason: 'Assigned as OM following the sales status change to “PR Check Required.”'
            );
        }

        return $this->formatResponse([
            'status'  => $success,
            'company' => $success
                ? $profileTransformer->transformCompanyProfileOverview($company->refresh())
                : null,
        ]);
    }

    /**
     * @param int $companyId
     * @param PatchCompanyBasicInfoRequest $request
     * @param LegacyCompanyRepository $legacyCompanyRepository
     * @param LegacyCompanySuccessManagerRepository $legacyCompanySuccessManagerRepository
     * @param CompanyRepository $companyRepository
     * @param CompanyProfileTransformer $profileTransformer
     * @param ProductCampaignRepository $productCampaignRepository
     * @return JsonResponse
     * @throws Throwable
     */
    public function updateBasicInfo(
        int                                   $companyId,
        PatchCompanyBasicInfoRequest          $request,
        LegacyCompanyRepository               $legacyCompanyRepository,
        LegacyCompanySuccessManagerRepository $legacyCompanySuccessManagerRepository,
        CompanyRepository                     $companyRepository,
        CompanyProfileTransformer             $profileTransformer,
        ProductCampaignRepository             $productCampaignRepository,
    ): JsonResponse
    {
        $company = $companyRepository->findOrFail($companyId);

        //TODO: save status, type

        $appointmentsActive = (bool)$request->get(self::REQUEST_APPOINTMENTS_ACTIVE);

        $companyRepository->updateAppointmentsActive($company, $appointmentsActive);

        if (!$appointmentsActive) {
            $productCampaignRepository->deactivateCompanyCampaigns($company);
        }

        $legacySyncData = $request->safe()->except(
            [
                self::REQUEST_PRESCREENED,
                Company::FIELD_NAME,
                self::REQUEST_COMPANY_INDUSTRIES,
                Company::FIELD_SOURCED_FROM,
            ]
        );

        if ($this->companyRepository->updatePrescreened($company, $request->get(self::REQUEST_PRESCREENED))) {
            $legacySyncData[EloquentCompany::TIMESTAMP_LAST_PRESCREENED] = $company->{Company::FIELD_PRESCREENED_AT}
                ? $company->{Company::FIELD_PRESCREENED_AT}->timestamp
                : 0;
        }

        if ($request->has(self::REQUEST_COMPANY_INDUSTRIES)) {

            $company->industries()->sync($request->get(self::REQUEST_COMPANY_INDUSTRIES));

            $legacySyncData = array_merge($legacySyncData, [
                EloquentCompany::TYPE => $legacyCompanyRepository->getLegacyCompanyTypeFromIndustryIds($request->get(self::REQUEST_COMPANY_INDUSTRIES))
            ]);
        }


        $legacyCompanyRepository->updateBasicDetails($company->{Company::FIELD_REFERENCE}, $legacySyncData);

        $success = $companyRepository->updateBasicDetails($company, $request->safe()->collect()->except(
            [
                self::REQUEST_PRESCREENED,
                EloquentCompany::COMPANY_NAME,
                self::REQUEST_COMPANY_INDUSTRIES
            ]
        ));

        $companyRepository->updateCompanyPhoneOrAddress([
            CompanyLocation::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
            CompanyLocation::FIELD_ID         => $request->get(self::FIELD_ADDRESS)[self::FIELD_LOCATION_ID],
            CompanyLocation::FIELD_ADDRESS_ID => $request->get(self::FIELD_ADDRESS)[CompanyLocation::FIELD_ADDRESS_ID],
            CompanyLocation::FIELD_PHONE      => $request->get(CompanyLocation::FIELD_PHONE),
            CompanyLocation::FIELD_NAME       => $request->get(self::FIELD_ADDRESS)[CompanyLocation::FIELD_NAME],
            Address::FIELD_ADDRESS_1          => $request->get(self::FIELD_ADDRESS)[Address::FIELD_ADDRESS_1],
            Address::FIELD_ADDRESS_2          => $request->get(self::FIELD_ADDRESS)[Address::FIELD_ADDRESS_2],
            Address::FIELD_CITY               => $request->get(self::FIELD_ADDRESS)[Address::FIELD_CITY],
            Address::FIELD_STATE              => $request->get(self::FIELD_ADDRESS)[Address::FIELD_STATE],
            Address::FIELD_ZIP_CODE           => $request->get(self::FIELD_ADDRESS)[Address::FIELD_ZIP_CODE],
        ]);

        $qrTop500Company = $request->get(GlobalConfigurableFields::QR_TOP_500_COMPANY->value);
        if($qrTop500Company) {
            $companyRepository->updateCompanyData($company, [GlobalConfigurableFields::QR_TOP_500_COMPANY->value => $qrTop500Company]);
        }

        return $this->formatResponse([
            "status"  => $success,
            'company' => $success
                ? $profileTransformer->transformCompanyProfileOverview($company->refresh())
                : null,
        ]);
    }

    /**
     * @param int $companyId
     * @param CompanyRepository $companyRepository
     * @param CompanyProfileService $service
     * @return JsonResponse
     */
    public function getCompanyLogo(int                   $companyId,
                                   CompanyRepository     $companyRepository,
                                   CompanyProfileService $service
    ): JsonResponse
    {
        $company = $companyRepository->findOrFail($companyId);

        $logo = $service->getCompanyLogoURL($company);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => true,
            self::RESPONSE_KEY_LOGO   => $logo
        ]);
    }

    /**
     * @param int $companyId
     * @param StoreCompanyLogoRequest $request
     * @param CompanyRepository $companyRepository
     * @param CompanyProfileService $service
     * @return JsonResponse
     * @throws Exception
     */
    public function saveCompanyLogo(int                     $companyId,
                                    StoreCompanyLogoRequest $request,
                                    CompanyRepository       $companyRepository,
                                    CompanyProfileService   $service
    ): JsonResponse
    {
        $company = $companyRepository->findOrFail($companyId);

        $logoUrl = $service->updateCompanyLogo(
            company: $company,
            file   : $request->file(self::REQUEST_COMPANY_LOGO)
        );

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => !!$logoUrl,
            self::RESPONSE_KEY_LOGO   => $logoUrl
        ]);
    }

    /**
     * @param int $companyId
     * @param CompanyRepository $companyRepository
     * @param CompanyMediaAssetRepository $companyMediaAssetRepository
     * @return JsonResponse
     */
    public function getCompanyMediaAssets(int                         $companyId,
                                          CompanyRepository           $companyRepository,
                                          CompanyMediaAssetRepository $companyMediaAssetRepository,
    ): JsonResponse
    {
        $company = $companyRepository->findOrFail($companyId);

        $mediaAssets = $companyMediaAssetRepository->getAllMediaAssets(
            company: $company->{Company::FIELD_ID},
            types  : [
                CompanyMediaAssetType::MEDIA,
                CompanyMediaAssetType::LINK,
            ],
            sortCol: CompanyMediaAsset::FIELD_ID,
            sortDir: 'asc',
        );

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS          => true,
            self::REQUEST_COMPANY_MEDIA_ASSETS => CompanyMediaAssetResource::collection($mediaAssets),
        ]);
    }

    /**
     * @param int $companyId
     * @param StoreCompanyMediaAssetsRequest $request
     * @param CompanyRepository $companyRepository
     * @param CompanyProfileService $service
     * @return JsonResponse
     * @throws Exception
     */
    public function saveCompanyMediaAssets(int                            $companyId,
                                           StoreCompanyMediaAssetsRequest $request,
                                           CompanyRepository              $companyRepository,
                                           CompanyProfileService          $service
    ): JsonResponse
    {
        $company = $companyRepository->findOrFail($companyId);

        $assets = $service->uploadCompanyMediaAssets(
            company: $company,
            files  : $request->file(self::REQUEST_COMPANY_MEDIA_ASSETS),
        );

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS       => !!$assets,
            self::RESPONSE_KEY_MEDIA_ASSETS => CompanyMediaAssetResource::collection($assets),
        ]);
    }

    /**
     * @param int $companyId
     * @param StoreCompanyYoutubeAssetRequest $request
     * @param CompanyRepository $companyRepository
     * @param CompanyMediaAssetRepository $companyMediaAssetRepository
     * @param CompanyProfileService $service
     * @return JsonResponse
     * @throws Exception
     */
    public function saveCompanyYoutubeAsset(int                             $companyId,
                                            StoreCompanyYoutubeAssetRequest $request,
                                            CompanyRepository               $companyRepository,
                                            CompanyMediaAssetRepository     $companyMediaAssetRepository,
                                            CompanyProfileService           $service
    ): JsonResponse
    {
        $company = $companyRepository->findOrFail($companyId);

        $youtubeAssetUrl = $request->get(self::REQUEST_COMPANY_YOUTUBE_ASSET);

        /** @var CompanyMediaAsset|null $youtubeAsset */
        $youtubeAsset = $companyMediaAssetRepository->getYoutubeAsset($companyId, $youtubeAssetUrl);

        if (!empty($youtubeAsset)) {
            return $this->formatResponse([
                self::RESPONSE_KEY_STATUS => false,
                self::RESPONSE_KEY_ERROR  => "You've already uploaded this YouTube video."
            ]);
        }

        $asset = $service->addYoutubeLink($company, $youtubeAssetUrl);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS        => !!$asset,
            self::RESPONSE_KEY_YOUTUBE_ASSET => new CompanyMediaAssetResource($asset),
        ]);
    }

    /**
     * @param int $companyId
     * @param DeleteCompanyMediaAssetRequest $request
     * @param CompanyRepository $companyRepository
     * @param CompanyProfileService $service
     * @return JsonResponse
     */
    public function deleteCompanyMediaAsset(int                            $companyId,
                                            DeleteCompanyMediaAssetRequest $request,
                                            CompanyRepository              $companyRepository,
                                            CompanyProfileService          $service
    ): JsonResponse
    {
        $company = $companyRepository->findOrFail($companyId);

        $status = $service->deleteCompanyMediaAsset(
            company: $company,
            assetId: $request->route(self::ROUTE_ASSET_ID)
        );

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => $status,
        ]);
    }

    /**
     * @param int $companyId
     * @param CompanyRepository $companyRepository
     * @param LegacyCompanyRepository $legacyCompanyRepository
     * @param CompanyDataTransformer $transformer
     * @param PatchCompanyConfigurableFields $request
     * @return JsonResponse
     */
    public function updateConfigurableFields(
        int                            $companyId,
        CompanyRepository              $companyRepository,
        LegacyCompanyRepository        $legacyCompanyRepository,
        CompanyDataTransformer         $transformer,
        PatchCompanyConfigurableFields $request
    ): JsonResponse
    {
        $company = $companyRepository->findOrFail($companyId);

        $input = $request->safe()->all();
        if (!$input) throw new BadRequestException("Oops, the requested data set was deemed invalid!");

        return $this->formatResponse([
            "status"        => $companyRepository->updateCompanyData($company, $input)
                && $legacyCompanyRepository->updateConfigurableFields($company->{Company::FIELD_REFERENCE}, $input),
            "updatedFields" => $transformer->transform($company)
        ]);
    }

    /**
     * Create a new Odin CompanyLocation - sync with integration-api
     *
     * @param int $companyId
     * @param StoreCompanyLocationRequest $request
     * @param CompanyAddressRepository $legacyAddressRepository
     * @param CompanyLocationToLegacyAddressTransformer $legacyTransformer
     * @param CompanyLocationService $companyLocationService
     *
     * @return JsonResponse
     * @throws RequestException
     */
    public function createCompanyLocation(
        int                                       $companyId,
        StoreCompanyLocationRequest               $request,
        CompanyAddressRepository                  $legacyAddressRepository,
        CompanyLocationToLegacyAddressTransformer $legacyTransformer,
        CompanyLocationService                    $companyLocationService
    ): JsonResponse
    {
        /** @var Company|ModelNotFoundException $company */
        $company     = $this->companyRepository->findOrFail($companyId);
        $addressData = $request->safe()->only(CompanyLocation::RELATION_ADDRESS)[CompanyLocation::RELATION_ADDRESS] ?? [];
        // Default to US in the absence of any country selector
        $addressData[Address::FIELD_COUNTRY] = $addressData[Address::FIELD_COUNTRY] ?? 'US';
        $locationData                        = $request->safe()->except(CompanyLocation::RELATION_ADDRESS);

        $newAddress  = $this->addressRepository->createAddressFromAttributes(
            $companyLocationService->addLatLngToAddress($addressData)
        );
        $newLocation = $this->companyLocationRepository->updateOrCreateCompanyLocation([
            ...$locationData,
            CompanyLocation::FIELD_COMPANY_ID => $companyId,
            CompanyLocation::FIELD_ADDRESS_ID => $newAddress->{Address::FIELD_ID}
        ]);
        if ($newLocation->{CompanyLocation::FIELD_ID}) {
            try {
                $legacyAddressData = $legacyTransformer->transformCompanyLocationToLegacyAddress($newLocation);
                $legacyStatus      = $legacyAddressRepository->createCompanyAddress($company->{Company::FIELD_REFERENCE}, $legacyAddressData)[self::REQUEST_STATUS] ?? false;
            } catch (Throwable $err) {
                logger()->error("Failed to create CompanyAddress on integration-api\n\t" . $err->getMessage());
            }
        }

        return $this->formatResponse([
            self::REQUEST_STATUS        => !!$newLocation,
            self::REQUEST_LEGACY_STATUS => $legacyStatus ?? false,
        ]);
    }

    /**
     * Update Odin CompanyLocation and related Address - sync with integration-api
     *
     * @param int $companyId
     * @param int $locationId
     * @param StoreCompanyLocationRequest $request
     * @param CompanyAddressRepository $legacyAddressRepository
     * @param CompanyLocationToLegacyAddressTransformer $legacyTransformer
     * @param CompanyLocationService $companyLocationService
     *
     * @return JsonResponse
     * @throws RequestException
     */
    public function updateCompanyLocation(
        int                                       $companyId,
        int                                       $locationId,
        StoreCompanyLocationRequest               $request,
        CompanyAddressRepository                  $legacyAddressRepository,
        CompanyLocationToLegacyAddressTransformer $legacyTransformer,
        CompanyLocationService                    $companyLocationService
    ): JsonResponse
    {
        /** @var Company|ModelNotFoundException $company */
        $company = $this->companyRepository->findOrFail($companyId);
        /** @var CompanyLocation|ModelNotFoundException $location */
        $location = $this->companyLocationRepository->findOrFail($locationId);

        $addressData  = $companyLocationService->addLatLngToAddress(
            $request->safe()->only(CompanyLocation::RELATION_ADDRESS)[CompanyLocation::RELATION_ADDRESS] ?? []
        );
        $locationData = $request->safe()->except(CompanyLocation::RELATION_ADDRESS);

        $address = $location->{CompanyLocation::RELATION_ADDRESS};
        if (!$address) {
            $newAddress                                      = $this->addressRepository->createAddressFromAttributes($addressData);
            $locationData[CompanyLocation::FIELD_ADDRESS_ID] = $newAddress->{Address::FIELD_ID};
        } else {
            $this->addressRepository->updateAddress($address, $addressData);
        }

        $success = !!$this->companyLocationRepository->updateOrCreateCompanyLocation([
            ...$locationData,
            CompanyLocation::FIELD_COMPANY_ID => $companyId,
            CompanyLocation::FIELD_ADDRESS_ID => $location->{CompanyLocation::FIELD_ADDRESS_ID}
        ]);

        if ($success) {
            try {
                // Perform legacy check on the given location - if for some reasons it's not already added, go ahead to create one & sync back
                $legacyAddressData = $legacyTransformer->transformCompanyLocationToLegacyAddress($location->refresh());
                $legacyAddressId   = $location->{CompanyLocation::RELATION_ADDRESS}->{Address::FIELD_LEGACY_ID};
                $legacyStatus      = $legacyAddressId
                    ? $legacyAddressRepository->updateCompanyAddressDetails($company->{Company::FIELD_REFERENCE}, $legacyAddressId, $legacyAddressData)[self::REQUEST_STATUS] ?? false
                    : $legacyAddressRepository->createCompanyAddress($company->{Company::FIELD_REFERENCE}, $legacyAddressData)[self::REQUEST_STATUS] ?? false;
            } catch (Throwable $err) {
                logger()->error("Failed to update CompanyAddress on integration-api\n\t" . $err->getMessage());
            }
        }

        return $this->formatResponse([
            self::REQUEST_STATUS        => $success,
            self::REQUEST_LEGACY_STATUS => $legacyStatus ?? false,
        ]);
    }

    /**
     * @param int $companyId
     * @param int $companyLocationId
     * @return JsonResponse
     */
    public function deleteCompanyLocation(
        int                      $companyId,
        int                      $companyLocationId,
        CompanyAddressRepository $legacyAddressRepository
    ): JsonResponse
    {
        $location = $this->companyLocationRepository->findOrFail($companyLocationId);
        $company  = $this->companyRepository->findOrFail($companyId);

        if ($location->{CompanyLocation::FIELD_COMPANY_ID} === $companyId) {
            $legacyAddressId = $location->{CompanyLocation::RELATION_ADDRESS}?->{Address::FIELD_LEGACY_ID};
            $success         = $this->companyLocationRepository->deleteCompanyLocation($companyLocationId);
            if ($success && $legacyAddressId) {
                try {
                    $legacyStatus = $legacyAddressRepository->deleteCompanyAddress($company->{Company::FIELD_REFERENCE}, $legacyAddressId)[self::REQUEST_STATUS] ?? false;
                } catch (Throwable $err) {
                    logger()->error("Failed to delete CompanyAddress on integration-api\n\t" . $err->getMessage());
                    $legacyStatus = false;
                }
            } else $legacyStatus = false;
        }
        return $this->formatResponse([
            self::REQUEST_STATUS        => $success ?? false,
            self::REQUEST_LEGACY_STATUS => $legacyStatus ?? false,
        ]);
    }

    /**
     * @param int $companyId
     * @param LegacyRevenueRepository $repository
     * @return JsonResponse
     * @throws BadRequestException
     */
    public function getRevenueInsights(int $companyId, LegacyRevenueRepository $repository): JsonResponse
    {
        $eloquentCompany = $this->companyRepository->findLegacyCompanyByOdinIdOrFail($companyId);

        $timePeriod = $this->request->get(self::REQUEST_TIME_PERIOD);

        /** @var TimePeriod $requestedTimePeriod */
        $requestedTimePeriod = TimePeriod::tryFrom($timePeriod);

        if (empty($timePeriod) || $requestedTimePeriod === null)
            throw new BadRequestException();

        return $this->formatResponse([
            'status'  => true,
            'revenue' => $repository->getRevenueInsights($eloquentCompany, $requestedTimePeriod)
        ]);
    }

    public function getAssociatedProducts(int               $companyId, int $invoiceId,
                                          CompanyRepository $companyRepository): JsonResponse
    {
        $products = $companyRepository->getAssociatedProducts($companyId, $invoiceId);
        return $this->formatResponse([
            "products" => InvoiceAssociatedProductsResource::collection($products)
        ]);
    }

    /**
     * @param int $companyId
     * @param CompanyProfileTransformer $transformer
     * @param CompanyRepository $companyRepository
     *
     * @return JsonResponse
     */
    public function getProfileData(
        int                       $companyId,
        CompanyProfileTransformer $transformer,
        CompanyRepository         $companyRepository
    ): JsonResponse
    {
        $company = $companyRepository->findOrFail($companyId);
        return $this->formatResponse([
            'company'         => $transformer->transformCompanyProfile($company),
            'adminUrl'        => config('app.solarreviews_domain.admin'),
            'display_profile' => $company->contractorProfile?->status,
        ]);
    }

    /**
     * Delete a Lead Campaign via integration-api
     *
     * @param int $companyId
     * @param string $campaignUuid
     * @param LeadCampaignRepository $campaignRepository
     * @param ProductCampaignRepository $productCampaignRepository
     * @return Response|JsonResponse
     */
    public function deleteCampaign(
        int                       $companyId,
        string                    $campaignUuid,
        LeadCampaignRepository    $campaignRepository,
        ProductCampaignRepository $productCampaignRepository
    ): Response|JsonResponse
    {
        /** @var Company $company */
        $company = Company::query()->findOrFail($companyId);

        $deleteProductCampaignRes = $productCampaignRepository->deleteCampaign($productCampaignRepository->findCampaignByLegacyUuid($campaignUuid)?->{ProductCampaign::FIELD_ID} ?? 0);
        $deleteLeadCampaignRes    = $campaignRepository->delete($company->{Company::FIELD_LEGACY_ID}, $campaignUuid)['result'];

        return $this->formatResponse([
            "status" => $deleteLeadCampaignRes && $deleteProductCampaignRes
        ]);
    }

    /**
     * @param int $companyId
     * @param CompanyRepository $companyRepository
     * @param IndustryServiceTransformer $transformer
     * @return JsonResponse
     */
    public function getCompanyServices(
        int                        $companyId,
        CompanyRepository          $companyRepository,
        IndustryServiceTransformer $transformer
    ): JsonResponse
    {
        $company = $companyRepository->findOrFail($companyId);
        return $this->formatResponse([
            'services' => $transformer->transform($company->services)
        ]);
    }

    /**
     * @param int $companyId
     * @param CompanyRepository $companyRepository
     * @param CompanyLocationTransformer $locationTransformer
     * @return JsonResponse
     */
    public function getCompanyLocations(
        int                        $companyId,
        CompanyRepository          $companyRepository,
        CompanyLocationTransformer $locationTransformer
    ): JsonResponse
    {
        $company = $companyRepository->findOrFail($companyId);

        return $this->formatResponse([
            'addresses' => $locationTransformer->transformCompanyLocations($company)
        ]);
    }

    /**
     * Returns the data for global company lookup.
     *
     * @param int $id
     * @param CompanyRepository $repository
     * @param CompanyProfileTransformer $transformer
     * @param SuccessManagerTransformer $successManagerTransformer
     * @return JsonResponse
     */
    public function getCompanyProfile(
        int                       $id,
        CompanyRepository         $repository,
        CompanyProfileTransformer $transformer,
        SuccessManagerTransformer $successManagerTransformer,
    ): JsonResponse
    {
        $company = $repository->findOrFail($id);

        return $this->formatResponse([
            'company'          => $transformer->transformCompanyProfileOverview($company),
            'account_managers' => AccountManagerResource::collection(User::accountManagerRole()->get()),
            'success_managers' => $successManagerTransformer->transformAll(SuccessManager::query()->get()),
        ]);
    }

    /**
     * Returns a paginated list of companies that have got either of their contact detail (cell phone, office phone and email)
     * or IP address similar to the given company's ID.
     *
     * @param int $companyId
     * @param CompanyTransformer $transformer
     * @return JsonResponse
     */
    public function getOtherRegistrationsForCompany(int $companyId, CompanyTransformer $transformer): JsonResponse
    {
        // Temporarily disabled to investigate high CPU usage
        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS            => false,
            self::RESPONSE_KEY_SIMILAR_COMPANIES => [],
            'message' => 'Temporary disabled'
        ]);

//        /** @var Company|ModelNotFoundException $company */
//        $company = $this->companyRepository->findOrFail($companyId);
//
//        /** @var Collection<CompanyUser> $companyUsers */
//        $companyUsers = $company->{Company::RELATION_USERS};
//
//        $companiesByContactDetails = $this->companyContactRepository
//            ->getCompaniesWithSimilarContactDetails(
//                $companyUsers->pluck(CompanyUser::FIELD_CELL_PHONE)->filter(fn($cellPhone) => $cellPhone)->toArray(),
//                $companyUsers->pluck(CompanyUser::FIELD_OFFICE_PHONE)->filter(fn($officePhone) => $officePhone)->toArray(),
//                $companyUsers->pluck(CompanyUser::FIELD_EMAIL)->filter(fn($email) => $email)->toArray())
//            ->toArray();
//
//        /** @var Collection $companyIPAddresses */
//        $companyIPAddresses = $company->{Company::RELATION_CONTRACTS}->pluck(CompanyContract::FIELD_IP_ADDRESS);
//        $ipAddress          = $company->{Company::RELATION_DATA}?->{CompanyData::FIELD_PAYLOAD}[GlobalConfigurableFields::IP_ADDRESS->value] ?? '';
//        if ($ipAddress) $companyIPAddresses->push($ipAddress);
//
//        $companiesByIPAddresses = $this->companyRepository
//            ->getCompaniesByIPAddress($companyIPAddresses->unique()->toArray())->pluck(Company::FIELD_ID)
//            ->toArray();
//
//        $companiesByName = $this->companyRepository->getSimilarCompaniesByName($company->{Company::FIELD_NAME})->pluck(Company::FIELD_ID)->toArray();
//
//        $similarCompanies = Company::query()
//            ->whereIn(Company::FIELD_ID, array_unique(array_merge($companiesByContactDetails, $companiesByIPAddresses, $companiesByName)))
//            ->whereNot(Company::FIELD_ID, $company->{Company::FIELD_ID})
//            ->paginate(self::ACTIVITY_LIMIT_PER_PAGE, ['*'], 'page', $this->request->get('page'))
//            ->through(fn(Company $company) => $transformer->transformCompany($company));
//
//        return $this->formatResponse([
//            self::RESPONSE_KEY_STATUS            => true,
//            self::RESPONSE_KEY_SIMILAR_COMPANIES => $similarCompanies
//        ]);
    }

    /**
     * Handles creating a new company in Admin2 with basic details along with setting up industries & services.
     * Also, takes care to sync the new company to Legacy by creating one in the legacy system and pointing back its ID to Admin2.
     *
     * @param CreateBaseCompanyFormRequest $request
     * @param CompanyRegistrationService $companyService
     * @param CompanyRegistrationSyncService $syncService
     * @param LegacyCompanyRepository $legacyCompanyRepository
     * @param CompanyIndustryServiceRepository $companyIndustryServiceRepository
     * @return JsonResponse
     * @throws Exception
     */
    public function createCompany(CreateBaseCompanyFormRequest     $request,
                                  CompanyRegistrationService       $companyService,
                                  CompanyRegistrationSyncService   $syncService,
                                  LegacyCompanyRepository          $legacyCompanyRepository,
                                  CompanyIndustryServiceRepository $companyIndustryServiceRepository
    ): JsonResponse
    {
        $requestedData = $request->safe()->collect();

        $company = $companyService->registerBaseCompany(
            name      : $requestedData->get(self::REQUEST_COMPANY_NAME),
            entityName: $requestedData->get(self::REQUEST_COMPANY_ENTITY_NAME),
            watchDogId: $requestedData->get(self::REQUEST_COMPANY_WATCHDOG_ID),
            website   : $requestedData->get(self::REQUEST_COMPANY_WEBSITE),
        );

        if ($company?->{Company::FIELD_REFERENCE}) {
            $companyIndustries = $requestedData->get(self::REQUEST_COMPANY_INDUSTRIES);
            $companyServices   = $requestedData->get(self::REQUEST_COMPANY_SERVICES);

            $legacyData = $syncService->transformForLegacy(Company::class, $company->toArray());

            if (!empty($companyIndustries)) {
                $company->industries()->sync($companyIndustries);
                $legacyData = array_merge($legacyData, [
                    EloquentCompany::TYPE => $legacyCompanyRepository->getLegacyCompanyTypeFromIndustryIds($companyIndustries)
                ]);
            }

            $legacyResponse = $syncService->post(
                route        : $syncService::CREATE_COMPANY_API_ROUTE,
                data         : $legacyData,
                retries      : 3,
                retryInterval: 100,
                timeout      : 10
            )?->json() ?? [];

            if (is_array($legacyResponse)
                && array_key_exists(self::RESPONSE_KEY_STATUS, $legacyResponse)
                && $legacyResponse[self::RESPONSE_KEY_STATUS]) {
                $company->update([
                    Company::FIELD_LEGACY_ID => $legacyResponse[self::RESPONSE_KEY_COMPANY_LEGACY_ID]
                ]);

                if (!empty($companyServices)) {
                    $companyIndustryServiceRepository->setupServicesForCompany($company, $companyServices, false);
                }
            } else {
                logger()->error("The operation to create a new company `$company->name` could not continue due to a failure to sync to legacy.");
                $company->delete();
                throw new Exception("Something went wrong while reaching out to the legacy server. Please try re-submitting.");
            }
        }

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS            => !!$company?->{Company::FIELD_ID},
            self::RESPONSE_KEY_COMPANY_ID        => $company?->{Company::FIELD_ID} ?? '',
            self::RESPONSE_KEY_COMPANY_LEGACY_ID => $company?->{Company::FIELD_LEGACY_ID} ?? '',
        ]);
    }

    /**
     * @param int $companyId
     * @param BestRevenueScenarioLogsService $bestRevenueScenarioLogsService
     * @param CompanyBestRevenueScenarioLogsTransformer $companyBestRevenueScenarioLogsTransformer
     * @return JsonResponse
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getBestRevenueScenarioLogsForCompany(
        int                                       $companyId,
        BestRevenueScenarioLogsService            $bestRevenueScenarioLogsService,
        CompanyBestRevenueScenarioLogsTransformer $companyBestRevenueScenarioLogsTransformer
    ): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS   => true,
            self::RESPONSE_KEY_BRS_LOGS => $companyBestRevenueScenarioLogsTransformer->transformLogsQuery(
                $bestRevenueScenarioLogsService->getCompanyLogsQuery($companyId),
                request()->get(self::REQUEST_PAGE)
            )
        ]);
    }


    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getCompanyStatuses(Request $request): JsonResponse
    {
        $statuses = array_map(function ($status) {
            return ['value' => $status, 'label' => CompanyConsolidatedStatus::label($status)];
        }, Company::STATUSES);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS      => true,
            self::REQUEST_COMPANY_STATUSES => $statuses,
        ]);
    }

    /**
     * @param int $companyId
     * @param int $consumerProductId
     * @param CompanyRepository $companyRepository
     * @param ConsumerProductRepository $consumerProductRepository
     * @param ProductAssignmentRepository $productAssignmentRepository
     * @param ProductCampaignRepository $productCampaignRepository
     * @param AppointmentService $appointmentService
     * @return JsonResponse
     */
    public function investigateAllocationFailure(
        int                         $companyId,
        int                         $consumerProductId,
        CompanyRepository           $companyRepository,
        ConsumerProductRepository   $consumerProductRepository,
        ProductAssignmentRepository $productAssignmentRepository,
        ProductCampaignRepository   $productCampaignRepository,
        AppointmentService          $appointmentService
    ): JsonResponse
    {
        $company           = $companyRepository->findOrFail($companyId);
        $consumerProduct   = $consumerProductRepository->findOrFail(
            $consumerProductId,
            [
                ConsumerProduct::RELATION_SERVICE_PRODUCT . '.' . ServiceProduct::RELATION_PRODUCT,
                ConsumerProduct::RELATION_SERVICE_PRODUCT . '.' . ServiceProduct::RELATION_SERVICE . '.' . IndustryService::RELATION_INDUSTRY,

            ]
        );
        $productCampaignId = $this->request->get(self::REQUEST_CAMPAIGN_ID, 0);

        if ($consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_PRODUCT}->{ProductModel::FIELD_NAME} !== ProductEnum::APPOINTMENT->value) {
            return $this->formatResponse(
                [
                    self::RESPONSE_KEY_STATUS => false,
                    self::RESPONSE_KEY_ERROR  => 'Consumer product must be appointment'
                ],
                422
            );
        }

        $productCampaign = null;
        if ($productCampaignId) {
            $productCampaign = $productCampaignRepository->findByIdAndCompany($productCampaignId, $company);

            if (empty($productCampaign)) {
                return $this->formatResponse(
                    [
                        self::RESPONSE_KEY_STATUS => false,
                        self::RESPONSE_KEY_ERROR  => 'Invalid campaign'
                    ],
                    422
                );
            }
        }

        if ($productAssignmentRepository->findByConsumerProductAndCompany($consumerProduct, $company)) {
            return $this->formatResponse(
                [
                    self::RESPONSE_KEY_STATUS => false,
                    self::RESPONSE_KEY_ERROR  => 'Company already received consumer product'
                ],
                422
            );
        }

        if (in_array($companyId, $appointmentService->getCompaniesThatReceivedRelatedAppointments($consumerProductId), true)) {
            return $this->formatResponse(
                [
                    self::RESPONSE_KEY_STATUS => false,
                    self::RESPONSE_KEY_ERROR  => 'Company already received another appointment for same consumer'
                ],
                422
            );
        }

        $industry = IndustryEnum::from($consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{IndustryModel::FIELD_NAME});

        if (!in_array($industry->value, [IndustryEnum::SOLAR->value, IndustryEnum::ROOFING->value], true)) {
            $brsService = BestRevenueScenarioServiceFactory::make(BestRevenueScenarioServiceFactory::DRIVER_ODIN);
        } else {
            $brsService = BestRevenueScenarioServiceFactory::make(config('sales.brs_driver'));
        }

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS                    => true,
            self::RESPONSE_KEY_ALLOCATION_FAILURE_REASON => $brsService->investigateAllocationFailure($company, $consumerProduct, $productCampaign)
        ]);
    }

    /**
     * @param int $companyId
     * @param int $leadId
     * @param ProductAssignmentRepository $productAssignmentRepository
     * @param UpdateChargeableStatusRequest $request
     *
     * @return JsonResponse
     */
    public function updateChargeableStatus(
        int                           $companyId,
        int                           $leadId,
        ProductAssignmentRepository   $productAssignmentRepository,
        UpdateChargeableStatusRequest $request
    ): JsonResponse
    {
        $status  = $productAssignmentRepository->noChargeLead($companyId, $leadId);
        $message = $status ? 'Charge status updated successfully' : 'Could not update chargeable status';

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => $status,
            'message'                 => $message
        ]);
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     */
    public function getProducts(
        int $companyId
    ): JsonResponse
    {
        $products = ProductModel::all();

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => true,
            'products'                => $products,
        ]);
    }

    /**
     * @param int $companyId
     * @param string $campaignUuid
     * @return JsonResponse
     */
    public function downloadCampaignZipcodes(
        int               $companyId,
        string            $campaignUuid,
        CompanyRepository $companyRepository
    ): JsonResponse
    {
        $company = $companyRepository->findOrFail($companyId);

        $leadCampaignId = LeadCampaign::query()
            ->where(LeadCampaign::UUID, $campaignUuid)
            ->pluck(LeadCampaign::ID)
            ->first();

        $locationIds = LeadCampaignLocation::query()
            ->where(LeadCampaignLocation::LEAD_CAMPAIGN_ID, $leadCampaignId)
            ->pluck(LeadCampaignLocation::LOCATION_ID)
            ->toArray();

        $zipCodes = Location::query()
            ->whereIn(Location::ID, $locationIds)
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->pluck(Location::ZIP_CODE)
            ->unique()
            ->toArray();

        $csvContent = "ID, ZIPCODE\n";
        $rowNumber  = 1;
        foreach ($zipCodes as $zipCode) {
            $csvContent .= "$rowNumber,";
            $csvContent .= "$zipCode";
            $csvContent .= "\n";
            $rowNumber++;
        }

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => true,
            'csvContent'              => $csvContent
        ]);
    }

    /**
     * @param int $companyId
     *
     * @return TestProductResourceCollection
     */
    public function getTestLeads(int $companyId): TestProductResourceCollection
    {
        $testLeads = TestProduct::query()
            ->where(TestProduct::FIELD_COMPANY_ID, $companyId)
            ->orderBy(TestProduct::FIELD_ID, 'DESC')
            ->paginate($this->request->get(self::REQUEST_PER_PAGE, 10));

        return new TestProductResourceCollection($testLeads);
    }

    /**
     * @param int $companyId
     * @param ComputedRejectionStatisticRepository $rejectionStatisticRepository
     * @param ProductRejectionCalculationService $rejectionService
     * @return JsonResponse
     */
    public function resetCrmRejections(int $companyId, ComputedRejectionStatisticRepository $rejectionStatisticRepository, ProductRejectionCalculationService $rejectionService): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        if (!$user->hasRole(RoleType::ADMIN->value)) {
            throw new UnauthorizedException("User lacks the required role to reset CRM Rejections");
        }

        $rejectionStatisticRepository->resetCrmRejectionsByCompanyId($companyId, $rejectionService);

        return $this->formatResponse([
            'status' => true
        ]);
    }

    /**
     * @param int $companyId
     * @param CreateTestLeadRequest $request
     * @return JsonResponse
     * @throws \phpDocumentor\GraphViz\Exception
     */
    public function createTestLead(int $companyId, CreateTestLeadRequest $request): JsonResponse
    {
        $company        = $this->companyRepository->findOrFail($companyId);
        $requestedData  = $request->safe()->collect();
        $campaignId     = $requestedData->get(CreateTestLeadRequest::FIELD_CAMPAIGN_ID);
        $campaignSource = CompanyCampaignSource::tryFrom($requestedData->get(CreateTestLeadRequest::FIELD_SOURCE));

        $userId   = \auth()->user()->id;
        $revealAt = now()->addDay(); //todo: reveal at

        $this->testLeadService->createMultiCampaignTestLead($company, $campaignId, $campaignSource, $userId, $revealAt);

        return $this->formatResponse([
            'status' => true
        ]);
    }

    /**
     * @param int $companyId
     * @param SearchCompanyContractsRequests $request
     * @return JsonResponse
     */
    public function getCompanyContracts(
        int                             $companyId,
        SearchCompanyContractsRequests  $request,
    ) : JsonResponse
    {
        $searchParams = $request->safe()->collect();

        //todo filter contracts by contract key, website, active and companyUser
        $companyContractsQuery = $this->companyRepository->getContractsByCompanyIdWithSearchFilters(
            $companyId,
            $searchParams->get(self::REQUEST_CONTRACT_NAME),
            $searchParams->get(self::REQUEST_CONTRACT_TYPE),
        );

        $paginated = $companyContractsQuery->paginate(10, ['*'], 'page', $request->get(self::REQUEST_PAGE));
        $resourceCollection = CompanyContractsResource::collection($paginated);
        $response = new PaginatedResourceResponse($resourceCollection);

        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS           => true,
            self::RESPONSE_COMPANY_CONTRACTS    => json_decode($response->toResponse($request)->content(), true),
            'company_contract_bypass'           => Company::query()->findOrFail($companyId)->bypass_contract_signing,
        ]);
    }

    /**
     * @param DashboardShadowRepository $dashboardShadowRepository
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getShadowToken(DashboardShadowRepository $dashboardShadowRepository): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($this->request->route('company_id'));
        $isFixr  = $this->request->get('is_fixr', false);

        $token = $dashboardShadowRepository->getShadowToken($company, $isFixr);

        return $this->formatResponse([
            'token' => $token
        ]);
    }

    /**
     * @param int $companyId
     * @param int $companyContractId
     * @param AuditLogRepository $auditLogRepository
     * @return JsonResponse
     */
    public function getCompanyContractAuditLogs(int $companyId, int $companyContractId, AuditLogRepository $auditLogRepository): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_KEY_STATUS => true,
            self::RESPONSE_AUDIT_LOGS => $auditLogRepository->getAuditLogsForCompanyContract($companyContractId),
        ]);
    }

    /**
     * @param int|string $companyId
     * @return JsonResponse
     */
    public function getCompanyContractApprovals(int|string $companyId): JsonResponse
    {
        $company = Company::query()->findOrFail($companyId);

        $legacyContracts = $this->companyRepository->getLegacyContracts(
            companyId: $company->{Company::FIELD_LEGACY_ID}
        )->get();

        $a20Contracts = $this->companyContractRepository->getCompanyContractsQuery(
            companyId:  $company->{Company::FIELD_ID},
        )->get();

        $resource = new ConsolidatedCompanyContractResource(collect([
            ConsolidatedCompanyContractResource::FIELD_A20_CONTRACTS    => $a20Contracts,
            ConsolidatedCompanyContractResource::FIELD_LEGACY_CONTRACTS => $legacyContracts
        ]));

        return $this->formatResponse([
            'status'    => true,
            'contracts' => $resource->toArray($this->request)
        ]);
    }

    /**
     * @param int|string $companyId
     * @param CompanyProfileTransformer $profileTransformer
     * @return JsonResponse
     */
    public function recalculateConsolidatedStatus(int|string $companyId, CompanyProfileTransformer $profileTransformer): JsonResponse
    {
        $company = Company::query()->findOrFail($companyId);

        $company->recalculateSystemStatus();
        $company->recalculateCampaignStatus();
        $company->recalculateConsolidatedStatus();

        $company->refresh();

        return $this->formatResponse([
             'status'  => true,
             'company' => true
                 ? $profileTransformer->transformCompanyProfileOverview($company)
                 : null
        ]);
    }

    /**
     * @param int|string $companyId
     * @return JsonResponse
     */
    public function updateBypassContractSigning(int|string $companyId): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        if (!$user->hasPermissionTo(PermissionType::CONTRACT_MANAGEMENT_SEND->value)) {
            throw new UnauthorizedException("User lacks the required permissions");
        }

        $company = Company::query()->findOrFail($companyId);
        $company->bypass_contract_signing = $this->request->get('bypass_contract_signing', false) ;

        return $this->formatResponse([
            'status' => $company->save()
        ]);
    }

    /**
     * @param int $companyId
     * @return JsonResponse
     */
    public function checkForDuplicates(int $companyId): JsonResponse
    {
        /** @var Company $company */
        $company = Company::find($companyId);
        if (!$company?->website_verified_url) {
            return $this->formatResponse();
        }

        $dupes = ProspectDuplicateMatchingService::findCompaniesWithExactWebsite($company->website_verified_url)->where(Company::FIELD_ID, '<>', $company->id)->map(function (Company $company) {

            $companyDeleteService = new CompanyDeleteService(company: $company);
            $companyDeleteService->validate();

            return [
                    'company_id'   => $company->id,
                    'company_name' => $company->name,
                    'website'      => $company->website_verified_url,
                    'profile_link' => $company->getAdminProfileUrl(),
                    'deletable'    => $companyDeleteService->isDeletable(),
                    'queued'       => $companyDeleteService->markedForDeletion(),
                ];
            })->toArray();

        return $this->formatResponse(['company' => ['duplicate_companies' => $dupes]]);
    }

    /**
     * @param int $companyId
     * @param UpdateAmPreAssignmentRequest $request
     *
     * @return JsonResponse
     */
    public function updateAmPreAssignment(int $companyId, UpdateAmPreAssignmentRequest $request): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($companyId);

        if ($request->get(UpdateAmPreAssignmentRequest::REQUEST_AM_ID) === null) {
            $company->removeAccountManagerPreAssignment();
        } else {
            $company->preAssignAccountManager($request->get(UpdateAmPreAssignmentRequest::REQUEST_AM_ID));
        }

        return $this->formatResponse([
            'pre_assigned_account_manager' => [
                'id' => $company->preassignedAccountManager?->id,
                'name' => $company->preassignedAccountManager?->name
            ]
        ]);
    }

    /**
     * @param int $companyId
     * @param RoleType $roleType
     * @param UpdateCompanyManagerRequest $request
     * @param CompanyProfileTransformer $transformer
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function updateCompanyMangerAssignment(int $companyId, RoleType $roleType, UpdateCompanyManagerRequest $request, CompanyProfileTransformer $transformer): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($companyId);

        //un-assignment request
        if ($request->has(UpdateCompanyManagerRequest::REQUEST_MANAGER_ID) && $request->validated(UpdateCompanyManagerRequest::REQUEST_MANAGER_ID) === null) {
            $company->unassign($roleType->value, true);

            return $this->formatResponse([
                'company' => $transformer->transformCompanyProfileOverview($company->refresh())
            ]);
        }

        $manager = User::role($roleType)->findOrFail($request->validated(UpdateCompanyManagerRequest::REQUEST_MANAGER_ID));

        $relationship = $company->assign($manager)->as($roleType->value, true);

        $commissionableFrom = $request->validated(UpdateCompanyManagerRequest::REQUEST_COMMISSIONABLE_FROM);
        $commissionableTo = $request->validated(UpdateCompanyManagerRequest::REQUEST_COMMISSIONABLE_TO);

        if ($relationship && ($commissionableFrom || $commissionableTo)) {
            $relationship->update(
                collect([
                    CompanyUserRelationship::FIELD_COMMISIONABLE_AT => $commissionableFrom ? Carbon::parse($commissionableFrom)->utc() : null,
                    CompanyUserRelationship::FIELD_COMMISIONABLE_TO => $commissionableTo ? Carbon::parse($commissionableTo)->utc() : null,
                ])->filter()->toArray()
            );
        }

        return $this->formatResponse([
            'company' => $transformer->transformCompanyProfileOverview($company->refresh())
        ]);
    }

}
