<?php

namespace App\Http\Controllers\API\Sales;

use App\Enums\RoleType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Sales\AccountAssignmentParticipantsRequest;
use App\Http\Resources\Sales\AccountAssignmentParticipantResource;
use App\Services\Sales\SalesManagementService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class SalesManagementAPIController extends APIController
{
    const string REQUEST_ADD_USER_IDS    = 'add_user_ids';
    const string REQUEST_REMOVE_USER_IDS = 'remove_user_ids';
    const string RESPONSE_PARTICIPANTS   = 'participants';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    public function getAccountAssignmentParticipants(RoleType $roleType, AccountAssignmentParticipantsRequest $request, SalesManagementService $salesManagementService): JsonResponse
    {
        $participants = $salesManagementService->getAccountAssignmentParticipants($roleType);

        return $this->formatResponse([
            self::RESPONSE_PARTICIPANTS => AccountAssignmentParticipantResource::collection($participants),
        ]);
    }

    /**
     * @throws Throwable
     */
    public function updateAccountAssignmentParticipants(RoleType $roleType, AccountAssignmentParticipantsRequest $request, SalesManagementService $salesManagementService): JsonResponse
    {
        $validated = $request->validated();
        $participants = $salesManagementService->updateAccountAssignmentParticipants($validated[self::REQUEST_ADD_USER_IDS], $validated[self::REQUEST_REMOVE_USER_IDS], $roleType->value)
            ? $salesManagementService->getAccountAssignmentParticipants($roleType)
            : null;

        return $this->formatResponse([
            self::RESPONSE_PARTICIPANTS => AccountAssignmentParticipantResource::collection($participants),
        ]);
    }
}
