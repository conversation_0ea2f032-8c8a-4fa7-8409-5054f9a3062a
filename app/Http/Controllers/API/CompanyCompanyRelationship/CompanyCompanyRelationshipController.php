<?php

namespace App\Http\Controllers\API\CompanyCompanyRelationship;

use App\Enums\Company\CompanyRelationshipEnum;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\CompanyCompanyRelationship\StoreCompanyCompanyRelationshipRequest;
use App\Http\Resources\CompanyCompanyRelationship\CompanyCompanyRelationshipResource;
use App\Models\CompanyCompanyRelationship;
use App\Models\Odin\Company;
use App\Repositories\CompanyCompanyRelationship\CompanyCompanyRelationshipRepository;
use App\Services\CompanyCompanyRelationship\CompanyCompanyRelationshipService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class CompanyCompanyRelationshipController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected CompanyCompanyRelationshipService $companyCompanyRelationshipService,
        protected CompanyCompanyRelationshipRepository $companyCompanyRelationshipRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param int $companyId
     *
     * @return JsonResponse
     */
    public function getCompanyRelationships(int $companyId): JsonResponse
    {
        return $this->formatResponse([
            'relationships' => CompanyCompanyRelationshipResource::collection(Company::findOrFail($companyId)->companyRelationships()->latest()->get())
        ]);
    }

    public function createRelationship(StoreCompanyCompanyRelationshipRequest $request, int $companyId): JsonResponse
    {
        if ($companyId === $request->validated(CompanyCompanyRelationship::FIELD_TARGET_COMPANY_ID)) {
            throw new BadRequestException('Invalid target company');
        }

        $this->companyCompanyRelationshipRepository->createCompanyRelationship(
            company: Company::findOrFail($companyId),
            targetCompany: Company::findOrFail($request->validated(CompanyCompanyRelationship::FIELD_TARGET_COMPANY_ID)),
            relationship: CompanyRelationshipEnum::from($request->validated(CompanyCompanyRelationship::FIELD_RELATIONSHIP))
        );

        return $this->formatResponse();
    }

    public function deleteRelationship(int $companyId, CompanyCompanyRelationship $companyCompanyRelationship): JsonResponse
    {
        if ($companyCompanyRelationship->company_id !== $companyId) {
            throw new NotFoundHttpException('Invalid relationship');
        }

        $companyCompanyRelationship->delete();

        return $this->formatResponse();
    }
}
