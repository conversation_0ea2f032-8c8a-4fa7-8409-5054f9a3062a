<?php

namespace App\Http\Controllers\API\Webhooks\Twilio;

use App\Http\Controllers\Controller;
use App\Repositories\CommunicationRepository;
use App\Services\CallForwardingService;
use App\Services\Communication\CommunicationService;
use App\Services\Communication\TwilioCommunicationService;
use App\Services\ConsumerRevalidationService;
use App\Models\Call;
use App\Models\Phone;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Services\PubSub\PubSubService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Throwable;
use Twilio\TwiML\MessagingResponse;
use Twilio\TwiML\VoiceResponse;

class TwilioWebhookController extends Controller
{
    const REQUEST_DIRECTION          = 'Direction';
    const REQUEST_DIAL_STATUS        = 'DialCallStatus';
    const REQUEST_FROM               = 'From';
    const REQUEST_TO                 = 'To';
    const REQUEST_CALLER             = 'Caller';
    const REQUEST_DIAL_CALL_SID      = 'DialCallSid';
    const REQUEST_CALL_SID           = 'CallSid';
    const REQUEST_RECORDING_URL      = 'RecordingUrl';
    const REQUEST_RECORDING_DURATION = 'RecordingDuration';
    const REQUEST_CALL_STATUS        = 'CallStatus';

    const DIAL_STATUS_NO_ANSWER = 'no-answer';
    const DIAL_STATUS_BUSY      = 'busy';
    const DIAL_STATUS_COMPLETED = 'completed';

    const DIRECTION_OUTGOING = 'outbound';
    const DIRECTION_INBOUND  = 'inbound';

    const VOICEMAIL_KEY      = 'Digits';
    const CAll_RECORDING_KEY = 'RecordingSid';

    /**
     * Handles an incoming voice notification from twilio
     *
     * @param TwilioCommunicationService $service
     * @return JsonResponse
     */
    public function voice(TwilioCommunicationService $service): Response
    {
        if (request()->post('Outgoing', 'false') === 'true')
            return $this->outgoingCall($service);

        return $this->incomingCall($service);
    }

    public function callStatus(CommunicationRepository $repository, TwilioCommunicationService $service, CommunicationService $communicationService, PubSubService $pubSubService): Response
    {
        $toNumber = Str::replace("+1", "", request()->post(self::REQUEST_TO));
        $fromNumber = Str::replace("+1", "", request()->post(self::REQUEST_FROM));
        $callSid = request()->post(self::REQUEST_CALL_SID);
        $dialCallSid = request()->post(self::REQUEST_DIAL_CALL_SID);
        $dialStatus = request()->post(self::REQUEST_DIAL_STATUS);

        // Handle missed calls via server-side webhook instead of client-side logic
        if (in_array($dialStatus, [self::DIAL_STATUS_NO_ANSWER, self::DIAL_STATUS_BUSY])){
            // Log the missed call before handling voicemail
            $this->handleMissedCall($communicationService, $pubSubService, $fromNumber, $toNumber, $callSid, $dialStatus);

            $user = $repository->getUserByNumber($toNumber);
            return $user?->userVoicemailMessage?->getTwilioResponse() ?? $this->voicemail($service);
        }

        $response = new VoiceResponse();
        $response->hangup();

        if (request()->has(self::VOICEMAIL_KEY)) {
            $communicationService->recordVoicemail(
                $fromNumber,
                $toNumber,
                $callSid,
                request()->post(self::REQUEST_RECORDING_URL),
                request()->post(self::REQUEST_RECORDING_DURATION)
            );
        } elseif (request()->has(self::CAll_RECORDING_KEY)) {

            // The sId we use to track calls is sent in different columns, depending if the call is inbound or outbound
            // the only way to distinguish between inbound and outbound is by the presence of "client:" in "Caller"
            $sId = str_contains(request()->post(self::REQUEST_CALLER), 'client:' ) ?
                $callSid :
                $dialCallSid;

            $communicationService->recordCall(
                $sId,
                request()->post(self::CAll_RECORDING_KEY),
                request()->post(self::REQUEST_RECORDING_URL),
                request()->post(self::REQUEST_RECORDING_DURATION)
            );
        }

        return response($response->asXML());
    }

    /**
     * Handle missed call logging via server-side webhook
     * This prevents duplicate notifications from multiple client tabs
     * and ensures notifications even when no tabs are open
     *
     * Uses the same CommunicationService.updateOrCreateInboundCall method
     * that the client-side was using, ensuring caller identification,
     * relation mapping, and PubSub event triggering all work correctly.
     */
    protected function handleMissedCall(CommunicationService $communicationService, PubSubService $pubSubService, string $fromNumber, string $toNumber, string $callSid, string $dialStatus): void
    {
        try {
            // Determine call result based on dial status
            $callResult = $dialStatus === self::DIAL_STATUS_BUSY ? 'busy' : 'missed';

            // Use the same service method that the client-side was using
            // This ensures caller identification and relation mapping work correctly
            $callLogId = $communicationService->updateOrCreateInboundCall(
                serviceName: 'twilio',
                userPhone: $toNumber,
                contactPhone: $fromNumber,
                externalReference: $callSid,
                callResult: $callResult
            );

            if ($callLogId > 0 && in_array($callResult, [Call::RESULT_BUSY, Call::RESULT_MISSED, Call::RESULT_VOICEMAIL], true)) {
                $call = Call::query()->with(Call::RELATION_PHONE)->findOrFail($callLogId);

                $pubSubService->handle(
                    EventCategory::INTERNAL->value,
                    EventName::MISSED_CALL->value,
                    [
                        'relation_type' => $call?->{Call::FIELD_RELATION_TYPE},
                        'relation_id' => $call?->{Call::FIELD_RELATION_ID},
                        'from_phone' => $call->{Call::FIELD_OTHER_NUMBER},
                        'to_phone' => $call->{Call::RELATION_PHONE}->{Phone::FIELD_PHONE},
                        'company_reference' => $call?->{Call::FIELD_RELATION_TYPE} === Call::RELATION_COMPANY ? (int) $call?->{Call::RELATION_COMPANY}?->reference : '',
                        'lead_id' => $call?->{Call::FIELD_RELATION_TYPE} === Call::RELATION_LEAD ? (int) $call?->{Call::FIELD_RELATION_ID} : 0
                    ]
                );
            }

        } catch (\Exception $e) {
            \Log::error("Failed to handle missed call webhook", [
                'error' => $e->getMessage(),
                'from' => $fromNumber,
                'to' => $toNumber,
                'call_sid' => $callSid
            ]);
        }
    }

    protected function voicemail(TwilioCommunicationService $service): Response
    {
        $response = new VoiceResponse();
        $response->play(url("/audio/generic-voicemail.mp3"));
        // $response->say("Thanks for calling Solar Reviews. Sorry we couldn't answer your call at this time. Please leave your name and number after the tone, and we'll contact you as soon as possible.");
        $response->record(["timeout" => 30, "transcribe" => true]);
        $response->hangup();

        return response($response->asXML());
    }

    /**
     * Handles an incoming SMS notification from twilio.
     *
     * @param TwilioCommunicationService $service
     * @return Response
     * @throws Throwable
     */
    public function sms(TwilioCommunicationService $service): Response
    {
        $service->handleIncomingSMS(request()->post());
        $response = new MessagingResponse();

        return response($response->asXML());
    }


    /**
     * @param ConsumerRevalidationService $consumerRevalidationService
     *
     * @return Response
     * @throws BindingResolutionException
     */
    public function consumerSMS(ConsumerRevalidationService $consumerRevalidationService): Response
    {
        $consumerRevalidationService->handleConsumerReply(request()->post());

        return response((new MessagingResponse())->asXML());
    }

    /**
     * @return Response
     */
    public function recycledLeadsSms(): Response
    {
        return response((new MessagingResponse())->asXML());
    }

    protected function outgoingCall(TwilioCommunicationService $service): Response
    {
        $response = new VoiceResponse();
        $response->dial(
            request()->post(self::REQUEST_TO),
            [
                "callerId" => request()->post(self::REQUEST_FROM),
                "record"   => "record-from-ringing-dual",
                "action"   => route("twilio-call-status"),
                "method"   => "POST",
            ]
        );

        return response($response->asXML());
    }

    protected function incomingCall(TwilioCommunicationService $service): Response
    {
        $from = Str::replace("+1", "", request()->post(self::REQUEST_FROM));
        $to = Str::replace("+1", "", request()->post(self::REQUEST_TO));
        $response = new VoiceResponse();
        $callForwardingService = app()->make(CallForwardingService::class);
        $forwardingNumber = $callForwardingService->getForwardingNumber($to);
        $dial = $response->dial('', [
            "callerId" => $from,
            "record"   => "record-from-ringing-dual",
            "action"   => route("twilio-call-status"),
            "method"   => "POST",
            "timeout"  => 20
        ]);
        $dial->client("incoming_" . $to);
        if($forwardingNumber){$dial->number($forwardingNumber);}

        return response($response->asXML());
    }
}
