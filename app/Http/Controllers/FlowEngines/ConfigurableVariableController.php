<?php

namespace App\Http\Controllers\FlowEngines;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\FlowEngines\DeleteConfigurableVariableDataRequest;
use App\Http\Requests\FlowEngines\GetConfigurableVariableDataRequest;
use App\Http\Requests\FlowEngines\SaveConfigurableVariableDataRequest;
use App\Services\Engines\Client\ConfigurableEngineVariableClientFactory;
use App\Services\Engines\Client\ConfigurableEngineVariableClientService;
use App\Services\LocationService;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Throwable;

class ConfigurableVariableController extends APIController
{
    const string ENV_PRODUCTION = 'production';
    const string ENV_DEVELOPMENT = 'development';

    protected ConfigurableEngineVariableClientService $configurableEngineVariableClientService;
    protected ?ConfigurableEngineVariableClientService $devConfigurableEngineVariableClientService = null;

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected LocationService $locationService,
    )
    {
        $this->configurableEngineVariableClientService = ConfigurableEngineVariableClientFactory::make('api');
        try {
            $this->devConfigurableEngineVariableClientService = ConfigurableEngineVariableClientFactory::make('dev');
        }
        catch (Throwable $e) {
            logger()->error("DevConfigurableEngineVariableClientService Error: " . $e->getMessage());
        }

        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param GetConfigurableVariableDataRequest $request
     * @return JsonResponse
     * @throws ConnectionException
     * @throws RequestException
     */
    public function getConfigurableVariableData(GetConfigurableVariableDataRequest $request): JsonResponse
    {
        $filter = $request->validated();
        $enabledEnvironments = [self::ENV_PRODUCTION];
        $data = [];

        $data[self::ENV_PRODUCTION] = $this->configurableEngineVariableClientService->getConfigurableEngineVariables(
            state : Arr::get($filter, GetConfigurableVariableDataRequest::STATE),
            county: Arr::get($filter, GetConfigurableVariableDataRequest::COUNTY),
            engine: Arr::get($filter, GetConfigurableVariableDataRequest::ENGINE),
            key   : Arr::get($filter, GetConfigurableVariableDataRequest::KEY),
        );

        if ($this->devConfigurableEngineVariableClientService) {
            try {
                $devData = $this->getClientService(self::ENV_DEVELOPMENT)->getConfigurableEngineVariables(
                    state: Arr::get($filter, GetConfigurableVariableDataRequest::STATE),
                    county: Arr::get($filter, GetConfigurableVariableDataRequest::COUNTY),
                    engine: Arr::get($filter, GetConfigurableVariableDataRequest::ENGINE),
                    key: Arr::get($filter, GetConfigurableVariableDataRequest::KEY),
                );
                if ($devData) {
                    $enabledEnvironments[] = self::ENV_DEVELOPMENT;
                    $data[self::ENV_DEVELOPMENT] = $devData;
                }
            }
            catch (Throwable $e) {
                logger()->error("Unable to connect to dev engines - " . $e->getMessage());
            }
        }

        return $this->formatResponse([
            'environments' => $enabledEnvironments,
            ...$data,
        ]);
    }


    /**
     * @param SaveConfigurableVariableDataRequest $request
     * @return JsonResponse
     * @throws ConnectionException
     * @throws RequestException
     */
    public function saveConfigurableVariable(SaveConfigurableVariableDataRequest $request): JsonResponse
    {
        $payload = $request->validated();

        $service = $this->getClientService();
        $data = $service->saveConfigurableEngineVariables($payload);

        return response()->json($data);
    }


    /**
     * @param int $variableId
     * @param DeleteConfigurableVariableDataRequest $request
     * @return JsonResponse
     * @throws ConnectionException
     * @throws RequestException
     */
    public function deleteConfigurableVariable(int $variableId, DeleteConfigurableVariableDataRequest $request): JsonResponse
    {
        $request->validated();

        $service = $this->getClientService();
        $data = $service->deleteConfigurableEngineVariables($variableId);

        return $this->formatResponse($data);
    }

    /**
     * @param string|null $environment
     * @return ConfigurableEngineVariableClientService
     */
    private function getClientService(?string $environment = null): ConfigurableEngineVariableClientService
    {
        $environment = $environment ?? $this->request->get(SaveConfigurableVariableDataRequest::FIELD_ENGINE_ENVIRONMENT);

        return $environment === self::ENV_DEVELOPMENT
            ? $this->devConfigurableEngineVariableClientService
            : $this->configurableEngineVariableClientService;
    }
}
