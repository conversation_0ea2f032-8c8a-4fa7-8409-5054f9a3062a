<?php

namespace App\Http\Requests\CompanyCompanyRelationship;

use App\Enums\Company\CompanyRelationshipEnum;
use App\Models\CompanyCompanyRelationship;
use App\Models\Odin\Company;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreCompanyCompanyRelationshipRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            CompanyCompanyRelationship::FIELD_TARGET_COMPANY_ID => [
                'required',
                'exists:' . Company::TABLE . ',' . Company::FIELD_ID,
                Rule::unique(CompanyCompanyRelationship::TABLE)
                    ->where(function (Builder $query) {
                        $query->where(CompanyCompanyRelationship::FIELD_TARGET_COMPANY_ID, request()->get(CompanyCompanyRelationship::FIELD_TARGET_COMPANY_ID))
                            ->where(CompanyCompanyRelationship::FIELD_RELATIONSHIP, request()->get(CompanyCompanyRelationship::FIELD_RELATIONSHIP))
                            ->whereNull(CompanyCompanyRelationship::FIELD_DELETED_AT);
                    })
            ],
            CompanyCompanyRelationship::FIELD_RELATIONSHIP => [Rule::enum(CompanyRelationshipEnum::class)]
        ];
    }
}
