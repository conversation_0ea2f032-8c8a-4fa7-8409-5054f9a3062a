<?php

namespace App\Http\Resources\CompanyCompanyRelationship;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\CompanyCompanyRelationship;
use Illuminate\Http\Request;

/**
 * @mixin CompanyCompanyRelationship
 */
class CompanyCompanyRelationshipResource extends BaseJsonResource
{
    /**
     * @param Request $request
     *
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id'             => $this->id,
            'company'        => ['id' => $this->company->id, 'name' => $this->company->name],
            'target_company' => ['id' => $this->targetCompany->id, 'name' => $this->targetCompany->name],
            'relationship'   => ['value' => $this->relationship->value, 'label' => $this->relationship->label()],
            'created_by'     => ['user_id', $this->createdBy->id, $this->createdBy->name],
            'deleted_at'     => $this->deleted_at?->toIso8601String(),
            'created_at'     => $this->created_at?->toIso8601String(),
            'updated_at'     => $this->updated_at?->toIso8601String(),
            'active'         => !$this->deleted_at,
        ];
    }
}
