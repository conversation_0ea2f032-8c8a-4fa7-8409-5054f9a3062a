<?php

namespace App\Http\Resources\CompanyCampaign;

use App\DTO\CompanyCampaignData\CompanyCampaignLeadAggregatesDTO;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\Campaigns\PauseReason;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Helpers\CarbonHelper;
use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Legacy\Location;
use App\Services\Campaigns\Modules\Budget\BudgetUsageService;
use App\Services\CompanyOptInNameService;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Support\Number;
use JsonSerializable;
use App\Repositories\LocationRepository;

/**
 * @mixin CompanyCampaign
 */
class AdminCompanyCampaignSummaryResource extends BaseCompanyCampaignSummaryResource
{
    const string PAYLOAD_DELIVERY_METHODS = 'delivery_methods';
    const string PAYLOAD_CAMPAIGN_FILTER_ENABLED = 'campaign_filter_enabled';
    const string PAYLOAD_HAS_FILTERS = 'has_filters';

    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        $lastLeadSoldAt = 'No Leads Purchased in Last 3 Months';
        $averageLeadCost = Number::currency(0) . ' (No Leads)';
        $campaignData = $this?->campaignData;
        if (!empty($campaignData)) {
            $lastLeadSoldAt = CarbonHelper::parse(
                $campaignData->payload[CompanyCampaignLeadAggregatesDTO::LEAD_LAST_SOLD_AT])->toFormat(
                format: CarbonHelper::FORMAT_BASE_TIMEZONE,
                timezone: CarbonHelper::TIMEZONE_MOUNTAIN
            );

            $leadCount = $campaignData->payload[CompanyCampaignLeadAggregatesDTO::AVERAGE_LEAD_COST_LEAD_COUNT];

            $averageLeadCost = Number::currency(
                    $campaignData->payload[CompanyCampaignLeadAggregatesDTO::AVERAGE_LEAD_COST] ?? 0
                ) . " (Last $leadCount Leads)";
        }

        return array_merge(parent::toArray($request), [
            self::PAYLOAD_LEAD_LAST_SOLD_AT                    => $lastLeadSoldAt,
            self::PAYLOAD_AVERAGE_LEAD_COST                    => $averageLeadCost,
            CompanyCampaign::FIELD_EXCLUDED_FROM_AD_AUTOMATION => $this->excluded_from_ad_automation,
            self::PAYLOAD_CAMPAIGN_FILTER_ENABLED              => $this->service->campaign_filter_enabled,
            self::PAYLOAD_HAS_FILTERS                          => $this->filters()->exists(),
            self::PAYLOAD_DELIVERY_METHODS                     => $this->getCampaignDeliveryMethodsSummary(),
            ...$this->getStateAndCountyLocations(),
            ...$this->getReactivation(),
            self::PAYLOAD_VERIFIED_BUDGET_USAGE                => $this->getVerifiedBudgetUsage()
        ]);
    }

    /**
     * @return array
     */
    private function getStateAndCountyLocations(): array
    {
        extract($this->locationModule->getStateAndCountyLocationIds());

        $counties = app(LocationRepository::class)->getLocationsWhereIn('id', $county_location_ids);

        $states = app(LocationRepository::class)->getLocationsWhereIn('id', $state_location_ids);

        $states = $states->map(function (Location $location) use ($counties) {
            return [
                ...$location->toArray(),
                'counties' => $counties->filter(fn(Location $county) => $county->state_key === $location->state_key)->values()->toArray(),
            ];
        })->toArray();

        return compact('states', 'counties');
    }

    /**
     * @return array
     */
    private function getReactivation(): array
    {
        $output = [];
        $reactivation = $this->reactivation;

        if ($reactivation) {
            if ($reactivation->reactivate_at)
                $output[CampaignReactivation::FIELD_REACTIVATE_AT] = CarbonHelper::parse($this->reactivation->reactivate_at)
                    ->toFormat(CarbonHelper::FORMAT_BASE_TIMEZONE, CarbonHelper::TIMEZONE_DENVER);
            if ($reactivation->reason)
                $output[CampaignReactivation::FIELD_REASON] = PauseReason::tryFrom($this->reactivation->reason)?->getDisplayName() ?? $this->reactivation->reason ?? null;
        }

        return $output;
    }

    /**
     * @return array
     */
    private function getCampaignDeliveryMethodsSummary(): array
    {
        return [
            'crm'   => $this->deliveryModule?->crms->count() > 0,
            'phone' => $this->deliveryModule?->contacts->where('sms_active')->count() > 0,
            'email' => $this->deliveryModule?->contacts->where('email_active')->count() > 0,
        ];
    }

    /**
     * @return array|null
     */
    protected function getVerifiedBudgetUsage(): ?array
    {
        if ($this->status !== CampaignStatus::ACTIVE) {
            return null;
        }

        $verifiedBudget = $this->budgetContainer->budgets
            ->first(fn(Budget $budget) => $budget->product_configuration === BudgetProductConfigurationEnum::LEAD_VERIFIED && $budget->status === Budget::STATUS_ENABLED);

        if (!$verifiedBudget) {
            return null;
        }

        if ($verifiedBudget->type === BudgetType::NO_LIMIT) {
            return [
                'type' => $verifiedBudget->type->value,
                'usage' => 0
            ];
        }

        return [
            'type' => $verifiedBudget->type,
            'usage' => app(BudgetUsageService::class)->getCurrentUsagePercent($verifiedBudget)
        ];
    }
}
