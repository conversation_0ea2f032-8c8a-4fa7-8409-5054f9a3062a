<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\Activitylog\Models\Activity;

/**
 * @mixin Activity
 */
class ActivityLogResource extends JsonResource
{
    const string BATCHED_LOG_DESCRIPTION = 'description';

    public function toArray($request): array
    {
        return [
            'id'                => $this->id,
            'batch_uuid'        => $this->batch_uuid ?? null,
            'batch_count'       => $this->batch_count ?? null,
            'causer'            => $this->causer,
            'subject'           => $this->subject,
            'changes'           => $this->changes,
            'properties'        => $this->properties,
            'event'             => $this->event,
            'log_name'          => $this->getLogName(),
            'log_name_original' => $this->log_name,
            'display_date'      => $this->updated_at->timezone("MST")->format("F j Y g:i A e"),
            'description'       => $this->description ?? '',
        ];
    }

    private function getLogName(): string
    {
        $logName = preg_replace("/_/", " ", $this->log_name ?? '');

        return $this->batch_uuid
            ? $logName . 's'
            : $logName;
    }
}
