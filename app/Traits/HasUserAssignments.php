<?php

namespace App\Traits;

use App\Enums\RoleType;
use App\Enums\RoundRobinType;
use App\Models\RoundRobinParticipant;
use App\Services\Roles\CompanyRoleNotificationService;
use App\Services\RoundRobins\RoundRobinService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;
use Throwable;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Models\CompanyUserRelationship;
use App\Enums\ActivityLog\ActivityLogName;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Repositories\ActivityLog\ActivityLogRepository;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

/**
 * @property-read User|null $accountManager
 * @property-read User|null $businessDevelopmentManager
 * @property-read User|null $customerSuccessManager
 * @property-read User|null $onboardingManager
 * @property-read User|null $salesDevelopmentRepresentative
 * @property-read Collection<CompanyUserRelationship> $userRelationships
 */
trait HasUserAssignments
{
    protected User $userToBeAssigned;

    public function userRelationships(): HasMany
    {
        return $this->hasMany(CompanyUserRelationship::class);
    }

    public function currentAssignment(string $role): CompanyUserRelationship|null
    {
        return $this->userRelationships()->with(['user','role'])->whereRoleId(Role::findByName($role)->id)->first();
    }

    public function assignments(): HasManyThrough
    {
        return $this->hasManyThrough(
            User::class,
            CompanyUserRelationship::class,
            'company_id',
            'id',
            'id',
            'user_id'
        );
    }

    public function currentlyAssigned(string $role): User|null
    {
        return $this->assignments()->whereRoleId(Role::findByName($role)->id)->first();
    }

    public function accountManager(): HasOneThrough
    {
        return $this->assignments()
            ->whereRoleId(Role::findByName('account-manager')->id)
            ->latest('company_user_relationships.id')
            ->one();
    }

    public function businessDevelopmentManager(): HasOneThrough
    {
        return $this->assignments()
            ->whereRoleId(Role::findByName('business-development-manager')->id)
            ->latest('company_user_relationships.id')
            ->one();
    }

    public function customerSuccessManager(): HasOneThrough
    {
        return $this->assignments()
            ->whereRoleId(Role::findByName('customer-success-manager')->id)
            ->latest('company_user_relationships.id')
            ->one();
    }

    public function salesDevelopmentRepresentative(): HasOneThrough
    {
        return $this->assignments()
            ->whereRoleId(Role::findByName('sales-development-representative')->id)
            ->latest('company_user_relationships.id')
            ->one();
    }

    public function onboardingManager(): HasOneThrough
    {
        return $this->assignments()
            ->whereRoleId(Role::findByName('onboarding-manager')->id)
            ->latest('company_user_relationships.id')
            ->one();
    }

    public function assign(User $user): self
    {
        $this->userToBeAssigned = $user;

        return $this;
    }

    /**
     * @throws Throwable
     */
    public function as(string $role, bool $notify = false, ?string $reason = null): ?CompanyUserRelationship
    {
        throw_if(blank($this->userToBeAssigned), 'You must set a user first before attempting to assign to a company');

        if ($this->userToBeAssigned->isNot($this->currentlyAssigned($role))) {
            $this->unassign(role: $role, notify: $notify, reason: $reason);

            /** @var CompanyUserRelationship $relationship */
            $relationship = $this->userRelationships()->create([
                'user_id' => $this->userToBeAssigned->id,
                'role_id' => Role::findByName($role)->id,
            ]);

            $reason = $reason ?? ActivityLogRepository::systemOrUser()->value;

            if ($notify) {
                app(CompanyRoleNotificationService::class)->notifyUser(
                    user: $relationship->user,
                    company: $relationship->company,
                    type: Str::headline($role),
                    action: 'assigned',
                    reason: $reason,
                );
            }

            app(ActivityLogRepository::class)->createActivityLog(
                ActivityLogName::COMPANY_ROLE_ASSIGNED->value,
                $reason,
                Company::class,
                $this->id,
                $relationship->toArray(),
            );

            return $relationship;
        }

        return null;
    }

    public function asAccountManager(): void
    {
        $this->as('account-manager');
    }

    public function asBusinessDevelopmentManager(bool $notify = false, ?string $reason = null): void
    {
        $this->as('business-development-manager', $notify, $reason);
    }

    public function asCustomerSuccessManager(): void
    {
        $this->as('customer-success-manager');
    }

    public function asSalesDevelopmentRepresentative(): void
    {
        $this->as('sales-development-representative');
    }

    public function asOnboardingManager(): void
    {
        $this->as('onboarding-manager');
    }

    /**
     * @param string $role
     * @param bool $notify
     * @param string|null $reason
     * @return User|null
     * @throws Throwable
     */
    public function unassign(string $role, bool $notify = false, ?string $reason = null): ?User
    {
        if ($this->hasAssignment($role)) {
            /** @var CompanyUserRelationship $relationship */
            $relationship = $this->userRelationships()->whereRoleId(Role::findByName($role)->id)->first();

            throw_unless($relationship->delete(), 'Failed to unassign ' . $role . ' for Company: ' . $this->id);

            $reason = $reason ?? ActivityLogRepository::systemOrUser()->value;

            app(ActivityLogRepository::class)->createActivityLog(
                ActivityLogName::COMPANY_ROLE_UNASSIGNED->value,
                $reason,
                Company::class,
                $this->id,
                $relationship->toArray(),
            );

            if ($notify) {
                app(CompanyRoleNotificationService::class)->notifyUser(
                    user: $relationship->user,
                    company: $relationship->company,
                    type: Str::headline($role),
                    action: 'unassigned',
                    reason: $reason,
                );
            }

            return $relationship->user;
        }

        return null;
    }

    public function unassignAccountManager(bool $notify = false, ?string $reason = null): void
    {
        $this->unassign('account-manager', $notify, $reason);
    }

    public function unassignBusinessDevelopmentManager(bool $notify = false, ?string $reason = null): void
    {
        $this->unassign('business-development-manager', $notify, $reason);
    }

    public function unassignCustomerSuccessManager(bool $notify = false, ?string $reason = null): void
    {
        $this->unassign('customer-success-manager', $notify, $reason);
    }

    public function unassignSalesDevelopmentRepresentative(bool $notify = false, ?string $reason = null): void
    {
        $this->unassign('sales-development-representative', $notify, $reason);
    }

    public function unassignOnboardingManager(bool $notify = false, ?string $reason = null): void
    {
        $this->unassign('onboarding-manager', $notify, $reason);
    }

    public function hasAssignment(string $role)
    {
        return $this->userRelationships()->whereRoleId(Role::findByName($role)->id)->exists();
    }

    public function assignViaRoundRobin(string $role, bool $notify = false, ?string $reason = null): User|null
    {
        $user = $this->determineRoundRobin($role);

        if (blank($user)) {
            Log::warning("No users found with role: $role");

            return null;
        }

        $this->assign($user)->as(
            role: $role,
            notify: $notify,
            reason: $reason
        );

        $this->updateRoundRobinCache($user, $role);

        return $user;
    }

    public function updateRoundRobinCache(User $user, string $role): void
    {
        Cache::put($this->getRoundRobinCacheKey($role), $user->id, now()->addMonth());
    }

    public function determineRoundRobin(string $role): User|null {
        $roundRobinTypeBinding = self::getRoundRobinTypeBinding($role);
        if ($roundRobinTypeBinding)
            return $this->determineRoundRobinFromParticipants($roundRobinTypeBinding, $role);

        $users = User::role($role)->get();

        if ($users->isEmpty()) {
            return null;
        }

        $previousUserId = Cache::get($this->getRoundRobinCacheKey($role));

        if (empty($previousUserId)) {
            return $users->first();
        }

        $previousUser = User::find($previousUserId);

        if (blank($previousUser)) {
            return $users->first();
        }

        $previousUserPosition = $users->search($previousUser);

        if ($previousUserPosition === false) {
            return $users->first();
        }

        return $users->get(($previousUserPosition + 1) % $users->count());
    }

    /**
     * @param RoundRobinType $type
     * @param string $role
     *
     * @return User|null
     */
    private function determineRoundRobinFromParticipants(RoundRobinType $type, string $role): User|null
    {
        $service = app(RoundRobinService::class)->setRoundRobinFilter($role);
        $nextId = $service->executeRoundRobin($type);

        return RoundRobinParticipant::query()->find($nextId)
            ?->user;
    }

    private function getRoundRobinCacheKey(string $role): string {
        return "last_assigned_user_id_$role";
    }

    public function currentBusinessContact(): CompanyUserRelationship|null {
        return $this->currentAssignment('account-manager')
        ?? $this->currentAssignment('onboarding-manager')
        ?? $this->currentAssignment('business-development-manager')
        ?? null;
    }

    /**
     * @param int $userId
     *
     * @return bool
     */
    public function preAssignAccountManager(int $userId): bool
    {
        $this->preassigned_account_manager_user_id = $userId;
        return $this->save();
    }

    /**
     * @return bool
     */
    public function removeAccountManagerPreAssignment(): bool
    {
        $this->preassigned_account_manager_user_id = null;
        return $this->save();
    }

    /**
     * @param string $roleTypeString
     * @return RoundRobinType|null
     */
    protected static function getRoundRobinTypeBinding(string $roleTypeString): RoundRobinType|null
    {
        return match ($roleTypeString) {
            RoleType::ACCOUNT_MANAGER->value, RoleType::BUSINESS_DEVELOPMENT_MANAGER->value, RoleType::ONBOARDING_MANAGER->value => RoundRobinType::ACCOUNT_ASSIGNMENT,
            default                          => null,
        };
    }
}
