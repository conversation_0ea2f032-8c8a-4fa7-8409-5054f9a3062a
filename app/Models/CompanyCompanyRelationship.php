<?php

namespace App\Models;

use App\Enums\Company\CompanyRelationshipEnum;
use App\Models\Odin\Company;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property int $company_id
 * @property int $target_company_id
 * @property int $created_by_id
 * @property CompanyRelationshipEnum $relationship
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property ?Carbon $deleted_at
 *
 * @property-read Company $company
 * @property-read Company $targetCompany
 * @property-read User $createdBy
 */
class CompanyCompanyRelationship extends Model
{
    use SoftDeletes, HasFactory, LogsActivity;

    const string TABLE = 'company_company_relationships';

    const string FIELD_ID                = 'id';
    const string FIELD_COMPANY_ID        = 'company_id';
    const string FIELD_TARGET_COMPANY_ID = 'target_company_id';
    const string FIELD_CREATED_BY_ID     = 'created_by_id';
    const string FIELD_RELATIONSHIP      = 'relationship';
    const string FIELD_CREATED_AT        = 'created_at';
    const string FIELD_UPDATED_AT        = 'updated_at';
    const string FIELD_DELETED_AT        = 'deleted_at';

    const string RELATION_COMPANY        = 'company';
    const string RELATION_TARGET_COMPANY = 'targetCompany';
    const string RELATION_CREATED_BY     = 'createdBy';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_RELATIONSHIP => CompanyRelationshipEnum::class
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function targetCompany(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_TARGET_COMPANY_ID);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_CREATED_BY_ID);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('company_company_relationship')
            ->logOnly([
                self::FIELD_COMPANY_ID,
                self::FIELD_TARGET_COMPANY_ID,
                self::FIELD_RELATIONSHIP,
                self::FIELD_CREATED_BY_ID
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
