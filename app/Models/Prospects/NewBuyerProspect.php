<?php

namespace App\Models\Prospects;

use App\Enums\Prospects\ProspectResolution;
use App\Enums\Prospects\ProspectSource;
use App\Enums\Prospects\ProspectStatus;
use App\Models\BaseModel;
use App\Models\Odin\Company;
use App\Models\SalesIntel\CompanyImportRecord;
use App\Models\User;
use App\Observers\NewBuyerProspectObserver;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $reference
 * @property ?string $external_reference
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property ?Carbon $released_at
 * @property ?int $released_by_user_id
 * @property ?int $user_id
 * @property ?int $company_id
 * @property ProspectStatus $status
 * @property ?ProspectResolution $resolution
 * @property array $industry_service_ids
 * @property ProspectSource $source
 * @property array $source_data
 * @property string $company_name
 * @property ?string $company_website
 * @property ?string $company_description
 * @property ?string $company_phone
 * @property ?string $address_street
 * @property ?string $address_city_key
 * @property ?string $address_state_abbr
 * @property ?string $decision_maker_first_name
 * @property ?string $decision_maker_last_name
 * @property ?string $decision_maker_email
 * @property ?string $decision_maker_phone
 * @property bool $decision_maker_confirmed
 * @property ?string $notes
 * @property ?int $ordinal_value
 *
 * @property-read ?Company $company
 * @property-read ?User $user
 * @property-read Collection<Contact> $contacts
 */
#[ObservedBy([NewBuyerProspectObserver::class])]
class NewBuyerProspect extends BaseModel
{
    use HasFactory;

    const string TABLE = 'new_buyer_prospects';

    const string FIELD_ID                        = 'id';
    const string FIELD_REFERENCE                 = 'reference';
    const string FIELD_EXTERNAL_REFERENCE        = 'external_reference';
    const string FIELD_CREATED_AT                = 'created_at';
    const string FIELD_UPDATED_AT                = 'updated_at';
    const string FIELD_RELEASED_AT               = 'released_at';
    const string FIELD_RELEASED_BY_USER_ID       = 'released_by_user_id';
    const string FIELD_USER_ID                   = 'user_id';
    const string FIELD_COMPANY_ID                = 'company_id';
    const string FIELD_STATUS                    = 'status';
    const string FIELD_RESOLUTION                = 'resolution';
    const string FIELD_INDUSTRY_SERVICE_IDS      = 'industry_service_ids';
    const string FIELD_SOURCE                    = 'source';
    const string FIELD_SOURCE_DATA               = 'source_data';
    const string FIELD_COMPANY_NAME              = 'company_name';
    const string FIELD_COMPANY_WEBSITE           = 'company_website';
    const string FIELD_COMPANY_DESCRIPTION       = 'company_description';
    const string FIELD_COMPANY_PHONE             = 'company_phone';
    const string FIELD_ADDRESS_STREET            = 'address_street';
    const string FIELD_ADDRESS_CITY_KEY          = 'address_city_key';
    const string FIELD_ADDRESS_STATE_ABBR        = 'address_state_abbr';
    const string FIELD_DECISION_MAKER_FIRST_NAME = 'decision_maker_first_name';
    const string FIELD_DECISION_MAKER_LAST_NAME  = 'decision_maker_last_name';
    const string FIELD_DECISION_MAKER_EMAIL      = 'decision_maker_email';
    const string FIELD_DECISION_MAKER_PHONE      = 'decision_maker_phone';
    const string FIELD_DECISION_MAKER_CONFIRMED  = 'decision_maker_confirmed';
    const string FIELD_NOTES                     = 'notes';
    const string FIELD_ORDINAL_VALUE             = 'ordinal_value';
    const string FIELD_USER_ASSIGNED_AT          = 'user_assigned_at';

    const string RELATION_CONTACTS = 'contacts';

    const string SOURCE_KEY_BUYING_LEADS        = 'buying_leads';
    const string SOURCE_KEY_CITY                = 'city';
    const string SOURCE_KEY_EMAIL               = 'email';
    const string SOURCE_KEY_ENTITY_NAME         = 'entity_name';
    const string SOURCE_KEY_FIRST_NAME          = 'first_name';
    const string SOURCE_KEY_INDUSTRIES          = 'industries';
    const string SOURCE_KEY_JOB_ROLE            = 'job_role';
    const string SOURCE_KEY_LAST_NAME           = 'last_name';
    const string SOURCE_KEY_PHONE               = 'phone';
    const string SOURCE_KEY_STATE               = 'state';
    const string SOURCE_KEY_STREET_ADDRESS      = 'street_address';
    const string SOURCE_KEY_ZIP_CODE            = 'zip_code';
    const string SOURCE_KEY_WEBSITE             = 'website';
    const string SOURCE_KEY_COMPANY_TURNOVER    = 'company_turnover';
    const string SOURCE_KEY_NUMBER_OF_EMPLOYEES = 'number_of_employees';
    const string SOURCE_KEY_USES_CRM            = 'uses_crm';
    const string SOURCE_KEY_YEARS_IN_BUSINESS   = 'years_in_business';
    const string SOURCE_KEY_WHO_CALLS_LEADS     = 'who_calls_leads';
    const string SOURCE_KEY_BUYING_LEADS_FROM   = 'buying_leads_from';
    const string SOURCE_KEY_PHONE_VERIFIED      = 'phone_verified';
    const string SOURCE_KEY_EMAIL_VERIFIED      = 'email_verified';
    const string SOURCE_KEY_STATES              = 'states';
    const string SOURCE_KEY_REGISTRATION_URL    = 'registration_url';

    const array FIELDS_REQUIRED_TO_CONVERT = [
        self::FIELD_COMPANY_NAME,
        self::FIELD_COMPANY_WEBSITE,
        self::FIELD_ADDRESS_STATE_ABBR,
        self::FIELD_DECISION_MAKER_FIRST_NAME,
        self::FIELD_DECISION_MAKER_LAST_NAME,
    ];

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts   = [
        self::FIELD_SOURCE_DATA          => 'array',
        self::FIELD_INDUSTRY_SERVICE_IDS => 'array',
        self::FIELD_SOURCE => ProspectSource::class
    ];

    /**
     * @return bool
     */
    public function isReserved(): bool
    {
        return $this->user_id && $this->user_id > 0;
    }

    public function resolve(ProspectStatus $status, ProspectResolution $resolution): void {
        $this->update([
            'status' => $status,
            'resolution' => $resolution,
        ]);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * @param string $key
     *
     * @return mixed
     */
    public function getSourceDataByKey(string $key): mixed
    {
        return Arr::get($this->source_data, $key);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @return string
     */
    public function getBDMProfileUrl(): string
    {
        return config('app.url') . "/bdm-dashboard?prospect_id=" . $this->external_reference;
    }

    public function salesIntelImportRecord() {
        return $this->morphOne(CompanyImportRecord::class, 'importable');
    }

    /**
     * @return HasMany
     */
    public function contacts() {
        return $this->hasMany(Contact::class, 'prospect_id');
    }

    /**
     * @return void
     */
    public function attemptCreateContactFromSourceData(): void
    {
        try {
            $this->contacts()->create([
                Contact::FIELD_FIRST_NAME => $this->getSourceDataByKey(self::SOURCE_KEY_FIRST_NAME),
                Contact::FIELD_LAST_NAME  => $this->getSourceDataByKey(self::SOURCE_KEY_LAST_NAME),
                Contact::FIELD_EMAIL      => $this->getSourceDataByKey(self::SOURCE_KEY_EMAIL),
                Contact::FIELD_TITLE      => $this->getSourceDataByKey(self::SOURCE_KEY_JOB_ROLE),
                Contact::FIELD_CELL_PHONE => $this->getSourceDataByKey(self::SOURCE_KEY_PHONE),
            ]);
        } catch (\Exception $e) {}
    }
}
