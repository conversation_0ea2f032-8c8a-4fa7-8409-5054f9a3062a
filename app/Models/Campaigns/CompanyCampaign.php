<?php

namespace App\Models\Campaigns;

use App\ActivityLog\Pipes\CompanyCampaign\CampaignPipe;
use App\Enums\Campaigns\CampaignExternalRelationType;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\CampaignType;
use App\Models\Alert;
use App\Models\BaseModel;
use App\Models\Billing\CampaignBillingProfile;
use App\Models\Campaigns\Modules\Bidding\CompanyCampaignBidPriceModule;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\CompanyCampaignData;
use App\Models\CompanyCampaignPropertyType;
use App\Models\CompanyOptInName;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ProductRejection;
use App\Models\Odin\PropertyType;
use App\Models\Odin\ServiceProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property int $company_id
 * @property int $product_id
 * @property int $service_id
 * @property CampaignType $type
 * @property CampaignStatus $status
 * @property string $name
 * @property string $reference
 * @property int $maximum_budget_usage
 * @property bool $zip_code_targeted
 * @property bool $uses_custom_floor_prices
 * @property bool $has_low_bids
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property ?int $active_opt_in_name_id
 * @property bool $bidding_disabled
 * @property bool $excluded_from_ad_automation
 *
 * @property-read Company $company
 * @property-read Product $product
 * @property-read IndustryService $service
 * @property-read CampaignReactivation|null $reactivation
 * @property-read Collection $modules
 * @property-read BudgetContainer $budgetContainer
 * @property-read Collection<Budget> $budgets
 * @property-read CompanyCampaignDeliveryModule $deliveryModule
 * @property-read Collection<PropertyType> $campaignPropertyTypes
 * @property-read CompanyCampaignBidPriceModule $bidPriceModule
 * @property-read Collection<CompanyCampaignRelation> $companyCampaignRelations
 * @property-read CompanyCampaignLocationModule $locationModule
 * @property-read Collection<CustomCampaignStateFloorPrice|CustomCampaignCountyFloorPrice> $customCampaignFloorPrices
 * @property-read Collection<CustomCampaignStateFloorPrice> $customCampaignStateFloorPrices
 * @property-read Collection<CustomCampaignCountyFloorPrice> $customCampaignCountyFloorPrices
 * @property-read Collection<ProductAssignment> $productAssignments
 * @property-read Collection<ProductAssignment> $soldProductAssignments
 * @property-read CompanyCampaignData $campaignData
 * @property-read ?CompanyOptInName $activeOptInName
 * @property-read ServiceProduct $serviceProduct
 * @property-read Collection<Alert> $alerts
 * @property-read Collection<CompanyCampaignFilter> $filters
 */
class CompanyCampaign extends BaseModel
{
    use SoftDeletes, LogsActivity, HasFactory;

    const string TABLE = 'company_campaigns';

    const string FIELD_ID                           = 'id';
    const string FIELD_COMPANY_ID                   = 'company_id';
    const string FIELD_PRODUCT_ID                   = 'product_id';
    const string FIELD_SERVICE_ID                   = 'service_id';
    const string FIELD_TYPE                         = 'type';
    const string FIELD_STATUS                       = 'status';
    const string FIELD_NAME                         = 'name';
    const string FIELD_REFERENCE                    = 'reference';
    const string FIELD_MAXIMUM_BUDGET_USAGE         = 'maximum_budget_usage';
    const string FIELD_ZIP_CODE_TARGETED            = 'zip_code_targeted';
    const string FIELD_USES_CUSTOM_FLOOR_PRICES     = 'uses_custom_floor_prices';
    const string FIELD_HAS_LOW_BIDS                 = 'has_low_bids';
    const string FIELD_DELETED_AT                   = 'deleted_at';
    const string FIELD_ACTIVE_OPT_IN_NAME_ID        = 'active_opt_in_name_id';
    const string FIELD_BIDDING_DISABLED             = 'bidding_disabled';
    const string FIELD_EXCLUDED_FROM_AD_AUTOMATION  = 'excluded_from_ad_automation';

    const string RELATION_COMPANY                 = 'company';
    const string RELATION_PRODUCT                 = 'product';
    const string RELATION_SERVICE                 = 'service';
    const string RELATION_REACTIVATION            = 'reactivation';
    const string RELATION_MODULES                 = 'modules';
    const string RELATION_PROPERTY_TYPES          = 'propertyTypes';
    const string RELATION_CAMPAIGN_PROPERTY_TYPES = 'campaignPropertyTypes';
    const string RELATION_LOCATION_MODULE         = 'locationModule';
    const string RELATION_BUDGET_CONTAINER        = 'budgetContainer';
    const string RELATION_BUDGETS                 = 'budgets';
    const string RELATION_DELIVERY_MODULE         = 'deliveryModule';
    const string RELATION_BID_PRICE_MODULE        = 'bidPriceModule';

    const string RELATION_COMPANY_CAMPAIGN_RELATIONS = 'companyCampaignRelations';
    const string RELATION_COMPANY_CAMPAIGN_SCHEDULES = 'companyCampaignSchedules';
    const string RELATION_CUSTOM_STATE_FLOOR_PRICES  = 'customStateFloorPrices';
    const string RELATION_CUSTOM_COUNTY_FLOOR_PRICES = 'customCountyFloorPrices';
    const string RELATION_PRODUCT_ASSIGNMENTS        = 'productAssignments';
    const string RELATION_SOLD_PRODUCT_ASSIGNMENTS   = 'soldProductAssignments';
    const string RELATION_CAMPAIGN_DATA              = 'campaignData';
    const string RELATION_BILLING_PROFILES           = 'campaignBillingProfiles';
    const string RELATION_FILTERS                    = 'filters';

    const array FILLABLE = [
        self::FIELD_COMPANY_ID,
        self::FIELD_PRODUCT_ID,
        self::FIELD_SERVICE_ID,
        self::FIELD_TYPE,
        self::FIELD_STATUS,
        self::FIELD_NAME,
        self::FIELD_REFERENCE,
        self::FIELD_MAXIMUM_BUDGET_USAGE,
        self::FIELD_ZIP_CODE_TARGETED,
        self::FIELD_HAS_LOW_BIDS,
        self::FIELD_EXCLUDED_FROM_AD_AUTOMATION,
    ];

    protected $table = self::TABLE;

    protected $fillable = self::FILLABLE;

    protected $casts = [
        self::FIELD_TYPE                        => CampaignType::class,
        self::FIELD_STATUS                      => CampaignStatus::class,
        self::FIELD_BIDDING_DISABLED            => 'boolean',
        self::FIELD_EXCLUDED_FROM_AD_AUTOMATION => 'boolean',
    ];

    protected static function booted(): void
    {
        self::addLogChange(new CampaignPipe());

        parent::booted();
    }

    /**
     * Defines the relationship for this campaign to a given company.
     *
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * Defines the relationship between this campaign and its product.
     *
     * @return HasOne
     */
    public function product(): HasOne
    {
        return $this->hasOne(Product::class, Product::FIELD_ID, self::FIELD_PRODUCT_ID);
    }

    /**
     * Defines the relationship between this campaign and its service.
     *
     * @return HasOne
     */
    public function service(): HasOne
    {
        return $this->hasOne(IndustryService::class, IndustryService::FIELD_ID, self::FIELD_SERVICE_ID);
    }

    /**
     * Defines the relationship between this campaign and its reactivation.
     *
     * @return HasOne
     */
    public function reactivation(): HasOne
    {
        return $this->hasOne(CampaignReactivation::class, CampaignReactivation::FIELD_CAMPAIGN_ID, self::FIELD_ID);
    }

    /**
     * @return HasManyThrough
     */
    public function propertyTypes(): HasManyThrough
    {
        return $this->hasManyThrough(
            PropertyType::class,
            CompanyCampaignPropertyType::class,
            CompanyCampaignPropertyType::FIELD_COMPANY_CAMPAIGN_ID,
            PropertyType::FIELD_ID,
            self::FIELD_ID,
            CompanyCampaignPropertyType::FIELD_PROPERTY_TYPE_ID
        );
    }

    /**
     * @return BelongsToMany
     */
    public function campaignPropertyTypes(): BelongsToMany
    {
        return $this->belongsToMany(
            PropertyType::class,
            CompanyCampaignPropertyType::TABLE,
            CompanyCampaignPropertyType::FIELD_COMPANY_CAMPAIGN_ID,
            CompanyCampaignPropertyType::FIELD_PROPERTY_TYPE_ID,
            self::FIELD_ID,
            PropertyType::FIELD_ID,
        );
    }

    /**
     * Defines the relationship between this campaign and its serialized modules.
     *
     * @return MorphTo
     */
    public function modules(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * @return HasOne
     */
    public function bidPriceModule(): HasOne
    {
        return $this->hasOne(CompanyCampaignBidPriceModule::class, CompanyCampaignBidPriceModule::FIELD_CAMPAIGN_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function locationModule(): HasOne
    {
        return $this->hasOne(CompanyCampaignLocationModule::class, CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID, self::FIELD_ID);
    }

    /**
     * Defines the relationship to the delivery module for this campaign.
     *
     * @return HasOne
     */
    public function deliveryModule(): HasOne
    {
        return $this->hasOne(CompanyCampaignDeliveryModule::class, CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function budgetContainer(): HasOne
    {
        return $this->hasOne(BudgetContainer::class);
    }

    /**
     * @return HasManyThrough
     */
    public function budgets(): HasManyThrough
    {
        return $this->hasManyThrough(
            Budget::class,
            BudgetContainer::class,
                BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID,
            Budget::FIELD_BUDGET_CONTAINER_ID,
            self::FIELD_ID,
            BudgetContainer::FIELD_ID,
        );
    }

    /**
     * @return Collection<ProductAssignment>
     */
    public function productAssignments(): Collection
    {
        return $this
            ->budgets()
            ->with(Budget::RELATION_PRODUCT_ASSIGNMENTS)
            ->get()
            ->pluck(Budget::RELATION_PRODUCT_ASSIGNMENTS)
            ->flatten();
    }

    /**
     * @return EloquentBuilder
     */
    public function soldProductAssignmentsQuery(): EloquentBuilder
    {
       return ProductAssignment::query()
            ->leftJoin(ProductRejection::TABLE, fn(JoinClause $join) =>
            $join->on(
                ProductRejection::TABLE .'.'. ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID,
                '=',
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID
            )->whereNull(ProductRejection::TABLE .'.'. ProductRejection::FIELD_DELETED_AT))
            ->leftJoin(ProductCancellation::TABLE, fn(JoinClause $join) =>
            $join->on(
                ProductCancellation::TABLE .'.'. ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID,
                '=',
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID
            )->whereNull(ProductCancellation::TABLE .'.'. ProductCancellation::FIELD_DELETED_AT))
            ->join(
                Budget::TABLE,
                Budget::TABLE .'.'. Budget::FIELD_ID,
                '=',
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_BUDGET_ID
            )
            ->join(
                BudgetContainer::TABLE,
                BudgetContainer::TABLE  .'.'. BudgetContainer::FIELD_ID,
                '=',
                Budget::TABLE .'.'. Budget::FIELD_BUDGET_CONTAINER_ID
            )
            ->where(
                BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID,
                '=',
                $this->id
            )
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->whereNull(ProductRejection::TABLE .'.'. ProductRejection::FIELD_ID)
            ->whereNull(ProductCancellation::TABLE .'.'. ProductCancellation::FIELD_ID)
            ->orderByDesc(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_DELIVERED_AT)
            ->select([
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID,
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_DELIVERED_AT,
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_COST,
            ]);
    }

    /**
     * @return LeadCampaign|null
     */
    public function legacyLeadCampaign() : ?LeadCampaign
    {
        $companyCampaignRelations = $this->companyCampaignRelations()
            ?->where(
                CompanyCampaignRelation::FIELD_RELATION_TYPE,
                CampaignExternalRelationType::LEGACY_LEAD_CAMPAIGN
            )
            ?->first();

        return $companyCampaignRelations?->{CompanyCampaignRelation::RELATION_COMPANY_CAMPAIGN_EXTERNAL_RELATION};
    }

    /**
     * @return HasMany
     */
    public function companyCampaignRelations(): HasMany
    {
        return $this->hasMany(CompanyCampaignRelation::class);
    }

    /**
     * @return HasMany
     */
    public function companyCampaignSchedules(): HasMany
    {
        return $this->hasMany(CompanyCampaignSchedule::class);
    }

    /**
     * @return Carbon|null
     */
    public function pausedAt(): ?Carbon
    {
        return $this->reactivation?->created_at ?? null;
    }

    /**
     * @return Carbon|null
     */
    public function unpausedAt(): ?Carbon
    {
        return $this->reactivation()->withTrashed()->first()?->deleted_at ?? null;
    }

    /**
     * @return HasMany
     */
    public function customStateFloorPrices(): HasMany
    {
        return $this->hasMany(CustomCampaignStateFloorPrice::class, CustomCampaignStateFloorPrice::FIELD_COMPANY_CAMPAIGN_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function customCountyFloorPrices(): HasMany
    {
        return $this->hasMany(CustomCampaignCountyFloorPrice::class, CustomCampaignCountyFloorPrice::FIELD_COMPANY_CAMPAIGN_ID, self::FIELD_ID);
    }

    /**
     * @return Collection
     */
    public function customFloorPrices(): Collection
    {
        return collect([
            self::RELATION_CUSTOM_STATE_FLOOR_PRICES  => $this->customStateFloorPrices(),
            self::RELATION_CUSTOM_COUNTY_FLOOR_PRICES => $this->customCountyFloorPrices(),
        ]);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                self::FIELD_TYPE,
                self::FIELD_NAME,
                self::FIELD_MAXIMUM_BUDGET_USAGE,
                self::FIELD_USES_CUSTOM_FLOOR_PRICES,
                self::FIELD_BIDDING_DISABLED,
                self::FIELD_EXCLUDED_FROM_AD_AUTOMATION,
            ])
            ->useLogName('company_campaign')
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Returns whether this campaign has zip-code targeting enabled, either on the campaign or company level
     * @return bool
     */
    public function zipCodeTargeted(): bool
    {
        return $this->zip_code_targeted
            ?: $this->company?->configuration?->unrestricted_zip_code_targeting
            ?: false;
    }

    /**
     * @return HasOne
     */
    public function campaignData(): HasOne
    {
        return $this->hasOne(CompanyCampaignData::class, CompanyCampaignData::FIELD_CAMPAIGN_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function campaignBillingProfiles(): HasMany
    {
        return $this->hasMany(
            CampaignBillingProfile::class,
            CampaignBillingProfile::FIELD_CAMPAIGN_ID,
            self::FIELD_ID
        );
    }

    /**
     * @return HasOne
     */
    public function activeOptInName(): HasOne
    {
        return $this->hasOne(CompanyOptInName::class, CompanyOptInName::FIELD_ID, self::FIELD_ACTIVE_OPT_IN_NAME_ID);
    }

    /**
     * @return ServiceProduct
     */
    public function serviceProduct(): ServiceProduct
    {
        /** @var ServiceProduct */
        return ServiceProduct::query()
            ->where([ServiceProduct::FIELD_PRODUCT_ID => $this->product_id, ServiceProduct::FIELD_INDUSTRY_SERVICE_ID => $this->service_id])
            ->first();
    }

    /**
     * @return MorphMany
     */
    public function alerts(): MorphMany
    {
        return $this->morphMany(Alert::class, 'alertable');
    }

    /**
     * @return HasMany
     */
    public function filters(): HasMany
    {
        return $this->hasMany(CompanyCampaignFilter::class);
    }

    /**
     * @return bool
     */
    public function hasActiveFilters(): bool
    {
        return $this->service->campaign_filter_enabled && $this->filters()->exists();
    }
}
