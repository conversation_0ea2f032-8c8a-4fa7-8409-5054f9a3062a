<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class NonPurchasingCompanyLocation
 *
 * @property int $id
 * @property int $company_id
 * @property int $location_id
 * @property boolean $isSetByRadius
 *
 * @property-read EloquentCompany $company
 * @property-read Location $location
 */
class LegacyNonPurchasingCompanyLocation extends LegacyModel
{
    const TABLE = 'non_purchasing_company_locations';

    const FIELD_ID               = 'id';
    const FIELD_COMPANY_ID       = 'company_id';
    const FIELD_LOCATION_ID      = 'location_id';
    const FIELD_IS_SET_BY_RADIUS = 'is_set_by_radius';

    const RELATION_COMPANY  = 'company';
    const RELATION_LOCATION = 'location';

    const STATUS_IS_SET_BY_RADIUS  = 1;
    const STATUS_NOT_SET_BY_RADIUS = 0;

    /** @var string $table */
    protected $table = self::TABLE;

    protected $primaryKey = self::FIELD_ID;

    public $timestamps = false;

    protected $fillable = [
        self::FIELD_COMPANY_ID,
        self::FIELD_LOCATION_ID,
        self::FIELD_IS_SET_BY_RADIUS
    ];

    /**
     * @return BelongsTo
     */
    function company(): BelongsTo
    {
        return $this->belongsTo(EloquentCompany::class, self::FIELD_COMPANY_ID, EloquentCompany::ID);
    }

    /**
     * @return BelongsTo
     */
    function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_LOCATION_ID, Location::ID);
    }
}
