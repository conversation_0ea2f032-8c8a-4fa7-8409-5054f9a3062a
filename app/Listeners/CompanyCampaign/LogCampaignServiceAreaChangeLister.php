<?php

namespace App\Listeners\CompanyCampaign;

use App\Events\CompanyCampaign\CompanyCampaignServiceAreaUpdatedEvent;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;

class LogCampaignServiceAreaChangeLister
{
    /**
     * Create the event listener.
     */
    public function __construct(protected CompanyCampaignRepository $companyCampaignRepository) {}

    /**
     * Handle the event.
     */
    public function handle(CompanyCampaignServiceAreaUpdatedEvent $event): void
    {
        $campaign = $this->companyCampaignRepository->find($event->companyCampaignId);
        $oldZipCodes = Location::query()
            ->whereIntegerInRaw(Location::ID, $event->oldLocations)
            ->where(Location::FIELD_TYPE, Location::TYPE_ZIP_CODE)
            ->pluck(Location::ZIP_CODE);
        $newZipCodes = $campaign->locationModule->locations->pluck(CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE);

        activity('campaign_service_area')
            ->performedOn($campaign)
            ->event('updated')
            ->withProperties([
                'attributes' => [
                    'zip_codes_added' => $newZipCodes->diff($oldZipCodes)->values(),
                    'zip_codes_deleted' => $oldZipCodes->diff($newZipCodes)->values()
                ]
            ])
            ->log('campaign-service-area-updated');
    }
}
