<?php

namespace App\Repositories\RoundRobins;

use App\Enums\RoundRobinType;
use App\Models\AccountManager;
use App\Models\Hunter;
use App\Models\HunterIndustry;
use App\Models\HunterState;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\Industry;
use App\Models\RoundRobin;
use App\Models\RoundRobinParticipant;
use App\Models\SupportOfficer;
use App\Workflows\WorkflowPayload;
use Illuminate\Database\Eloquent\Builder;

class RoundRobinRepository
{
    protected ?WorkflowPayload $workflowPayload = null;
    protected ?string $filter = null;

    /**
     * Returns the next user in the round-robin.
     *
     * @param RoundRobinType $type
     * @return int|null
     */
    public function getNextUser(RoundRobinType $type): ?int
    {
        $roundRobin = $this->getRoundRobin($type);

        return $this->filterQuery($type, $roundRobin->last_id ?? 0) ?? $this->filterQuery($type, 0);
    }

    /**
     * Returns the round-robin for a given type.
     *
     * @param RoundRobinType $type
     *
     * @return RoundRobin
     */
    public function getRoundRobin(RoundRobinType $type): RoundRobin
    {
        /** @var RoundRobin */
        return match ($type) {
            RoundRobinType::HUNTER => RoundRobin::query()->firstOrCreate([
                RoundRobin::FIELD_TYPE => $type,
                RoundRobin::FIELD_FILTER => $this->getHunterFilter()
            ], [
                RoundRobin::FIELD_LAST_ID => 0
            ]),
            RoundRobinType::ACCOUNT_ASSIGNMENT => RoundRobin::query()->firstOrCreate([
                RoundRobin::FIELD_TYPE => $type,
                RoundRobin::FIELD_FILTER => $this->filter,
            ], [
                RoundRobin::FIELD_LAST_ID => 0
            ]),
            default => RoundRobin::query()->firstOrCreate([
                RoundRobin::FIELD_TYPE => $type,
            ], [
                RoundRobin::FIELD_LAST_ID => 0
            ])
        };
    }

    public function setFilter(string $filter): static
    {
        $this->filter = $filter;

        return $this;
    }

    /**
     * Returns the base query for a given user.
     *
     * @param RoundRobinType $type
     * @return Builder
     */
    protected function getBaseQuery(RoundRobinType $type): Builder
    {
        return match ($type) {
            RoundRobinType::ACCOUNT_MANAGER => AccountManager::query()->where(AccountManager::FIELD_STATUS, AccountManager::STATUS_ACTIVE),
            RoundRobinType::SALES_TOOL      => AccountManager::query()->where(AccountManager::FIELD_INCLUDE_IN_SALES_ROUND_ROBIN, true)
                ->where(AccountManager::FIELD_STATUS, AccountManager::STATUS_ACTIVE),
            RoundRobinType::SUPPORT_OFFICER => SupportOfficer::query()->where(SupportOfficer::FIELD_INCLUDED_IN_ROUND_ROBIN, true),
            RoundRobinType::HUNTER          => $this->getHunterQuery(),
            RoundRobinType::ACCOUNT_ASSIGNMENT => $this->getParticipantsQuery($type),
        };
    }

    /**
     * @param RoundRobinType $type
     * @return Builder
     */
    protected function getParticipantsQuery(RoundRobinType $type): Builder
    {
        $roundRobin = $this->getRoundRobin($type);

        return $roundRobin->participants()
            ->getQuery()
            ->orderBy(RoundRobinParticipant::FIELD_ID);
    }

    /**
     * Returns the next id.
     *
     * @param RoundRobinType $type
     * @param int|null $index
     * @return int|null
     */
    protected function filterQuery(RoundRobinType $type, ?int $index): ?int
    {
        $query = $this->getBaseQuery($type)
                      ->where('id', '>', $index);

        return $query->first()?->id;
    }

    /**
     * Handles updating the last id of the round robin.
     *
     * @param RoundRobinType $type
     * @param int|null $index
     *
     * @return RoundRobin
     */
    public function updateRoundRobin(RoundRobinType $type, ?int $index): RoundRobin
    {
        if (!$index)
            return $this->getRoundRobin($type);

        /** @var RoundRobin */
        return match ($type) {
            RoundRobinType::HUNTER => RoundRobin::query()->updateOrCreate([
                RoundRobin::FIELD_TYPE   => $type,
                RoundRobin::FIELD_FILTER => $this->getHunterFilter()
            ], [
                RoundRobin::FIELD_LAST_ID => $index
            ]),
            RoundRobinType::ACCOUNT_ASSIGNMENT => RoundRobin::query()->updateOrCreate([
                RoundRobin::FIELD_TYPE   => $type,
                RoundRobin::FIELD_FILTER => $this->filter
            ], [
                RoundRobin::FIELD_LAST_ID => $index
            ]),
            default => RoundRobin::query()->updateOrCreate([
                RoundRobin::FIELD_TYPE => $type
            ], [
                RoundRobin::FIELD_LAST_ID => $index
            ])
        };
    }

    /**
     * @param WorkflowPayload|null $workflowPayload
     *
     * @return $this
     */
    public function setWorkflowPayload(?WorkflowPayload $workflowPayload): static
    {
        $this->workflowPayload = $workflowPayload;

        return $this;
    }

    /**
     * @param RoundRobinType $type
     * @param array $userIds
     *
     * @return bool
     */
    public function addParticipants(RoundRobinType $type, array $userIds): bool
    {
        $roundRobinId = $this->getRoundRobin($type)->id;
        $now = now();
        $upsertData = array_reduce($userIds, function($output, $userId) use ($roundRobinId, $now) {
            $output[] = [
                RoundRobinParticipant::FIELD_ROUND_ROBIN_ID => $roundRobinId,
                RoundRobinParticipant::FIELD_USER_ID        => $userId,
                RoundRobinParticipant::CREATED_AT           => $now,
            ];
            return $output;
        }, []);

        return !!RoundRobinParticipant::query()
            ->upsert($upsertData, [], []);
    }

    /**
     * @param RoundRobinType $type
     * @param array $userIds
     *
     * @return bool
     */
    public function removeParticipants(RoundRobinType $type, array $userIds): bool
    {
        $roundRobinId = $this->getRoundRobin($type)->id;
        return RoundRobinParticipant::query()
            ->where(RoundRobinParticipant::FIELD_ROUND_ROBIN_ID, $roundRobinId)
            ->whereIn(RoundRobinParticipant::FIELD_USER_ID, $userIds)
            ->delete();
    }

    /**
     * @return string|null
     */
    protected function getHunterFilter(): ?string
    {
        /** @var EloquentQuote|null $lead */
        $lead = $this->getLead();

        if (!$lead) return null;

        $stateAbbr = $lead->address->state;
        $industry  = $lead->roofing_lead ? Industry::KEY_ROOFING : Industry::KEY_SOLAR;

        return "{$stateAbbr}:{$industry}";
    }

    /**
     * @return EloquentQuote|null
     */
    protected function getLead(): ?EloquentQuote
    {
        if (!$this->workflowPayload) return null;

        $leadId = $this->workflowPayload->event->get('lead_id');

        if (!$leadId) return null;

        /** @var EloquentQuote|null */
        return EloquentQuote::query()->find($leadId);
    }

    /**
     * @return Builder
     */
    protected function getHunterQuery(): Builder
    {
        $query = Hunter::query()->where(Hunter::FIELD_INCLUDED_IN_ROUND_ROBIN, true);

        $lead = $this->getLead();

        if (!$lead) return $query;

        $stateAbbr = $lead->address->state;
        $industry  = $lead->roofing_lead ? Industry::KEY_ROOFING : Industry::KEY_SOLAR;

        $query->whereHas(Hunter::RELATION_STATES, function (Builder $query) use ($stateAbbr) {
            $query->where(HunterState::STATE_ABBR, $stateAbbr);
        });

        $query->whereHas(Hunter::RELATION_INDUSTRIES, function (Builder $query) use ($industry) {
            $query->where(HunterIndustry::INDUSTRY, $industry);
        });

        return $query;
    }
}
