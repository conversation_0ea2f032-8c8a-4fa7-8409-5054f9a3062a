<?php

namespace App\Repositories\CompanyCompanyRelationship;

use App\Enums\Company\CompanyRelationshipEnum;
use App\Models\CompanyCompanyRelationship;
use App\Models\Odin\Company;
use App\Services\ImpersonateService;
use Illuminate\Database\Eloquent\Builder;

class CompanyCompanyRelationshipRepository
{
    /**
     * @param int|null $companyId
     * @param int|null $targetCompanyId
     * @param string|null $relationship
     * @param bool|null $active
     * @return Builder
     */
    public function list(
        ?int $companyId = null,
        ?int $targetCompanyId = null,
        ?string $relationship = null,
        ?bool $active = null,
    ): Builder
    {
        $query = CompanyCompanyRelationship::query()
            ->with([CompanyCompanyRelationship::RELATION_COMPANY,
                CompanyCompanyRelationship::RELATION_TARGET_COMPANY,
                CompanyCompanyRelationship::RELATION_CREATED_BY,
            ]);

        if ($companyId) {
            $query->where(CompanyCompanyRelationship::FIELD_COMPANY_ID, $companyId);
        }

        if ($targetCompanyId) {
            $query->where(CompanyCompanyRelationship::FIELD_TARGET_COMPANY_ID, $targetCompanyId);
        }

        if ($relationship) {
            $query->where(CompanyCompanyRelationship::FIELD_RELATIONSHIP, 'like', "%{$relationship}%");
        }

        if (!is_null($active)) {
            if ($active) {
                $query->whereNull(CompanyCompanyRelationship::FIELD_DELETED_AT);
            } else {
                $query->whereNotNull(CompanyCompanyRelationship::FIELD_DELETED_AT);
            }
        }

        return $query;
    }

    public function createCompanyRelationship(Company $company, Company $targetCompany, CompanyRelationshipEnum $relationship): CompanyCompanyRelationship
    {
        return $company->companyRelationships()->create([
            CompanyCompanyRelationship::FIELD_TARGET_COMPANY_ID => $targetCompany->id,
            CompanyCompanyRelationship::FIELD_RELATIONSHIP => $relationship,
            CompanyCompanyRelationship::FIELD_CREATED_BY_ID => ImpersonateService::realUser()->id,
        ]);
    }
}
