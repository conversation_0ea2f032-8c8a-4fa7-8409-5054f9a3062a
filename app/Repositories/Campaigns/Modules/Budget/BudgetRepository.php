<?php

namespace App\Repositories\Campaigns\Modules\Budget;

use App\Enums\Campaigns\BudgetAverageCostGroupBy;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\ProductAssignment;
use App\Models\USZipCode;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class BudgetRepository
{
    const string BUDGET_ID = 'budget_id';
    const string PRODUCT_COUNT = 'product_count';
    const string AVERAGE_SPEND = 'average_spend';
    const string BUDGET_SPEND = 'budget_spend';
    const string BUDGET_VALUE = 'budget_value';
    const string BUDGET_TYPE = 'budget_type';
    const string BUDGET_USAGE = 'budget_usage';
    const array SAFE_UPDATE_PARAMS = [
        Budget::FIELD_STATUS,
        Budget::FIELD_TYPE,
        Budget::FIELD_VALUE,
        Budget::FIELD_DISPLAY_NAME,
        Budget::FIELD_LAST_MODIFIED_BY_COMPANY_USER_ID,
    ];

    /**
     * @param Budget $budget
     * @param array $budgetParams
     * @return bool
     */
    public function updateBudget(Budget $budget, array $budgetParams): bool
    {
        return $budget->update($this->getSafeUpdateParamsArray($budgetParams, $budget));
    }

    /**
     * @param CompanyCampaign $companyCampaign
     *
     * @return Budget
     */
    public function getVerifiedBudgetForCampaignOrFail(CompanyCampaign $companyCampaign): Budget
    {
        /** @var Budget */
        return $companyCampaign->budgetContainer->budgets()->where(Budget::FIELD_PRODUCT_CONFIGURATION, BudgetProductConfigurationEnum::LEAD_VERIFIED)->firstOrFail();
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @return Budget|null
     */
    public function getVerifiedBudgetForCampaign(CompanyCampaign $companyCampaign): ?Budget
    {
        /** @var ?Budget */
        return $companyCampaign->budgetContainer->budgets()->where(Budget::FIELD_PRODUCT_CONFIGURATION, BudgetProductConfigurationEnum::LEAD_VERIFIED)->first();
    }

    /**
     * Todo: This may be redundant, re-evaluate once validation lifecycle is implemented
     *
     * @param array $budgetParams
     * @param Budget $budget
     *
     * @return array
     */
    private function getSafeUpdateParamsArray(array $budgetParams, Budget $budget): array
    {
        $filteredParams = collect($budgetParams)->only(self::SAFE_UPDATE_PARAMS);

        if ($this->shouldUpdateLastModified(
            $budget->type,
            BudgetType::tryFrom($filteredParams->get(Budget::FIELD_TYPE)),
            $budget->value,
            $filteredParams->get(Budget::FIELD_VALUE)
        )) {
            $filteredParams->put(Budget::FIELD_LAST_MODIFIED_AT, Carbon::now());
            $filteredParams->put(Budget::FIELD_LAST_MODIFIED_UTC, app(BudgetRepository::class)->getCampaignUTCOffset($budget->budgetContainer->campaign));
        }

        return $filteredParams->toArray();
    }

    /**
     * @param BudgetType $oldType
     * @param BudgetType|null $newType
     * @param float $oldValue
     * @param float|null $newValue
     *
     * @return bool
     */
    private function shouldUpdateLastModified(BudgetType $oldType, ?BudgetType $newType, float $oldValue, ?float $newValue): bool
    {
        if ($newType !== null && $newType !== $oldType) return true;
        if ($newValue !== null && $newValue !== $oldValue) return true;

        return  false;
    }

    /**
     * @param array $budgetIds
     * @param Carbon|null $startDate
     * @return EloquentCollection
     */
    public function getBudgetSpend(
        array   $budgetIds,
        ?Carbon $startDate = null,
    ): EloquentCollection
    {
        $startDate = $startDate ?? now()->startOfDay();

        return ProductAssignment::query()
            ->select(
                ProductAssignment::FIELD_BUDGET_ID,
                DB::raw('SUM(' . ProductAssignment::FIELD_COST . ') AS ' . self::BUDGET_SPEND),
                DB::raw('COUNT(DISTINCT ' . ProductAssignment::FIELD_ID . ') AS ' . self::PRODUCT_COUNT),
            )
            ->whereIntegerInRaw(ProductAssignment::FIELD_BUDGET_ID, $budgetIds)
            ->where(ProductAssignment::FIELD_DELIVERED, '=', true)
            ->where(ProductAssignment::FIELD_CHARGEABLE, '=', true)
            ->where(ProductAssignment::FIELD_DELIVERED_AT, '>=', $startDate)
            ->groupBy(ProductAssignment::FIELD_BUDGET_ID)
            ->get();
    }

    /**
     * @param array $budgetIds
     * @param Carbon $cutoffDate
     * @param BudgetAverageCostGroupBy $groupBy
     * @return Collection
     */
    public function getAverageBudgetSpend(
        array                    $budgetIds,
        Carbon                   $cutoffDate,
        BudgetAverageCostGroupBy $groupBy
    ): Collection
    {
        $endDate = now();

        $timePeriodCount = match ($groupBy) {
            BudgetAverageCostGroupBy::HOUR => $endDate->diffInHours($cutoffDate),
            BudgetAverageCostGroupBy::DAY  => $endDate->diffInDays($cutoffDate),
            BudgetAverageCostGroupBy::WEEK => $endDate->diffInWeeks($cutoffDate),
        };

        $totalCampaignSpend = $this->getBudgetSpend($budgetIds, $cutoffDate);

        return $totalCampaignSpend->map(function ($item) use ($timePeriodCount) {
            return [
                self::BUDGET_ID     => $item[self::BUDGET_ID],
                self::PRODUCT_COUNT => $item[self::PRODUCT_COUNT],
                self::AVERAGE_SPEND => $item[self::BUDGET_SPEND] / $timePeriodCount
            ];
        });
    }

    /**
     * @param EloquentCollection $campaigns
     * @param EloquentCollection $getTodayBudgetSpend
     * @return EloquentCollection|Collection
     */
    public function getTodayBudgetUsage(EloquentCollection $campaigns, EloquentCollection $getTodayBudgetSpend): EloquentCollection|Collection
    {
        $budgetUsage = $getTodayBudgetSpend->map(function ($item) use ($campaigns) {

            $campaign = $campaigns->first(function ($camp) use ($item) {
                return $camp[self::BUDGET_ID] === $item[self::BUDGET_ID];
            });

            $budgetUsage = match ($campaign[self::BUDGET_TYPE]) {
                BudgetType::NO_LIMIT->value         => 0,
                BudgetType::TYPE_DAILY_UNITS->value => $this->preventDivByZero($item[self::PRODUCT_COUNT], $campaign[self::BUDGET_VALUE]),
                BudgetType::TYPE_DAILY_SPEND->value => $this->preventDivByZero($item[self::BUDGET_SPEND], $campaign[self::BUDGET_VALUE])
            };

            return [
                self::BUDGET_ID    => $item[self::BUDGET_ID],
                self::BUDGET_USAGE => $budgetUsage
            ];
        });

        return $budgetUsage;
    }

    /**
     * Returns the UTC offset for a campaign based on its service area.
     * The offset is determined from the state that contains the majority of the campaign's zip codes.
     *
     * @param CompanyCampaign $companyCampaign
     *
     * @return int
     */
    public function getCampaignUTCOffset(CompanyCampaign $companyCampaign): int
    {
        $zipCodeLocations = $companyCampaign
            ->locationModule
            ->locations()
            ->select(CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
            ->pluck(CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
            ->toArray();
        $zipCode = Location::whereIntegerInRaw(Location::ID, $zipCodeLocations)
            ->select([Location::ID, Location::STATE_ABBREVIATION, Location::ZIP_CODE, DB::raw('COUNT(*) AS total')])
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->groupBy(Location::STATE_ABBREVIATION)
            ->orderByRaw('total DESC')
            ->first();

        if (!$zipCode) {
            return 0;
        }

        $offset = USZipCode::query()
            ->where(USZipCode::FIELD_ZIP_CODE, $zipCode->zip_code)
            ->first()?->utc;

        if (!$offset) {
            return 0;
        }

        return (int) $offset;
    }

    /**
     * @param int|float $num1
     * @param int|float $num2
     * @return float
     */
    private function preventDivByZero(int|float $num1, int|float $num2): float
    {
        if ($num2 == 0) {
            $num2 = 1;
        }

        $result = (float)$num1 / (float)$num2;

        return round((float)$result, 2);
    }
}
