<?php

namespace App\Repositories\Billing;

use App\Enums\Billing\PaymentMethodServices;
use App\Enums\RoleType;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\CampaignBillingProfile;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\CompanyUserRelationship;
use App\Models\Odin\ProductAssignment;
use App\Models\User;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class BillingProfileRepository
{
    public function __construct(
        protected CompanyCampaignRepository $companyCampaignRepository,
        protected ProductAssignmentRepository $productAssignmentRepository

    )
    {

    }

    /**
     * @param PaymentMethodServices $paymentMethod
     * @param string $billingFrequencyCron
     * @param array $cronData
     * @param int $companyId
     * @param string $thresholdInDollars
     * @param string $maxAllowedChargeAttempts
     * @param int $dueInDays
     * @param int|null $createdById
     * @param int|null $updatedById
     * @param string|null $paymentGatewayClientCode
     * @param string|null $paymentGatewayPaymentMethodCode
     * @param bool $processAuto
     * @param bool|null $default
     * @param int|null $id
     * @param int|null $paymentMethodId
     * @param int|null $invoiceTemplateId
     * @return BillingProfile
     */
    public function createOrUpdate(
        PaymentMethodServices $paymentMethod,
        string $billingFrequencyCron,
        array $cronData,
        int $companyId,
        string $thresholdInDollars,
        string $maxAllowedChargeAttempts,
        int $dueInDays,
        ?int $createdById = null,
        ?int $updatedById = null,
        ?string $paymentGatewayClientCode = null,
        ?string $paymentGatewayPaymentMethodCode = null,
        ?bool $processAuto = true,
        ?bool $default = false,
        ?int $id = null,
        ?int $paymentMethodId = null,
        ?int $invoiceTemplateId = null,
        ?string $name = null
    ): BillingProfile
    {
        /** @var BillingProfile $profile */
        $profile = BillingProfile::query()->updateOrCreate([
            BillingProfile::FIELD_ID => $id,
        ], [
            BillingProfile::FIELD_CREATED_BY_ID                       => $createdById,
            BillingProfile::FIELD_PAYMENT_GATEWAY_CLIENT_CODE         => $paymentGatewayClientCode,
            BillingProfile::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE => $paymentGatewayPaymentMethodCode,
            BillingProfile::FIELD_PAYMENT_METHOD                      => $paymentMethod,
            BillingProfile::FIELD_BILLING_FREQUENCY_CRON              => $billingFrequencyCron,
            BillingProfile::FIELD_CRON_DATA                           => $cronData,
            BillingProfile::FIELD_COMPANY_ID                          => $companyId,
            BillingProfile::FIELD_THRESHOLD_IN_DOLLARS                => $thresholdInDollars,
            BillingProfile::FIELD_MAX_ALLOWED_CHARGE_ATTEMPTS         => $maxAllowedChargeAttempts,
            BillingProfile::FIELD_DEFAULT                             => $default,
            BillingProfile::FIELD_PROCESS_AUTO                        => $processAuto,
            BillingProfile::FIELD_UPDATED_BY_ID                       => $updatedById,
            BillingProfile::FIELD_PAYMENT_METHOD_ID                   => $paymentMethodId,
            BillingProfile::FIELD_DUE_IN_DAYS                         => $dueInDays,
            BillingProfile::FIELD_INVOICE_TEMPLATE_ID                 => $invoiceTemplateId,
            BillingProfile::FIELD_NAME                                => $name,
        ]);

        return $profile;
    }

    /**
     * @param string $paymentMethodCode
     * @return bool|null
     */
    public function deleteBillingProfile(string $paymentMethodCode): ?bool
    {
        /** @var BillingProfile $billingProfile */
        return $this->getBillingProfilesQuery(
            paymentMethodCode: $paymentMethodCode,
        )->delete();
    }

    /**
     * @param string $customerId
     * @param string $paymentMethod
     * @return bool
     */
    public function updateDefaultBillingProfile(string $customerId, string $paymentMethod): bool
    {
        /** @var BillingProfile $billingProfile */
        $billingProfile = $this->getBillingProfilesQuery(
            paymentMethodCode: $paymentMethod
        )->first();

        $this->getBillingProfilesQuery(
            companyId: $billingProfile->company_id
        )->update([BillingProfile::FIELD_DEFAULT => false]);

        return $billingProfile->update([BillingProfile::FIELD_DEFAULT => true]);
    }

    /**
     * @param int|null $id
     * @param int|null $companyId
     * @param string|null $paymentMethodCode
     * @param string|null $paymentMethod
     * @param int|null $campaignId
     * @param User|null $filterByUserRole
     * @param bool|null $showArchived
     * @param array|null $ids
     * @return Builder
     */
    public function getBillingProfilesQuery(
        ?int $id = null,
        ?int $companyId = null,
        ?string $paymentMethodCode = null,
        ?string $paymentMethod = null,
        ?int $campaignId = null,
        ?User $filterByUserRole = null,
        ?bool $showArchived = null,
        ?array $ids = [],
    ): Builder
    {
        $query = BillingProfile::query()
            ->with(BillingProfile::RELATION_INVOICE_TEMPLATE)
            ->select(BillingProfile::TABLE . '.*')
            ->distinct();

        if (filled($companyId)) {
            $query->where(BillingProfile::TABLE . '.' . BillingProfile::FIELD_COMPANY_ID, $companyId);
        }

        if (!is_null($showArchived)) {
            $query->whereNull(
                columns: BillingProfile::TABLE . '.' . BillingProfile::FIELD_ARCHIVED_AT,
                not    : $showArchived
            );
        }

        if (filled($id)) {
            $query->where(BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID, $id);
        }

        if (filled($paymentMethodCode)) {
            $query->where(BillingProfile::TABLE . '.' . BillingProfile::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE, $paymentMethodCode);
        }

        if (filled($paymentMethod)) {
            $query->where(BillingProfile::TABLE . '.' . BillingProfile::FIELD_PAYMENT_METHOD, $paymentMethod);
        }

        if (filled($campaignId)) {
            $query->whereHas(BillingProfile::RELATION_CAMPAIGNS, function (Builder $query) use ($campaignId) {
                return $query->where('campaign_id', $campaignId);
            });
        }

        if (filled($filterByUserRole) && empty($companyId)) {
            $isFinanceOwner = $filterByUserRole->hasRole(RoleType::FINANCE_OWNER);

            if (!$isFinanceOwner) {
                $query->join(
                    CompanyUserRelationship::TABLE,
                    CompanyUserRelationship::TABLE . '.' . CompanyUserRelationship::FIELD_COMPANY_ID,
                    BillingProfile::TABLE . '.' . BillingProfile::FIELD_COMPANY_ID
                )->where(CompanyUserRelationship::TABLE . '.' . CompanyUserRelationship::FIELD_USER_ID, $filterByUserRole->id);
            }
        }

        if (filled($ids)) {
            $query->whereIn(BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID, $ids);
        }

        return $query
            ->orderByDesc(BillingProfile::TABLE . '.' . BillingProfile::FIELD_CREATED_AT);
    }

    /**
     * @param BillingProfile $billingProfile
     * @param array $campaignIds
     * @return true
     */
    public function syncAssociatedCompanyCampaigns(BillingProfile $billingProfile, array $campaignIds): true
    {
        $currentCampaigns = $billingProfile->campaigns->pluck(CompanyCampaign::FIELD_ID)->toArray();
        $restoreCampaigns = array_intersect($currentCampaigns, $campaignIds);
        $detachCampaigns = array_diff($currentCampaigns, $campaignIds);
        $attachCampaigns = array_diff($campaignIds, $currentCampaigns);

        if (!empty($restoreCampaigns)) {
            CampaignBillingProfile::query()
                ->where(CampaignBillingProfile::FIELD_BILLING_PROFILE_ID, $billingProfile->id)
                ->whereIn(CampaignBillingProfile::FIELD_CAMPAIGN_ID, $restoreCampaigns)
                ->update(['deleted_at' => null, 'updated_at' => now()]);
        }

        if (!empty($detachCampaigns)) {
            CampaignBillingProfile::query()
                ->where(CampaignBillingProfile::FIELD_BILLING_PROFILE_ID, $billingProfile->id)
                ->whereIn(CampaignBillingProfile::FIELD_CAMPAIGN_ID, $detachCampaigns)
                ->update(['deleted_at' => now()]);
        }

        if (!empty($attachCampaigns)) {
            $billingProfile->campaigns()->attach($attachCampaigns, ['created_at' => now(), 'updated_at' => now()]);
        }

        return true;
    }


    /**
     * @param BillingProfile $billingProfile
     * @param int $campaignId
     * @return true
     */
    public function attachCompanyCampaignToBillingProfile(BillingProfile $billingProfile, int $campaignId): true
    {
        $billingProfile->campaigns()->attach($campaignId);

        return true;
    }

    /**
     * Get billing profile by campaign id or fallback by company id
     * @param int $campaignId
     * @param int $companyId
     * @return BillingProfile|null
     */
    public function getBillingProfileByCampaignId(
        int $campaignId,
        int $companyId,
    ): ?BillingProfile
    {
        /** @var ?BillingProfile */
        return $this->getBillingProfilesQuery(
            companyId : $companyId,
            campaignId: $campaignId
        )->first();
    }

    /**
     * @param BillingProfile $billingProfile
     * @return bool
     */
    public function hasDefaultBillingProfiles(BillingProfile $billingProfile): bool
    {
        return BillingProfile::query()
            ->where(BillingProfile::FIELD_COMPANY_ID, $billingProfile->{BillingProfile::FIELD_COMPANY_ID})
            ->whereNot(BillingProfile::FIELD_ID, $billingProfile->{BillingProfile::FIELD_ID})
            ->where(BillingProfile::FIELD_DEFAULT, true)
            ->exists();
    }


    /**
     * @param int $companyId
     * @return ?BillingProfile
     */
    public function getCompanyDefaultBillingProfile(int $companyId): ?BillingProfile
    {
        return BillingProfile::query()
            ->where(BillingProfile::FIELD_COMPANY_ID, $companyId)
            ->where(BillingProfile::FIELD_DEFAULT, true)
            ->first();
    }

    /**
     * Return the total of chargeable-uninvoiced leads grouped by billing profile
     * @param int|string|null $greaterOrEqualValue
     * @param bool $overThreshold
     * @return Builder
     */
    public function getTotalChargeableUninvoicedLeadsGroupedByBillingProfile(
        int|string|null $greaterOrEqualValue = null,
        bool $overThreshold = false
    ): Builder
    {
        $sub = CampaignBillingProfile::query()
            ->select([
                CampaignBillingProfile::TABLE . '.' . CampaignBillingProfile::FIELD_CAMPAIGN_ID,
                CampaignBillingProfile::TABLE . '.' . CampaignBillingProfile::FIELD_BILLING_PROFILE_ID,
            ])
            ->join(
                BillingProfile::TABLE,
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID,
                CampaignBillingProfile::TABLE . '.' . CampaignBillingProfile::FIELD_BILLING_PROFILE_ID
            )
            ->where(BillingProfile::TABLE . '.' . BillingProfile::FIELD_DEFAULT, false)
            ->whereNull(BillingProfile::TABLE . '.' . BillingProfile::FIELD_DELETED_AT)
            ->whereNull(BillingProfile::TABLE . '.' . BillingProfile::FIELD_ARCHIVED_AT)
            ->whereNull(CampaignBillingProfile::TABLE . '.' . CampaignBillingProfile::FIELD_DELETED_AT);

        $bpQuery = CampaignBillingProfile::query()
            ->select([
                CampaignBillingProfile::TABLE . '.' . CampaignBillingProfile::FIELD_CAMPAIGN_ID,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_NAME,
                DB::raw('COALESCE(sub.billing_profile_id, ' . CampaignBillingProfile::TABLE . '.' . CampaignBillingProfile::FIELD_BILLING_PROFILE_ID . ') as billing_profile_id')
            ])
            ->join(
                BillingProfile::TABLE,
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID,
                CampaignBillingProfile::TABLE . '.' . CampaignBillingProfile::FIELD_BILLING_PROFILE_ID
            )
            ->join(
                CompanyCampaign::TABLE,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID,
                CampaignBillingProfile::TABLE . '.' . CampaignBillingProfile::FIELD_CAMPAIGN_ID
            )->leftJoinSub($sub, 'sub', function ($join) {
                $join->on('sub.campaign_id', CampaignBillingProfile::TABLE . '.' . CampaignBillingProfile::FIELD_CAMPAIGN_ID);
            })
            ->where(
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_DEFAULT, true
            )
            ->whereNull(
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_DELETED_AT
            )
            ->whereNull(
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_ARCHIVED_AT
            )
            ->whereNull(
                CampaignBillingProfile::TABLE . '.' . CampaignBillingProfile::FIELD_DELETED_AT
            );

        return $this->productAssignmentRepository->getChargeableUninvoicedProductAssignmentsQuery()
            ->select([
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_THRESHOLD_IN_DOLLARS,
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_DEFAULT,
                'bp_sub.billing_profile_id AS billing_profile_id',
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID . ' AS company_id',
                DB::raw('GROUP_CONCAT(DISTINCT ' . CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID . ' ) AS campaign_ids'),
                DB::raw('SUM(' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COST . ') AS total_leads_cost_in_dollars'),
                DB::raw('COUNT(1) AS count_leads'),
            ])
            ->join(Budget::TABLE,
                Budget::TABLE . '.' . Budget::FIELD_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_BUDGET_ID,
            )
            ->join(BudgetContainer::TABLE,
                BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_ID,
                Budget::TABLE . '.' . Budget::FIELD_BUDGET_CONTAINER_ID
            )
            ->join(CompanyCampaign::TABLE,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID,
                BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID
            )
            ->joinSub($bpQuery, 'bp_sub', function ($join) {
                $join->on('bp_sub.campaign_id', CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID);
            })
            ->join(BillingProfile::TABLE,
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID,
                'bp_sub.billing_profile_id',
            )
            ->when(!is_null($greaterOrEqualValue), function ($query) use ($greaterOrEqualValue) {
                $query->havingRaw('total_leads_cost_in_dollars >= ?', [$greaterOrEqualValue]);
            })
            ->when($overThreshold, function ($query) {
                $query->havingRaw('total_leads_cost_in_dollars >= ' . BillingProfile::TABLE . '.' . BillingProfile::FIELD_THRESHOLD_IN_DOLLARS);
            })
            ->groupBy(
                'bp_sub.billing_profile_id'
            )
            ->orderBy(BillingProfile::TABLE . '.' . BillingProfile::FIELD_COMPANY_ID)
            ->orderBy(BillingProfile::TABLE . '.' . BillingProfile::FIELD_DEFAULT);
    }
}
