<?php

namespace App\Repositories\Odin;

use App\Enums\Company\CompanyAdminStatus;
use App\Enums\Company\CompanySystemStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Http\Requests\Odin\API\CompanyLocationSiloDataRequest;
use App\Models\ContractorProfile\ContractorProfile;
use App\Models\Legacy\CompanyRanking;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyNabcepCertification;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\Location;
use App\Models\Legacy\Ranking\CompanyRankingValue;
use App\Models\Legacy\Ranking\RankingCategory;
use App\Models\Legacy\Ranking\RankingOption;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyService;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Repositories\LocationRepository;
use App\Services\DatabaseHelperService;
use App\Transformers\Odin\CompanyListTransformer;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;


class CompanyListRepository
{
    const INDUSTRIES_LOGIC_ANY = 'any';
    const SERVICES_LOGIC_ANY   = 'any';

    const DEFAULT_INDUSTRIES_LOGIC = self::INDUSTRIES_LOGIC_ANY;
    const DEFAULT_SERVICES_LOGIC   = self::SERVICES_LOGIC_ANY;

    const ESTIMATED_REVENUE           = 'revenue_in_thousands';
    const EMPLOYEE_COUNT              = 'employee_count';
    const EXPERT_RATING_OVERALL_SCORE = 'expert_rating_overall_score';
    const FALLBACK_RATING             = 'google_rating';
    const FALLBACK_REVIEW_COUNT       = 'google_review_count';

    const FILTER_COMPARISON_OPERATOR = 'comparison_operator';
    const FILTER_COMPARISON_VALUE    = 'comparison_value';
    const FILTER_TIMEFRAME_IN_DAYS   = 'timeframe_in_days';

    protected bool $joinedCompanyData        = false;
    protected bool $joinedCompanyRankings    = false;
    protected bool $joinedProductAssignments = false;

    public function __construct(
        protected LocationRepository                     $locationRepository,
        protected NonPurchasingCompanyLocationRepository $nonPurchasingCompanyLocationRepository,
        protected CompanyListTransformer                 $companyLocationSiloTransformer,
        protected IndustryRepository                     $industryRepository,
        protected IndustryServiceRepository              $serviceRepository
    )
    {
    }


    /**
     * @param array $dataRequest
     * @param array|null $companyIdsByNonPurchasingLocation
     * @param array|null $companyIdsByActiveCampaignLocation
     * @param array $usedCompanyIds
     * @param Location|null $location
     * @param int|null $primaryIndustryId
     * @return array
     */
    public function getCompanyList(array $dataRequest, array $usedCompanyIds, ?array $companyIdsByNonPurchasingLocation = null, ?array $companyIdsByActiveCampaignLocation = null, ?Location $location = null, ?int $primaryIndustryId = null): array
    {
        $this->resetJoinedVariables();

        $industries                    = $dataRequest[CompanyLocationSiloDataRequest::FIELD_INDUSTRIES] ?? null;
        $services                      = $dataRequest[CompanyLocationSiloDataRequest::FIELD_SERVICES] ?? [];
        $industriesLogic               = $dataRequest[CompanyLocationSiloDataRequest::FIELD_INDUSTRIES_LOGIC] ?? self::DEFAULT_INDUSTRIES_LOGIC;
        $servicesLogic                 = $dataRequest[CompanyLocationSiloDataRequest::FIELD_SERVICES_LOGIC] ?? self::DEFAULT_SERVICES_LOGIC;
        $limit                         = $dataRequest[CompanyLocationSiloDataRequest::FIELD_TOTAL_COMPANY_LIMIT] ?? 5;
        $minimumCompanyCount           = $dataRequest[CompanyLocationSiloDataRequest::FIELD_MINIMUM_COMPANY_COUNT] ?? 5;
        $requireActiveBuyers           = $dataRequest[CompanyLocationSiloDataRequest::FIELD_REQUIRE_ACTIVE_BUYERS] ?? false;
        $requireLatestReviews          = $dataRequest[CompanyLocationSiloDataRequest::FIELD_REQUIRE_LATEST_REVIEWS] ?? false;
        $requireExpertRatingFactors    = $dataRequest[CompanyLocationSiloDataRequest::REQUEST_FIELD_REQUIRE_EXPERT_RATING_FACTORS] ?? false;
        $requireOfficeLocations        = $dataRequest[CompanyLocationSiloDataRequest::REQUEST_FIELD_REQUIRE_OFFICE_LOCATIONS] ?? false;
        $manualCompanyIds              = $dataRequest[CompanyLocationSiloDataRequest::FIELD_MANUAL_COMPANY_IDS] ?? [];
        $excludedCompanyIds            = $dataRequest[CompanyLocationSiloDataRequest::FIELD_EXCLUDED_COMPANY_IDS] ?? [];
        $excludeUsedCompanies          = $dataRequest[CompanyLocationSiloDataRequest::FIELD_EXCLUDE_USED_COMPANIES] ?? false;
        $includeActiveCampaigns        = $dataRequest[CompanyLocationSiloDataRequest::REQUIRE_ACTIVE_CAMPAIGN_SERVICE_AREA] ?? false;
        $sortingLogic                  = $dataRequest[CompanyLocationSiloDataRequest::FIELD_SORTING_LOGIC] ?? CompanyLocationSiloDataRequest::SORTING_LOGIC_COST_OF_PRODUCTS_PURCHASED;
        $requireActiveBuyersInFallback = $dataRequest[CompanyLocationSiloDataRequest::FIELD_REQUIRE_ACTIVE_BUYERS_IN_FALLBACK] ?? false;
        $requireFiltersInFallback      = $dataRequest[CompanyLocationSiloDataRequest::FIELD_REQUIRE_FILTERS_IN_FALLBACK] ?? false;
        $nameFilter                    = $dataRequest[CompanyLocationSiloDataRequest::FIELD_NAME_FILTER] ?? null;
        $showPartnersFirst             = $dataRequest[CompanyLocationSiloDataRequest::FIELD_SHOW_PARTNERS_FIRST] ?? false;
        $quoteRedirectUrl              = $dataRequest[CompanyLocationSiloDataRequest::FIELD_QUOTE_REDIRECT_URL] ?? null;
        $fixrProfileRequired           = $dataRequest[CompanyLocationSiloDataRequest::FIELD_FIXR_PROFILE_REQUIRED] ?? true;

        $industryIds        = ($industries && count($industries) > 0) ? $this->getIndustryIds($industries) : [$primaryIndustryId];
        $serviceIds         = $this->getServiceIds($services);
        $excludedCompanyIds = $this->getExcludedCompanyIds($excludedCompanyIds, $manualCompanyIds, $usedCompanyIds, $excludeUsedCompanies);

        $companyIds = [];

        $limit = max($minimumCompanyCount, $limit);

        // Adjust limit by number of manual companies
        $adjustedLimit = $limit - count($manualCompanyIds);

        // Skip if limit has been met or exceeded by manual companies
        if($adjustedLimit > 0) {
            if($includeActiveCampaigns) {
                $narrowedCompanyIds = $companyIdsByActiveCampaignLocation;

                // $companyIdsByActiveCampaignLocation inherently already has active buyers
                $requireActiveBuyers = false;

            } else {
                $narrowedCompanyIds = $companyIdsByNonPurchasingLocation;
            }

            $companyIds = $this->getMinimumCompanyIds(
                $dataRequest,
                $minimumCompanyCount,
                count($manualCompanyIds),
                $sortingLogic,
                $requireActiveBuyers,
                $narrowedCompanyIds,
                $excludedCompanyIds,
                $adjustedLimit,
                $industryIds,
                $serviceIds,
                $industriesLogic,
                $servicesLogic,
                $requireActiveBuyersInFallback,
                $requireFiltersInFallback,
                $nameFilter,
                $fixrProfileRequired,
                $showPartnersFirst,
            );
        }

        return $this->getTransformedCompanies(array_merge($manualCompanyIds, $companyIds), $requireLatestReviews, $requireExpertRatingFactors, $requireOfficeLocations, $location, $quoteRedirectUrl);
    }

    /**
     * @param Builder $query
     * @param int|null $limit
     * @return array
     */
    private function getCompanyIdsSortedByEstimatedRevenue(Builder $query, ?int $limit): array
    {
        return $this->getCompanyIdsSortedByCompanyDataField($query, self::ESTIMATED_REVENUE, $limit);
    }

    /**
     * @param Builder $query
     * @param int|null $limit
     * @return array
     */
    private function getCompanyIdsSortedByEmployeeCount(Builder $query, ?int $limit): array
    {
        return $this->getCompanyIdsSortedByCompanyDataField($query, self::EMPLOYEE_COUNT, $limit);
    }

    /**
     * @param Builder $query
     * @param int|null $limit
     * @return array
     */
    private function getCompanyIdsSortedByRatingFilter(Builder $query, ?int $limit): array
    {
        return $this->getCompanyIdsSortedByCompanyDataField($query, self::EXPERT_RATING_OVERALL_SCORE, $limit);
    }

    private function getCompanyIdsSortedByContractorRatingFilter(Builder $query, ?int $limit): array
    {
        $companies = $query->orderByDesc(ContractorProfile::TABLE.'.'.ContractorProfile::FIELD_RATING)->get();

        if($limit) {
            return $companies->take($limit)->pluck(Company::FIELD_ID)->toArray();
        }

        return $companies->pluck(Company::FIELD_ID)->toArray();
    }

    /**
     * @param Builder $query
     * @param int|null $limit
     * @return array
     */
    private function getCompanyIdsSortedByFallbackRatingFilter(Builder $query, ?int $limit): array
    {
        return $this->getCompanyIdsSortedByCompanyDataField($query, self::FALLBACK_RATING, $limit);
    }

    /**
     * @param Builder $query
     * @param string $sortingLogic
     * @param int|null $limit
     * @param bool $showPartnersFirst
     * @return array
     */
    private function getSortedCompanyIds(Builder $query, string $sortingLogic, ?int $limit, bool $showPartnersFirst): array
    {
        if ($showPartnersFirst) {
            $query->orderByRaw('field('.Company::FIELD_CONSOLIDATED_STATUS.', '.CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value.')');
        }

        switch($sortingLogic) {
            case CompanyLocationSiloDataRequest::SORTING_LOGIC_CONTRACTOR_PROFILE_EXPERT_RATING:
                return $this->getCompanyIdsSortedByContractorRatingFilter($query, $limit);
            case CompanyLocationSiloDataRequest::SORTING_LOGIC_EXPERT_RATING:
                return $this->getCompanyIdsSortedByRatingFilter($query, $limit);
            case CompanyLocationSiloDataRequest::SORTING_LOGIC_FALLBACK_RATING:
                return $this->getCompanyIdsSortedByFallbackRatingFilter($query, $limit);
            case CompanyLocationSiloDataRequest::SORTING_LOGIC_EMPLOYEE_COUNT:
                return $this->getCompanyIdsSortedByEmployeeCount($query, $limit);
            case CompanyLocationSiloDataRequest::SORTING_LOGIC_ESTIMATED_REVENUE:
                return $this->getCompanyIdsSortedByEstimatedRevenue($query, $limit);
            case CompanyLocationSiloDataRequest::SORTING_LOGIC_CONSUMER_RATING:
                $this->orderByConsumerRating($query);
                break;
            case CompanyLocationSiloDataRequest::SORTING_LOGIC_REVIEW_COUNT:
                $this->orderByReviewCount($query);
                break;
            case CompanyLocationSiloDataRequest::SORTING_LOGIC_COST_OF_PRODUCTS_PURCHASED:
                $this->orderByPurchasedLeadCost($query);
                break;
        }

        $this->limitQuery($query, $limit);

        return $query->pluck(Company::TABLE.'.'.Company::FIELD_ID)->toArray();
    }

    /**
     * @param Builder $query
     * @param array|null $dataRequest
     * @param bool $requireActiveBuyers
     * @param array $industryIds
     * @param array $serviceIds
     * @param string $industriesLogic
     * @param string $servicesLogic
     * @param string|null $nameFilter
     * @param bool $fixrProfileRequired
     * @return void
     */
    private function addFilters(
        Builder $query,
        ?array   $dataRequest,
        bool    $requireActiveBuyers,
        array   $industryIds,
        array   $serviceIds,
        string  $industriesLogic,
        string  $servicesLogic,
        ?string $nameFilter,
        bool    $fixrProfileRequired,
    ): void
    {
        if($requireActiveBuyers) {
            $this->filterActiveBuyers($query);
        }

        if($nameFilter) {
            $this->filterName($query, $nameFilter);
        }

        if($fixrProfileRequired) {
            $this->filterActiveFixrProfiles($query);
        }

        $this->filterIndustriesAndServices($query, $industryIds, $industriesLogic, $serviceIds, $servicesLogic);

        $this->filterContractors($query);

        if($dataRequest) {
            foreach(CompanyLocationSiloDataRequest::FILTER_OPTIONS as $filter) {
                $filterLogic = $this->getFilterLogicByName($dataRequest, $filter);
                if($filterLogic) {
                    switch($filter) {
                        case CompanyLocationSiloDataRequest::FIELD_EXPERT_RATING_FILTER:
                            $this->addExpertRatingFilter($query, $filterLogic);
                            break;
                        case CompanyLocationSiloDataRequest::FIELD_EMPLOYEE_COUNT_FILTER:
                            $this->addEmployeeCountFilter($query, $filterLogic);
                            break;
                        case CompanyLocationSiloDataRequest::FIELD_ESTIMATED_REVENUE_FILTER:
                            $this->addEstimatedRevenueFilter($query, $filterLogic);
                            break;
                        case CompanyLocationSiloDataRequest::FIELD_CONSUMER_RATING_FILTER:
                            $this->addConsumerRatingFilter($query, $filterLogic);
                            break;
                        case CompanyLocationSiloDataRequest::FIELD_CONSUMER_REVIEW_COUNT_FILTER:
                            $this->addConsumerReviewCountFilter($query, $filterLogic);
                            break;
                        case CompanyLocationSiloDataRequest::FIELD_COST_OF_PRODUCTS_PURCHASED_FILTER:
                            $this->addProductsPurchasedFilter($query, $filterLogic);
                            break;
                        case CompanyLocationSiloDataRequest::FIELD_FALLBACK_RATING_FILTER:
                            $this->addFallbackRatingFilter($query, $filterLogic);
                            break;
                        case CompanyLocationSiloDataRequest::FIELD_FALLBACK_REVIEW_COUNT_FILTER:
                            $this->addFallbackReviewCountFilter($query, $filterLogic);
                            break;
                    }
                }
            }
        }
    }

    /**
     * @param Builder $query
     * @param array $filterLogic
     * @return void
     */
    private function addExpertRatingFilter(Builder $query, array $filterLogic): void
    {
        $this->filterByCompanyDataField($query, self::EXPERT_RATING_OVERALL_SCORE, $filterLogic);
    }

    /**
     * @param Builder $query
     * @param array $filterLogic
     * @return void
     */
    private function addEmployeeCountFilter(Builder $query, array $filterLogic): void
    {
        $this->filterByCompanyDataField($query, self::EMPLOYEE_COUNT, $filterLogic);
    }

    /**
     * @param Builder $query
     * @param array $filterLogic
     * @return void
     */
    private function addFallbackRatingFilter(Builder $query, array $filterLogic): void
    {
        $this->filterByCompanyDataField($query, self::FALLBACK_RATING, $filterLogic);
    }

    /**
     * @param Builder $query
     * @param array $filterLogic
     * @return void
     */
    private function addFallbackReviewCountFilter(Builder $query, array $filterLogic): void
    {
        $this->filterByCompanyDataField($query, self::FALLBACK_REVIEW_COUNT, $filterLogic);
    }

    /**
     * @param Builder $query
     * @param array $filterLogic
     * @return void
     */
    private function addEstimatedRevenueFilter(Builder $query, array $filterLogic): void
    {
        $this->filterByCompanyDataField($query, self::ESTIMATED_REVENUE, $filterLogic);
    }

    /**
     * @param Builder $query
     * @param array $filterLogic
     * @return void
     */
    private function addConsumerRatingFilter(Builder $query, array $filterLogic): void
    {
        $this->filterByConsumerReviews($query, $filterLogic, CompanyRanking::BAYESIAN_ALL_TIME);
    }

    /**
     * @param Builder $query
     * @param array $filterLogic
     * @return void
     */
    private function addConsumerReviewCountFilter(Builder $query, array $filterLogic): void
    {
        $this->filterByConsumerReviews($query, $filterLogic, CompanyRanking::REVIEW_COUNT);
    }

    /**
     * @param Builder $query
     * @param array $filterLogic
     * @return void
     */
    private function addProductsPurchasedFilter(Builder $query, array $filterLogic): void
    {
        $this->joinProductAssignmentsOnCompanies($query);

        $query->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED_AT, '>', Carbon::now()->subDays($filterLogic[self::FILTER_TIMEFRAME_IN_DAYS]))
            ->havingRaw('SUM('.ProductAssignment::FIELD_COST.') '. $filterLogic[self::FILTER_COMPARISON_OPERATOR] . ' ' . $filterLogic[self::FILTER_COMPARISON_VALUE])
            ->groupBy(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID);
    }

    /**
     * @param Builder $query
     * @param string $dataField
     * @param array $filterLogic
     * @return void
     */
    private function filterByCompanyDataField(Builder $query, string $dataField, array $filterLogic): void
    {
        $this->joinCompanyDataOnCompanies($query);

        $query->whereHas(Company::RELATION_DATA, function($has) use ($dataField, $filterLogic) {
            $has->whereNotNull(CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->$dataField")
                ->where(
                    CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->$dataField",
                    $filterLogic[self::FILTER_COMPARISON_OPERATOR],
                    $filterLogic[self::FILTER_COMPARISON_VALUE]
                );
        });
    }

    /**
     * @param Builder $query
     * @param array $filterLogic
     * @param string $reviewColumn
     * @return void
     */
    private function filterByConsumerReviews(Builder $query, array $filterLogic, string $reviewColumn): void
    {
        $this->joinCompanyRankingsOnLegacyCompanies($query);

        $query->where(
            DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE.'.'.$reviewColumn,
            $filterLogic[self::FILTER_COMPARISON_OPERATOR],
            $filterLogic[self::FILTER_COMPARISON_VALUE]
        );
    }

    /**
     * @param array $dataRequest
     * @param string $filterName
     * @return array|null
     */
    private function getFilterLogicByName(array $dataRequest, string $filterName): ?array
    {
        $filter = $dataRequest[$filterName] ?? null;

        if($filter) {
            $filterLogic = explode("|", $filter);

            if(count($filterLogic) === 2) {
                return [
                    self::FILTER_COMPARISON_OPERATOR => $filterLogic[0],
                    self::FILTER_COMPARISON_VALUE    => $filterLogic[1],
                ];
            } else if(count($filterLogic) === 3) {
                return [
                    self::FILTER_COMPARISON_OPERATOR => $filterLogic[0],
                    self::FILTER_COMPARISON_VALUE    => $filterLogic[1],
                    self::FILTER_TIMEFRAME_IN_DAYS   => $filterLogic[2],
                ];
            }
        }

        return null;
    }

    /**
     * @param array $industries
     * @return array
     */
    private function getIndustryIds(array $industries): array
    {
        if(count($industries) > 0) {
            return $this->industryRepository->getIndustryIdsBySlugs($industries);
        }

        return [];
    }

    /**
     * @param array $services
     * @return array
     */
    private function getServiceIds(array $services): array
    {
        if(count($services) > 0) {
            return $this->serviceRepository->getIndustryServiceIdsBySlugs($services);
        }

        return [];
    }

    /**
     * @param array $excludedCompanyIds
     * @param array $manualCompanyIds
     * @param array $usedCompanyIds
     * @param bool $excludeUsedCompanies
     * @return array
     */
    private function getExcludedCompanyIds(array $excludedCompanyIds, array $manualCompanyIds, array $usedCompanyIds, bool $excludeUsedCompanies): array
    {
        $excludedCompanyIds   = array_merge($excludedCompanyIds, $manualCompanyIds);

        if($excludeUsedCompanies) {
            $excludedCompanyIds = array_merge($usedCompanyIds, $excludedCompanyIds);
        }

        return $excludedCompanyIds;
    }

    /**
     * @param bool $requireActiveBuyers
     * @param array|null $narrowedCompanyIds
     * @param array $excludedCompanyIds
     * @param array|null $dataRequest
     * @param int $adjustedLimit
     * @param array $industryIds
     * @param array $serviceIds
     * @param string $industriesLogic
     * @param string $servicesLogic
     * @param string $sortingLogic
     * @param string|null $nameFilter
     * @param bool $fixrProfileRequired
     * @param bool $showPartnersFirst
     * @return array
     */
    private function getCompanyIds(
        bool   $requireActiveBuyers,
        ?array $narrowedCompanyIds,
        array $excludedCompanyIds,
        ?array  $dataRequest,
        int    $adjustedLimit,
        array  $industryIds,
        array  $serviceIds,
        string $industriesLogic,
        string $servicesLogic,
        string $sortingLogic,
        ?string $nameFilter,
        bool $fixrProfileRequired = false,
        bool $showPartnersFirst = false,
    ): array
    {
        $query = $this->getFilteredCompanyQuery(
            $requireActiveBuyers,
            $narrowedCompanyIds,
            $excludedCompanyIds,
            $dataRequest,
            $industryIds,
            $serviceIds,
            $industriesLogic,
            $servicesLogic,
            $nameFilter,
            $fixrProfileRequired,
        );

        return $this->getSortedCompanyIds($query, $sortingLogic, $adjustedLimit, $showPartnersFirst);
    }

    /**
     * @param array $dataRequest
     * @param int $minimumCompanyCount
     * @param int $manualCompanyCount
     * @param string $sortingLogic
     * @param bool $requireActiveBuyers
     * @param array|null $narrowedCompanyIds
     * @param array $excludedCompanyIds
     * @param int $adjustedLimit
     * @param array $industryIds
     * @param array $serviceIds
     * @param string $industriesLogic
     * @param string $servicesLogic
     * @param bool $requireActiveBuyersInFallback
     * @param bool $requireFiltersInFallback
     * @param string|null $nameFilter
     * @param bool $fixrProfileRequired
     * @param bool $showPartnersFirst
     * @return array
     */
    private function getMinimumCompanyIds(
        array $dataRequest,
        int    $minimumCompanyCount,
        int    $manualCompanyCount,
        string $sortingLogic,
        bool   $requireActiveBuyers,
        ?array  $narrowedCompanyIds,
        array  $excludedCompanyIds,
        int    $adjustedLimit,
        array  $industryIds,
        array  $serviceIds,
        string $industriesLogic,
        string $servicesLogic,
        bool $requireActiveBuyersInFallback,
        bool $requireFiltersInFallback,
        ?string $nameFilter,
        bool $fixrProfileRequired,
        bool $showPartnersFirst,
    ): array
    {
        $companyIds = $this->getCompanyIds(
            $requireActiveBuyers,
            $narrowedCompanyIds,
            $excludedCompanyIds,
            $dataRequest,
            $adjustedLimit,
            $industryIds,
            $serviceIds,
            $industriesLogic,
            $servicesLogic,
            $sortingLogic,
            $nameFilter,
            $fixrProfileRequired,
            $showPartnersFirst,
        );

        // If haven't met minimum company count, use fallback sorting logic and adjust limit.
        if((count($companyIds) + $manualCompanyCount) < $minimumCompanyCount) {
            $adjustedLimit = $adjustedLimit - count($companyIds);

            if($adjustedLimit > 0) {
                $narrowedCompanyIds = $narrowedCompanyIds ? array_diff($narrowedCompanyIds, $companyIds) : null;
                $excludedCompanyIds = array_merge($companyIds, $excludedCompanyIds);

                $this->resetJoinedVariables();

                $fallbackCompanyIds = $this->getCompanyIds(
                    $requireActiveBuyersInFallback,
                    $narrowedCompanyIds,
                    $excludedCompanyIds,
                    $requireFiltersInFallback ? $dataRequest : null,
                    $adjustedLimit,
                    $industryIds,
                    $serviceIds,
                    $industriesLogic,
                    $servicesLogic,
                    $sortingLogic,
                    $nameFilter,
                    $fixrProfileRequired,
                );

                $companyIds = array_merge($companyIds, $fallbackCompanyIds);
            }
        }

        // If still haven't met minimum company count, get narrowed company ids based on adjust limit.
        if((count($companyIds) + $manualCompanyCount) < $minimumCompanyCount) {
            $adjustedLimit = $adjustedLimit - count($companyIds);
            $narrowedCompanyIds = $narrowedCompanyIds ? array_diff($narrowedCompanyIds, $companyIds) : null;

            if($adjustedLimit > 0 && $narrowedCompanyIds) {
                $fallbackCompanyIds = array_slice($narrowedCompanyIds, 0, $adjustedLimit);
                $companyIds         = array_merge($companyIds, $fallbackCompanyIds);
            }
        }

        return $companyIds;
    }

    /**
     * @param Builder $query
     * @param string $dataField
     * @param int|null $limit
     * @return array
     */
    private function getCompanyIdsSortedByCompanyDataField(Builder $query, string $dataField, ?int $limit): array
    {
        $query->select([
            Company::TABLE . '.' . Company::FIELD_ID,
            CompanyData::TABLE . '.' . CompanyData::FIELD_PAYLOAD
        ]);

        $this->joinCompanyDataOnCompanies($query);

        $companies = $query->get()
            ->sortByDesc(function($company) use ($dataField) {
                return $company->payload ? json_decode($company->payload)->{$dataField} ?? -1 : -1;
            });

        if($limit) {
            return $companies->take($limit)->pluck(Company::FIELD_ID)->toArray();
        }

        return $companies->pluck(Company::FIELD_ID)->toArray();
    }

    /**
     * @param Builder $query
     * @return void
     */
    private function joinCompanyDataOnCompanies(Builder $query): void
    {
        if(!$this->joinedCompanyData) {
            $query->join(
                CompanyData::TABLE,
                CompanyData::TABLE.'.'.CompanyData::FIELD_COMPANY_ID,
                '=',
                Company::TABLE.'.'.Company::FIELD_ID
            );

            $this->joinedCompanyData = true;
        }
    }

    /**
     * @param bool $requireActiveBuyers
     * @param array|null $narrowedCompanyIds
     * @param array $excludedCompanyIds
     * @param array|null $dataRequest
     * @param array $industryIds
     * @param array $serviceIds
     * @param string $industriesLogic
     * @param string $servicesLogic
     * @param string|null $nameFilter
     * @param bool $fixrProfileRequired
     * @return Builder
     */
    private function getFilteredCompanyQuery(
        bool   $requireActiveBuyers,
        ?array $narrowedCompanyIds,
        array $excludedCompanyIds,
        ?array  $dataRequest,
        array  $industryIds,
        array  $serviceIds,
        string $industriesLogic,
        string $servicesLogic,
        ?string $nameFilter,
        bool $fixrProfileRequired,
    ): Builder
    {
        $query = Company::query()
            ->select(Company::TABLE.'.'.Company::FIELD_ID)
            ->whereNotIn(Company::TABLE.'.'.Company::FIELD_ADMIN_STATUS, [
                CompanyAdminStatus::ADMIN_LOCKED,
                CompanyAdminStatus::ARCHIVED,
                CompanyAdminStatus::COLLECTIONS,
            ])
            ->whereNotIn(Company::TABLE.'.'.Company::FIELD_SYSTEM_STATUS, [
                CompanySystemStatus::SUSPENDED_PAYMENT
            ]);

        $this->joinContractorProfile($query);

        $this->joinLegacyCompaniesOnCompanies($query);

        if($narrowedCompanyIds) {
            $query->whereIn(Company::TABLE.'.'.Company::FIELD_ID, $narrowedCompanyIds);
        }

        if(count($excludedCompanyIds) > 0) {
            $query->whereNotIn(Company::TABLE.'.'.Company::FIELD_ID, $excludedCompanyIds);
        }

        $this->addFilters($query, $dataRequest, $requireActiveBuyers, $industryIds, $serviceIds, $industriesLogic, $servicesLogic, $nameFilter, $fixrProfileRequired);

        return $query;
    }


    /**
     * @param Builder $query
     * @param array $industryIds
     * @param string $industriesLogic
     * @param array $serviceIds
     * @param string $servicesLogic
     * @return void
     */
    private function filterIndustriesAndServices(Builder $query, array $industryIds, string $industriesLogic, array $serviceIds, string $servicesLogic): void
    {
        if(count($industryIds) > 0) {
            if ($industriesLogic === self::INDUSTRIES_LOGIC_ANY || count($industryIds) === 1) {
                $this->joinCompanyIndustriesOnCompanies($query);
                $query->whereIn(CompanyIndustry::TABLE.'.'.CompanyIndustry::FIELD_INDUSTRY_ID, $industryIds);
            } else {
                $query->whereHas(Company::RELATION_INDUSTRIES, function (Builder $query) use ($industryIds) {
                    $query->whereIn(CompanyIndustry::TABLE.'.'.CompanyIndustry::FIELD_INDUSTRY_ID, $industryIds);
                }, '=', count($industryIds));
            }
        }

        if(count($serviceIds) > 0) {
            if ($servicesLogic === self::SERVICES_LOGIC_ANY || count($serviceIds) === 1) {
                $this->joinCompanyServicesOnCompanies($query);
                $query->whereIn(CompanyService::TABLE.'.'.CompanyService::FIELD_INDUSTRY_SERVICE_ID, $serviceIds);
            } else {
                $query->whereHas(Company::RELATION_SERVICES, function (Builder $query) use ($serviceIds) {
                    $query->whereIn(CompanyService::TABLE.'.'.CompanyService::FIELD_INDUSTRY_SERVICE_ID, $serviceIds);
                }, '=', count($serviceIds));
            }
        }
    }

    /**
     * @param Builder $query
     * @return void
     */
    private function filterContractors(Builder $query): void
    {
        $query->whereIn(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::TYPE, [
            EloquentCompany::TYPE_INSTALLER, EloquentCompany::TYPE_ROOFER, EloquentCompany::TYPE_MULTI
        ]);
    }

    /**
     * @param Builder $query
     * @return void
     */
    private function filterActiveBuyers(Builder $query): void
    {
        $query->where(Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value);
    }

    /**
     * @param Builder $query
     * @param string $nameFilter
     * @return void
     */
    private function filterName(Builder $query, string $nameFilter): void
    {
        $query->where(Company::TABLE.'.'.Company::FIELD_NAME, 'LIKE', $nameFilter);
    }

    /**
     * @param Builder $query
     * @return void
     */
    private function filterActiveFixrProfiles(Builder $query): void
    {
        $query->where(ContractorProfile::TABLE.'.'.ContractorProfile::FIELD_STATUS, true);
    }


    /**
     * @param Builder $query
     * @param int|null $limit
     * @return void
     */
    private function limitQuery(Builder $query, ?int $limit): void
    {
        if(!is_null($limit)) {
            $query->limit($limit);
        }
    }

    /**
     * @param Builder $query
     * @return void
     */
    private function orderByConsumerRating(Builder $query): void
    {
        $this->joinCompanyRankingsOnLegacyCompanies($query);

        $query->orderByDesc(DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE.'.'.CompanyRanking::BAYESIAN_ALL_TIME);
    }

    /**
     * @param Builder $query
     * @return void
     */
    private function orderByReviewCount(Builder $query): void
    {
        $this->joinCompanyRankingsOnLegacyCompanies($query);

        $query->orderByDesc(DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE.'.'.CompanyRanking::REVIEW_COUNT);
    }

    /**
     * @param Builder $query
     * @return void
     */
    private function orderByPurchasedLeadCost(Builder $query): void
    {
        $this->joinProductAssignmentsOnCompanies($query);

        $query->orderByRaw('SUM(' . ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COST . ') desc');
    }

    /**
     * @param Builder $query
     * @return void
     */
    private function joinCompanyIndustriesOnCompanies(Builder $query): void
    {
        $query->join(
            CompanyIndustry::TABLE,
            Company::TABLE.'.'.Company::FIELD_ID,
            '=',
            CompanyIndustry::TABLE.'.'.CompanyIndustry::FIELD_COMPANY_ID
        );
    }

    /**
     * @param Builder $query
     * @return void
     */
    private function joinCompanyServicesOnCompanies(Builder $query): void
    {
        $query->join(
            CompanyService::TABLE,
            Company::TABLE.'.'.Company::FIELD_ID,
            '=',
            CompanyService::TABLE.'.'.CompanyService::FIELD_COMPANY_ID
        );
    }

    /**
     * @param Builder $query
     * @return void
     */
    private function joinProductAssignmentsOnCompanies(Builder $query): void
    {

        if(!$this->joinedProductAssignments) {
            $query->join(
                ProductAssignment::TABLE,
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID,
                '=',
                Company::TABLE.'.'.Company::FIELD_ID
            );

            $this->joinedProductAssignments = true;
        }
    }

    /**
     * @param Builder $query
     * @return void
     */
    private function joinLegacyCompaniesOnCompanies(Builder $query): void
    {
        $query->join(
            DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE,
            DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID,
            '=',
            Company::TABLE.'.'.Company::FIELD_LEGACY_ID
        );
    }

    /**
     * @param Builder $query
     * @return void
     */
    private function joinCompanyRankingsOnLegacyCompanies(Builder $query): void
    {
        if(!$this->joinedCompanyRankings) {
            $query->join(
                DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE,
                DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE.'.'.CompanyRanking::COMPANY_ID,
                '=',
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID,
            );

            $this->joinedCompanyRankings = true;
        }
    }

    /**
     * @return array
     */
    private function getRelationsArray(): array
    {
        return [
            Company::RELATION_DATA => function ($with) {
                $with->select([
                    CompanyData::FIELD_COMPANY_ID,
                    CompanyData::FIELD_PAYLOAD,
                ]);
            },
            Company::RELATION_SERVICES,
            Company::RELATION_SERVICES .'.'. IndustryService::RELATION_INDUSTRY,
            Company::RELATION_LOCATIONS => function($with) {
                $with->select([
                    CompanyLocation::FIELD_COMPANY_ID,
                    CompanyLocation::FIELD_ADDRESS_ID,
                    CompanyLocation::FIELD_PHONE,
                ]);
            },
            Company::RELATION_PRIMARY_LOCATION.'.'.CompanyLocation::RELATION_ADDRESS.'.'.Address::RELATION_ZIPCODE,
            Company::RELATION_LOCATIONS.'.'.CompanyLocation::RELATION_ADDRESS,
            Company::RELATION_LEGACY_COMPANY => function($with) {
                $with->select([
                    EloquentCompany::ID,
                    EloquentCompany::TYPE,
                    EloquentCompany::LINK,
                    EloquentCompany::DESCRIPTION,
                    EloquentCompany::LINK_TO_LOGO,
                ]);
            },
            Company::RELATION_LEGACY_COMPANY.'.'.EloquentCompany::RELATION_COMPANY_RANKING,
            Company::RELATION_LEGACY_COMPANY.'.'.EloquentCompany::RELATION_COMPANY_RANKING_VALUE => function($with) {
                $with->select([
                    CompanyRankingValue::FIELD_RANKING_CATEGORY_ID,
                    CompanyRankingValue::FIELD_RANKING_VALUE,
                    CompanyRankingValue::FIELD_COMPANY_ID,
                ]);
            },
            Company::RELATION_LEGACY_COMPANY.'.'.EloquentCompany::RELATION_COMPANY_RANKING_VALUE.'.'.CompanyRankingValue::RELATION_RANKING_CATEGORY => function($with) {
                $with->select([
                    RankingCategory::FIELD_ID,
                    RankingCategory::FIELD_DISPLAY_NAME
                ]);
            },
            Company::RELATION_LEGACY_COMPANY.'.'.EloquentCompany::RELATION_COMPANY_RANKING_VALUE.'.'.CompanyRankingValue::RELATION_RANKING_CATEGORY.'.'.RankingCategory::RELATION_RANKING_OPTIONS => function($with) {
                $with->select([
                    RankingOption::FIELD_CATEGORY_ID,
                    RankingOption::FIELD_POINT_VALUE
                ]);
            },
            Company::RELATION_LEGACY_COMPANY.'.'.EloquentCompany::RELATION_COMPANY_NABCEP_CERTIFICATIONS  => function($with) {
                $with->select([
                    EloquentCompanyNabcepCertification::COMPANY_ID,
                    EloquentCompanyNabcepCertification::EXPIRED_AT,
                    EloquentCompanyNabcepCertification::CERTIFICATION_TYPE
                ]);
            },
            Company::RELATION_ACTIVE_COMPANY_SLUG,
            Company::RELATION_CONTRACTOR_PROFILE => function($with) {
                $with->select([
                    ContractorProfile::FIELD_STATUS,
                    ContractorProfile::FIELD_INTRODUCTION,
                    ContractorProfile::FIELD_RATING,
                ]);
            }
        ];
    }

    /**
     * @param array $companyIds
     * @param bool $requireLatestReviews
     * @param bool $requireExpertRatingFactors
     * @param bool $requireOfficeLocations
     * @param Location|null $location
     * @param string|null $quoteRedirectLink
     * @return array
     */
    public function getTransformedCompanies(
        array     $companyIds,
        bool      $requireLatestReviews = false,
        bool      $requireExpertRatingFactors = false,
        bool      $requireOfficeLocations = false,
        ?Location $location = null,
        ?string   $quoteRedirectLink = null,
    ): array
    {
        $companies = Company::with($this->getRelationsArray())
            ->whereIn(Company::FIELD_ID, $companyIds)
            ->get();

        return $this->companyLocationSiloTransformer->transformCompanies($companies, $companyIds, $requireLatestReviews, $requireExpertRatingFactors, $requireOfficeLocations, $location, $quoteRedirectLink);
    }

    /**
     * @param int|null $locationId
     * @return ?array
     */
    public function getCompanyIdsByNonPurchasingLocation(?int $locationId): ?array
    {
        if($locationId) {
            return $this->nonPurchasingCompanyLocationRepository->getCompanyIdsByLocation($locationId)->toArray();
        }

        return null;
    }

    /**
     * This returns active product buyers with an active campaign in the given location
     *
     * @param int|null $locationId
     * @return ?array
     */
    public function getCompanyIdsByActiveCampaignLocation(?int $locationId): ?array
    {
        if ($locationId) {
            $locationIds = $this->locationRepository->getLocationIdsByParentLocationId($locationId);

            return Company::query()
                ->select(Company::TABLE . '.' . Company::FIELD_ID)
                ->join(
                    DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE,
                    Company::TABLE . '.' . Company::FIELD_LEGACY_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID)
                ->join(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignLocation::TABLE,
                    DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LEAD_CAMPAIGN_ID
                )
                ->where(Company::TABLE . '.' . Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value)
                ->where(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE)
                ->whereIn(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LOCATION_ID, $locationIds)
                ->groupBy(Company::TABLE . '.' . Company::FIELD_ID)
                ->pluck(Company::FIELD_ID)
                ->toArray();
        }

        return null;
    }

    /**
     * @return void
     */
    private function resetJoinedVariables(): void
    {
        $this->joinedCompanyRankings    = false;
        $this->joinedProductAssignments = false;
        $this->joinedCompanyData        = false;
    }

    private function joinContractorProfile(Builder $query): void
    {
        $query->leftJoin(ContractorProfile::TABLE, ContractorProfile::TABLE.'.'.ContractorProfile::FIELD_COMPANY_ID, '=', Company::TABLE.'.'.Company::FIELD_ID);
    }

}

