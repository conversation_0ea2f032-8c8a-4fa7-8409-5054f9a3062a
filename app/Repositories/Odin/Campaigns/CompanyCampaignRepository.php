<?php

namespace App\Repositories\Odin\Campaigns;

use App\Builders\Odin\CompanyCampaignBuilder;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\CampaignType;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\Odin\Industry;
use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Enums\RejectionReasons;
use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\BaseCompanyCampaignModule;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\CompanyOptInName;
use App\Models\ComputedRejectionStatistic;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Services\ProductRejectionCalculationService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Odin\Product as ProductModel;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;

class CompanyCampaignRepository
{
    const string CONFIG_KEY_LEAD                = 'lead';
    const string CONFIG_KEY_APPOINTMENT         = 'appointment';
    const string CONFIG_KEY_DIRECT_LEADS        = 'direct leads';
    const string CONFIG_KEY_PROPERTY_TYPES      = 'property_types';
    const string CONFIG_KEY_BUDGET_TYPES        = 'budget_types';
    const string CONFIG_KEY_BUDGETS             = 'budgets';
    const string CONFIG_KEY_BUDGET_OPTIONS      = 'budget_options';
    const string CONFIG_KEY_REJECTION_REASONS   = 'rejection_reasons';
    const string CONFIG_KEY_CAN_REJECT          = 'can_reject';
    const string CONFIG_OPTION_LINK_BUDGETS     = 'link_optional_budget_types';// controls whether optional budgets follow the type of required budget
    const string CONFIG_OPTION_SHOW_PRICING     = 'show_pricing';
    const string CONFIG_OPTION_MINIMUM_PRODUCTS = 'minimum_daily_products';
    const string CONFIG_OPTION_MINIMUM_SPEND    = 'minimum_daily_spend';

    public function __construct(protected ProductAssignmentRepository $productAssignmentRepository)
    {

    }

    /**
     * @param Company $company
     * @param int $productId
     * @param int $industryServiceId
     * @param string $campaignName
     * @param CampaignStatus $campaignStatus
     * @param array $propertyTypes
     * @param CompanyCampaign|null $campaign
     * @param bool|null $zipCodeTargeted
     * @param int|null $campaignTypeValue
     * @return CompanyCampaign
     */
    public function updateOrCreate(
        Company          $company,
        int              $productId,
        int              $industryServiceId,
        string           $campaignName,
        CampaignStatus   $campaignStatus,
        array            $propertyTypes,
        ?CompanyCampaign $campaign,
        ?bool            $zipCodeTargeted = false,
        ?int             $campaignTypeValue = null,
    ): CompanyCampaign
    {
        if ($campaign) {
            $campaign->update([
                CompanyCampaign::FIELD_STATUS => $campaignStatus,
                CompanyCampaign::FIELD_NAME   => $campaignName,
            ]);
        }
        else {
            if ($campaignTypeValue !== null)
                $campaignType = CampaignType::tryFrom($campaignTypeValue);
            $campaignType = $campaignType
                ?? CampaignType::fromProductAndServiceId($productId, $industryServiceId);

            /** @var CompanyCampaign $campaign */
            $campaign = CompanyCampaign::query()->create(
                [
                    CompanyCampaign::FIELD_COMPANY_ID        => $company->{Company::FIELD_ID},
                    CompanyCampaign::FIELD_STATUS            => $campaignStatus,
                    CompanyCampaign::FIELD_NAME              => $campaignName,
                    CompanyCampaign::FIELD_PRODUCT_ID        => $productId,
                    CompanyCampaign::FIELD_SERVICE_ID        => $industryServiceId,
                    CompanyCampaign::FIELD_TYPE              => $campaignType,
                    CompanyCampaign::FIELD_REFERENCE         => Uuid::uuid4()->toString(),
                    CompanyCampaign::FIELD_ZIP_CODE_TARGETED => $zipCodeTargeted ?? false,
                ]
            );
        }

        $campaign->campaignPropertyTypes()->sync($propertyTypes);
        $campaign->save();

        return $campaign;
    }

    /**
     * @param CompanyCampaign $campaign
     * @return ServiceProduct
     */
    public function getServiceProductFromCampaign(CompanyCampaign $campaign): ServiceProduct
    {
        /** @var ServiceProduct */
        return ServiceProduct::query()
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $campaign->{CompanyCampaign::FIELD_SERVICE_ID})
            ->whereHas(ServiceProduct::RELATION_PRODUCT, fn(Builder $query) => $query->where(ProductModel::FIELD_ID, $campaign->{CompanyCampaign::FIELD_PRODUCT_ID}))
            ->firstOrFail();
    }

    /**
     * @param CampaignType $campaignType
     * @return array
     */
    public function getConfigurationByCampaignType(CampaignType $campaignType): array
    {
        return [
            self::CONFIG_KEY_BUDGETS     => $campaignType->getBudgetConfiguration(),
        ];
    }

    /**
     * This config array will get harder to maintain as budget/campaign definitions are added
     * Will start moving configurations to Campaign Definition/Module/Enum Classes instead
     *  to provide more flexibility in configs
     * @see self::getConfigurationByCampaignType()
     *
     * @param Company $company
     * @param Industry|null $industry
     * @return array[]
     * @throws BindingResolutionException
     */
    public function getProductConfigData(Company $company, ?Industry $industry = null): array
    {
        /** @var ProductRejectionCalculationService $rejectionService */
        $rejectionService = app()->make(ProductRejectionCalculationService::class);
        $propertyTypes    = [];
        foreach (PropertyType::cases() as $case) {
            $propertyTypes[] = $case->value;
        }
        /** @var ProductModel $leadProductModel */
        $leadProductModel = ProductModel::query()
            ->where(ProductModel::FIELD_NAME, Product::LEAD->value)
            ->firstOrFail();
        /** @var ProductModel $appointmentProductModel */
        $appointmentProductModel = ProductModel::query()
            ->where(ProductModel::FIELD_NAME, Product::APPOINTMENT->value)
            ->firstOrFail();
        /** @var ProductModel $directLeadsProductModel */
        $directLeadsProductModel = ProductModel::query()
            ->where(ProductModel::FIELD_NAME, Product::DIRECT_LEADS->value)
            ->firstOrFail();

        //Temporarily hide Commercial
        $propertyTypes = [PropertyType::RESIDENTIAL->value];

        return [
            self::CONFIG_KEY_LEAD => [
                self::CONFIG_KEY_PROPERTY_TYPES => $propertyTypes,
                self::CONFIG_KEY_BUDGET_TYPES   => [
                    BudgetType::NO_LIMIT->value         => 'No Limit',
                    BudgetType::TYPE_DAILY_UNITS->value => 'Average Daily Leads',
                    BudgetType::TYPE_DAILY_SPEND->value => 'Average Daily Spend',
                ],
                self::CONFIG_KEY_BUDGET_OPTIONS    => [
                    self::CONFIG_OPTION_LINK_BUDGETS     => true,
                    self::CONFIG_OPTION_SHOW_PRICING     => true,
                    self::CONFIG_OPTION_MINIMUM_PRODUCTS => 1,
                    self::CONFIG_OPTION_MINIMUM_SPEND    => 50,
                ],
                self::CONFIG_KEY_REJECTION_REASONS => RejectionReasons::getIndustryReasons($industry),
                self::CONFIG_KEY_CAN_REJECT        => $rejectionService->companyCanManuallyRejectProduct($company, $leadProductModel),
            ],
            self::CONFIG_KEY_DIRECT_LEADS => [
                self::CONFIG_KEY_PROPERTY_TYPES => [PropertyType::RESIDENTIAL->value],
                self::CONFIG_KEY_BUDGET_TYPES   => [
                    BudgetType::NO_LIMIT->value         => 'No Limit',
                    BudgetType::TYPE_DAILY_UNITS->value => 'Average Daily Leads',
                    BudgetType::TYPE_DAILY_SPEND->value => 'Average Daily Spend',
                ],
                self::CONFIG_KEY_BUDGET_OPTIONS    => [
                    self::CONFIG_OPTION_LINK_BUDGETS     => true,
                    self::CONFIG_OPTION_SHOW_PRICING     => true,
                    self::CONFIG_OPTION_MINIMUM_PRODUCTS => 1,
                    self::CONFIG_OPTION_MINIMUM_SPEND    => 50,
                ],
                self::CONFIG_KEY_REJECTION_REASONS => RejectionReasons::getIndustryReasons($industry), //TODO rejection reason for Direct Leads
                self::CONFIG_KEY_CAN_REJECT        => $rejectionService->companyCanManuallyRejectProduct($company, $directLeadsProductModel),
            ],
            self::CONFIG_KEY_APPOINTMENT => [
                self::CONFIG_KEY_PROPERTY_TYPES => $propertyTypes,
                self::CONFIG_KEY_BUDGET_TYPES   => [
                    BudgetType::NO_LIMIT->value         => 'No Limit',
                    BudgetType::TYPE_DAILY_UNITS->value => 'Average Daily Appointments',
                    BudgetType::TYPE_DAILY_SPEND->value => 'Average Daily Spend',
                ],
                self::CONFIG_KEY_BUDGET_OPTIONS    => [
                    self::CONFIG_OPTION_LINK_BUDGETS     => false,
                    self::CONFIG_OPTION_SHOW_PRICING     => true,
                    self::CONFIG_OPTION_MINIMUM_PRODUCTS => 1,
                    self::CONFIG_OPTION_MINIMUM_SPEND    => 50,
                ],
                self::CONFIG_KEY_REJECTION_REASONS => RejectionReasons::getIndustryReasons($industry, Product::APPOINTMENT),
                self::CONFIG_KEY_CAN_REJECT        => $rejectionService->companyCanManuallyRejectProduct($company, $appointmentProductModel),
            ],
        ];
    }

    /**
     * @param int $campaignId
     * @return CompanyCampaign
     */
    public function find(int $campaignId): CompanyCampaign
    {
        /** @var CompanyCampaign */
        return CompanyCampaign::query()->find($campaignId);
    }

    /**
     * @param int[] $companyCampaignIds
     *
     * @return EloquentCollection<CompanyCampaign>
     */
    public function findMany(array $companyCampaignIds, array $relations = []): EloquentCollection
    {
        return CompanyCampaign::query()
            ->with($relations)
            ->findMany($companyCampaignIds);
    }

    /**
     * @param int $campaignId
     *
     * @return CompanyCampaign
     */
    public function findOrFail(int $campaignId): CompanyCampaign
    {
        /** @var CompanyCampaign */
        return CompanyCampaign::query()->findOrFail($campaignId);
    }

    /**
     * @param string $campaignReference
     * @return CompanyCampaign
     */
    public function findOrFailByReference(string $campaignReference): CompanyCampaign
    {
        /** @var CompanyCampaign */
        return CompanyCampaign::query()
            ->where(CompanyCampaign::FIELD_REFERENCE, $campaignReference)
            ->firstOrFail();
    }

    /**
     * This changes the status only on the Campaign model, and should be called with the Mediator->changeStatus() lifecycle
     * @param CompanyCampaign $campaign
     * @param CampaignStatus $newStatus
     * @param CampaignStatus $oldStatus
     * @return bool
     */
    public function changeStatus(CompanyCampaign $campaign, CampaignStatus $newStatus, CampaignStatus $oldStatus): bool
    {
        if ($newStatus === $oldStatus) return false;

        $campaign->status = $newStatus;

        return $campaign->save();
    }

    /**
     * Campaigns should be removed via the CampaignMediator->delete() lifecycle
     * This is for clean up only if required
     * @param CompanyCampaign $campaign
     * @return bool
     */
    public function delete(CompanyCampaign $campaign): bool
    {
        if ($campaign->exists)
            return $campaign->delete();
        else
            return true;
    }

    /**
     * @param string[] $uuids
     *
     * @return string[]
     */
    public function getCampaignZipCodes(array $uuids): array
    {
        return CompanyCampaign::query()
            ->whereIn(CompanyCampaign::FIELD_REFERENCE, $uuids)
            ->get()
            ->map(fn(CompanyCampaign $campaign) => $campaign->locationModule->locations->pluck(CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE))
            ->flatten()
            ->unique()
            ->filter()
            ->toArray();
    }

    /**
     * @param int $companyId
     * @param int|null $productId
     * @param bool|null $withDeleted
     * @param bool|null $status
     * @return Builder
     */
    public function getCompanyCampaignsQuery(
        int   $companyId,
        ?int  $productId = null,
        ?bool $withDeleted = null,
        ?bool $status = null,
    ): Builder
    {
        $query = $withDeleted ? CompanyCampaign::withTrashed() : CompanyCampaign::query();

        return $query
            ->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId)
            ->when($status === true, function ($query) use ($productId) {
                $query->where(CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE);
            })
            ->when($productId, function ($query) use ($productId) {
                $query->where(CompanyCampaign::FIELD_PRODUCT_ID, $productId);
            });
    }

    /**
     * @param Industry $industry
     * @param bool $rejectionThreshold
     * @param bool|null $excludeLowBids
     * @return EloquentCollection<CompanyCampaign>
     * @throws BindingResolutionException
     */
    public function getAvailableCampaignsByIndustry(Industry $industry, bool $rejectionThreshold = true, ?bool $excludeLowBids = false): EloquentCollection
    {
        /** @var CompanyCampaignBuilder $campaignBuilder */
        $campaignBuilder = app()->make(CompanyCampaignBuilder::class);

        $campaigns = $campaignBuilder
            ->forIndustries([$industry])
            ->forProducts([Product::LEAD])
            ->forStatuses([CampaignStatus::ACTIVE])
            ->excludeLowBids($excludeLowBids)
            ->forCompanyConsolidatedStatuses([CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS])
            ->forPropertyTypes([PropertyType::RESIDENTIAL])
            ->getQuery()
            ->get();

        if ($rejectionThreshold) {
            return $this->filterCampaignForCompanyRejectionThreshold($campaigns);
        }

        return $campaigns;
    }

    /**
     * @param int $companyId
     * @return Collection
     */
    public function getZipCodeTargetCampaignForCompany(int $companyId): Collection
    {
        /** @var Company $company */
        $company = Company::query()
            ->findOrFail($companyId);

        return $company->futureCampaigns()
            ->where(CompanyCampaign::FIELD_ZIP_CODE_TARGETED, true)
            ->get();
    }

    /**
     * @param EloquentCollection $campaigns
     *
     * @return EloquentCollection
     * @throws BindingResolutionException
     */
    protected function filterCampaignForCompanyRejectionThreshold(EloquentCollection $campaigns): EloquentCollection
    {
        $legacyCompanyIds = $campaigns->load([
            CompanyCampaign::RELATION_COMPANY => fn($query) => $query->select([Company::FIELD_ID, Company::FIELD_LEGACY_ID])
        ])
            ->map(fn(CompanyCampaign $campaign) => $campaign->company->legacy_id)
            ->unique()
            ->toArray();

        /** @var ComputedRejectionStatisticRepository $rejectionRepository */
        $rejectionRepository = app()->make(ComputedRejectionStatisticRepository::class);
        $rejectionThreshold  = $this->getRejectionThreshold();
        $rejections          = $rejectionRepository->getLeadRejectionPercentageForCompaniesByLegacyIds($legacyCompanyIds);

        return $campaigns->filter(function (CompanyCampaign $campaign) use ($rejectionThreshold, $rejections, $rejectionRepository) {
            $rejectionPercent = $rejectionRepository->getComputedRejectionStatisticsByLegacyIdFromRejectionsCollection(
                $rejections,
                $campaign->company->legacy_id
            );

            return ($rejectionPercent?->{ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY} ?? 0) < $rejectionThreshold;
        });
    }

    /**
     * @return mixed
     */
    protected function getRejectionThreshold(): mixed
    {
        return ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY === ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE ?
            config('sales.leads.crm_rejection_percentage_threshold') :
            config('sales.leads.overall_rejection_percentage_threshold');
    }

    /**
     * @param int $companyId
     * @param string $query
     * @return Collection
     */
    public function searchCompanyCampaigns(int $companyId, string $query): Collection
    {
        return CompanyCampaign::query()
            ->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId)
            ->where(CompanyCampaign::FIELD_NAME, 'LIKE', "%{$query}%")
            ->get();
    }

    /**
     * @param Collection $productAssignments
     * @return Collection<CompanyCampaign>
     */
    public function getProductAssignmentToCampaignMapping(Collection $productAssignments): Collection
    {
        $joinedProductAssignments = ProductAssignment::query()
            ->leftJoin(Budget::TABLE, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_BUDGET_ID, '=', Budget::TABLE . '.' . Budget::FIELD_ID)
            ->leftJoin(BudgetContainer::TABLE, Budget::TABLE . '.' . Budget::FIELD_BUDGET_CONTAINER_ID, '=', BudgetContainer::TABLE . '.' . BaseCompanyCampaignModule::FIELD_ID)
            ->leftJoin(CompanyCampaign::TABLE, BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID, '=', CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID)
            ->whereIntegerInRaw(Budget::TABLE . '.' . Budget::FIELD_ID, $productAssignments->pluck(ProductAssignment::FIELD_BUDGET_ID)->toArray())
            ->whereIntegerInRaw(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID, $productAssignments->pluck(ProductAssignment::FIELD_ID)->toArray())
            ->get([CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID . ' as campaign_id', ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID . ' as product_assignment_id']);
        return $joinedProductAssignments->pluck('campaign_id', 'product_assignment_id');
    }

    /**
     * @param int $companyId
     * @return EloquentCollection|array
     */
    public function getCampaignOverview(int $companyId): EloquentCollection|array
    {
        return CompanyCampaign::query()
            ->select(
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID . ' as campaign_id',
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_REFERENCE . ' as campaign_reference',
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_NAME . ' as campaign_name',
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_STATUS . ' as campaign_status',
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_MAXIMUM_BUDGET_USAGE,
                Budget::TABLE .'.'. Budget::FIELD_TYPE . ' as budget_type',
                Budget::TABLE .'.'. Budget::FIELD_DISPLAY_NAME . ' as budget_display_name',
                Budget::TABLE .'.'. Budget::FIELD_ID . ' as budget_id',
                Budget::TABLE .'.'. Budget::FIELD_VALUE . ' as budget_value',
                CampaignReactivation::TABLE .'.'. CampaignReactivation::FIELD_CREATED_AT . ' as campaign_paused_at',
                CampaignReactivation::TABLE .'.'. CampaignReactivation::FIELD_REACTIVATE_AT . ' as campaign_paused_until',
            )
            ->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID, $companyId)
            ->whereNot(Budget::TABLE .'.'. Budget::FIELD_KEY, 'unverified')
            ->leftJoin(
                CampaignReactivation::TABLE,
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID,
                '=',
                CampaignReactivation::FIELD_CAMPAIGN_ID
            )
            ->leftJoin(
                BudgetContainer::TABLE,
                CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID,
                '=',
                BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID,
            )->leftJoin(
                Budget::TABLE,
                BudgetContainer::TABLE .'.'. BaseCompanyCampaignModule::FIELD_ID,
                '=',
                Budget::TABLE .'.'. Budget::FIELD_BUDGET_CONTAINER_ID,
            )
            ->groupBy(Budget::TABLE .'.'. Budget::FIELD_ID)
            ->orderBy(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_NAME)
            ->get();
    }

    /**
     * @param string $name
     * @param string $campaignReference
     *
     * @return bool
     */
    public function saveOptInName(string $name, string $campaignReference): bool
    {
        $campaign = $this->findOrFailByReference($campaignReference);
        $optInName = CompanyOptInName::query()->firstOrCreate([
            CompanyOptInName::FIELD_NAME => $name,
            CompanyOptInName::FIELD_COMPANY_ID => $campaign->company_id
        ]);

        $campaign->active_opt_in_name_id = $optInName->id;

        return $campaign->save();
    }

    /**
     * Return the total of chargeable-uninvoiced leads grouped by campaign
     * @return Builder
     */
    public function getTotalChargeableUninvoicedLeadsGroupedByCampaign(): Builder
    {
        return $this->productAssignmentRepository->getChargeableUninvoicedProductAssignmentsQuery()
            ->select([
                BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_CAMPAIGN_ID . ' AS campaign_id',
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID . ' AS company_id',
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_NAME . ' AS campaign_name',
                DB::raw('SUM(' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COST . ') AS total_leads_cost_in_dollars'),
                DB::raw('COUNT(1) AS count_leads'),
            ])
            ->join(Budget::TABLE,
                Budget::TABLE . '.' . Budget::FIELD_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_BUDGET_ID,
            )
            ->join(BudgetContainer::TABLE,
                BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_ID,
                Budget::TABLE . '.' . Budget::FIELD_BUDGET_CONTAINER_ID
            )
            ->join(CompanyCampaign::TABLE,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID,
                BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID
            )
            ->groupBy(
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID,
            );
    }
}
