<?php

namespace App\Repositories;

use App\Contracts\Repositories\TeamRepositoryContract;
use App\Models\Teams\Team;
use App\Models\Teams\TeamLeader;
use App\Models\Teams\TeamMember;
use App\Models\Teams\TeamType;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class TeamRepository implements TeamRepositoryContract
{
    /**
     * @inheritDoc
     */
    public function findOrFail(int $teamId): Team
    {
        /** @var Team $team */
        $team = Team::query()->findOrFail($teamId);
        return $team;
    }

    /**
     * @inheritDoc
     */
    public function findTeamsByLeader(int $userId, ?int $filterByTeamTypeId = null): Collection
    {
        $leaderPositions = TeamLeader::query()
            ->where(TeamLeader::FIELD_USER_ID, $userId)
            ->get();
        $teams = $leaderPositions->map(fn(TeamLeader $leader) => $leader->{TeamLeader::RELATION_TEAM});

        return $filterByTeamTypeId
            ? $teams->filter(fn (Team $team) => $team->{Team::RELATION_TEAM_TYPE}?->{TeamType::FIELD_ID} === $filterByTeamTypeId)
            : $teams;
    }

    /**
     * @inheritDoc
     */
    public function findTeamsByType(int $teamTypeId): Collection
    {
        /** @var Collection $teams */
        $teams = Team::query()->where(Team::FIELD_TEAM_TYPE_ID, $teamTypeId)->get();

        return $teams;
    }

    /**
     * @inheritDoc
     */
    public function findTeamsByMember(int $userId): Collection
    {
        $memberPositions = TeamMember::query()->where(TeamMember::FIELD_USER_ID)?->get() ?? collect();
        return $memberPositions->map(fn(TeamMember $member) => $member->{TeamMember::RELATION_TEAM});
    }

    /**
     * @inheritDoc
     */
    public function createEmptyTeam(int $teamTypeId, string $name, ?string $description = ""): Team
    {
        $teamType = TeamType::query()->findOrFail($teamTypeId);
        /** @var Team $newTeam */
        $newTeam = Team::query()->create([
            Team::FIELD_NAME            => $name,
            Team::FIELD_DESCRIPTION     => $description ?? "",
            Team::FIELD_TEAM_TYPE_ID    => $teamType->{TeamType::FIELD_ID}
        ]);
        return $newTeam;
    }

    /**
     * @inheritDoc
     */
    public function createTeamFromLeader(int $teamTypeId, string $name, string $description, int $userId, string $leaderTitle, ?int $reportsTo = null): ?Team
    {
        /** @var User $user */
        $user = User::query()->findOrFail($userId);
        $reportsTo = $reportsTo
            ? User::query()->findOrFail($reportsTo)
            : null;

        $newTeam = $this->createEmptyTeam($teamTypeId, $name, $description);
        $this->createLeaderFromUser($newTeam, $user, $leaderTitle, $reportsTo);

        return $newTeam;
    }

    /**
     * @inheritDoc
     */
    public function assignUserToTeamLeader(Team $team, int $userId): bool
    {
        $user = User::query()->findOrFail($userId);

        $success = TeamLeader::query()->updateOrCreate([
            TeamLeader::FIELD_TEAM_ID   => $team->{Team::FIELD_ID}
        ],
        [
            TeamLeader::FIELD_USER_ID   => $user->{User::FIELD_ID}
        ]);

        return !!$success;
    }

    /**
     * @inheritDoc
     */
    public function createTeamMemberFromUser(Team $team, int $userId, string $memberTitle): bool
    {
        $user = User::query()->findOrFail($userId);

        $success = TeamMember::query()->firstOrCreate([
                TeamMember::FIELD_TEAM_ID   => $team->{Team::FIELD_ID},
                TeamMember::FIELD_USER_ID   => $user->{User::FIELD_ID},
            ],
            [
                TeamMember::FIELD_TITLE   => $memberTitle
            ]);

        return !!$success;
    }

    /**
     * @inheritDoc
     */
    public function removeUserFromTeam(Team $team, int $userId): bool
    {
        $user = User::query()->findOrFail($userId);

        $teamMember = TeamMember::query()->where(TeamMember::FIELD_USER_ID, $user->{User::FIELD_ID})
            ->where(TeamMember::FIELD_TEAM_ID, $team->{Team::FIELD_ID})
            ->first();

        return $teamMember && $teamMember->delete();
    }

    /**
     * @inheritDoc
     */
    public function deleteTeam(int $teamId): bool
    {
        $team = $this->findOrFail($teamId);

        DB::beginTransaction();

        $deletedLeader = !$team->{Team::RELATION_TEAM_LEADER} || $team->{Team::RELATION_TEAM_LEADER}()->delete();
        $deletedMembers = !$team->{Team::RELATION_TEAM_MEMBERS} || $team->{Team::RELATION_TEAM_MEMBERS}()->delete();
        $deletedTeam = $deletedLeader && $deletedMembers && $team->delete();

        if ($deletedMembers && $deletedLeader && $deletedTeam) {
            DB::commit();
            return true;
        }
        else {
            DB::rollBack();
            return false;
        }
    }

    /**
     * @inheritDoc
     */
    public function updateTeam(array $teamData, array $leaderData, array $memberData): bool
    {
        $team = $this->findOrFail($teamData[Team::FIELD_ID]);
        $teamUpdated = $team->update([
            Team::FIELD_DESCRIPTION     => $teamData[Team::FIELD_DESCRIPTION],
            Team::FIELD_TEAM_TYPE_ID    => $teamData[Team::RELATION_TEAM_TYPE][TeamType::FIELD_ID],
            Team::FIELD_PARENT_TEAM_ID  => $teamData[Team::FIELD_PARENT_TEAM_ID]
        ]);

        $assignedLeader = !Arr::get($leaderData, TeamLeader::FIELD_USER_ID) ? $this->deleteTeamLeader($team) : $this->assignUserToTeamLeader($team, $leaderData[TeamLeader::FIELD_USER_ID]);
        $updatedMembers = $this->syncTeamMembers($team, $memberData);

        return $teamUpdated && $assignedLeader && $updatedMembers;
    }

    /**
     * @param Team $team
     *
     * @return bool
     */
    public function deleteTeamLeader(Team $team): bool
    {
        return !$team->leader || !! $team->leader->delete();
    }

    /**
     * Retrieve team members for the given user.
     *
     * If the user is the team leader, all team members (including the leader) are returned.
     * If the user is only a team member, only that user is returned.
     *
     * @param User $user
     *
     * @return Collection<User>
     */
    public function getTeamMembersForUser(User $user): Collection
    {
        $leaderTeam = Team::query()
            ->orWhereHas(Team::RELATION_TEAM_LEADER, fn(Builder $builder) => $builder->where(TeamLeader::FIELD_USER_ID, $user->id))
            ->first();

        //User is a leader
        if ($leaderTeam) {
            $teamMembers = collect();

            TeamLeader::query()
                ->where(TeamLeader::FIELD_USER_ID, $user->id)
                ->get()
                ->each(function (TeamLeader $teamLeader) use (&$teamMembers) {
                    $teamMembers = $teamMembers->merge($teamLeader->team->members);
                });

            return $teamMembers->map(fn(TeamMember $teamMember) => $teamMember->user)->push($user)->unique(User::FIELD_ID)->values();
        }


        $team = Team::query()
            ->whereHas(Team::RELATION_TEAM_MEMBERS, fn(Builder $builder) => $builder->where(TeamMember::FIELD_USER_ID, $user->id))
            ->first();

        return $team ? collect([$user]) : collect();
    }


    /**
     * @param Team $team
     * @param array $members
     * @return bool
     */
    private function syncTeamMembers(Team $team, array $members): bool
    {
        $currentMemberIds = $team->{Team::RELATION_TEAM_MEMBERS}?->pluck(TeamMember::FIELD_USER_ID);
        $updatedMemberIds = collect($members)->pluck(TeamMember::FIELD_USER_ID);

        $currentMemberIds->each(function(int $currentMemberId) use ($team, $updatedMemberIds) {
            if (!$updatedMemberIds->contains($currentMemberId)) $this->removeUserFromTeam($team, $currentMemberId);
        });
        $updatedMemberIds->each(function(int $updatedMemberId, $index) use ($team, $currentMemberIds, $members) {
            if (!$currentMemberIds->contains($updatedMemberId)) $this->createTeamMemberFromUser($team, $updatedMemberId, $members[$index][TeamMember::FIELD_TITLE] ?? "");
        });

        return true;
    }

    /**
     * @param Team $team
     * @param User $user
     * @param string $leaderTitle
     * @param null | User $reportsTo
     * @return TeamLeader
     * @throws ModelNotFoundException
     */
    private function createLeaderFromUser(Team $team, User $user, string $leaderTitle, ?User $reportsTo): TeamLeader
    {
        $reportsToUserId = User::query()->find($reportsTo)?->{User::FIELD_ID};

        /** @var TeamLeader $newLeader */
        $newLeader = TeamLeader::query()->updateOrCreate([
            TeamLeader::FIELD_TEAM_ID   => $team->{Team::FIELD_ID}
        ],
            [
                TeamLeader::FIELD_USER_ID       => $user->{User::FIELD_ID},
                TeamLeader::FIELD_TITLE         => $leaderTitle,
                TeamLeader::FIELD_REPORTS_TO    => $reportsToUserId
            ]);
        return $newLeader;
    }
}
