<?php

namespace App\Repositories;

use App\Enums\CommunicationRelationTypes;
use App\Models\BaseModel;
use App\Models\Text;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class TextRepository
{
    /**
     * @param array|null $otherNumbers
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param string|null $direction
     * @param int|null $phoneId
     * @param array<BaseModel>|null $relations
     * @return Builder
     */
    public function list(
        ?array  $otherNumbers = [],
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        ?string $direction = null,
        ?int    $phoneId = null,
        ?array $relations = [],
    ): Builder
    {
        return Text::query()
            ->when($phoneId, fn($query) => $query->where(Text::FIELD_PHONE_ID, $phoneId))
            ->when($direction, fn($query) => $query->where(Text::FIELD_DIRECTION, $direction))
            ->when($startDate, fn($query) => $query->where(Text::FIELD_CREATED_AT, '>=', $startDate))
            ->when($endDate, fn($query) => $query->where(Text::FIELD_CREATED_AT, '<=', $endDate))
            ->when(filled($otherNumbers), fn($query) => $query->whereIn(Text::FIELD_OTHER_NUMBER, $otherNumbers))
            ->when(filled($relations), function ($query) use ($relations) {
                $query->where(function (Builder $query) use ($relations) {
                    foreach ($relations as $relation) {
                        $query->orWhere(function (Builder $query) use ($relation) {
                            $query->where(Text::FIELD_RELATION_TYPE, CommunicationRelationTypes::fromClass($relation::class)->value)
                                ->where(Text::FIELD_RELATION_ID, $relation->id);
                        });
                    }
                });
            });
    }

}
