<?php

namespace App\Repositories;

use App\Enums\Odin\Product as ProductEnum;
use App\Models\ComputedRejectionStatistic;
use App\Models\Odin\Company;
use App\Models\Odin\Product;
use App\Repositories\Odin\ProductRepository;
use App\Services\ProductRejectionCalculationService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class ComputedRejectionStatisticRepository
{

    public function __construct(protected ProductRepository $productRepository)
    {
    }

    /**
     * @param array $data
     * @return void
     */
    public function upsertRejectionStatistics(array $data): void
    {
        ComputedRejectionStatistic::query()->upsert(
            $data,
            [ComputedRejectionStatistic::FIELD_COMPANY_ID, ComputedRejectionStatistic::FIELD_PRODUCT_ID]
        );
    }

    /**
     * @param Company $company
     * @param int $productId
     * @param array $rejectionStatistics
     * @return ComputedRejectionStatistic
     */
    public function createOrUpdateRejectionStatistic(
        Company   $company,
        int   $productId,
        array $rejectionStatistics
    ): ComputedRejectionStatistic
    {
        $data = [
            ComputedRejectionStatistic::FIELD_COMPANY_ID                   => $company->{Company::FIELD_ID},
            ComputedRejectionStatistic::FIELD_LEGACY_COMPANY_ID            => $company->{Company::FIELD_LEGACY_ID},
            ComputedRejectionStatistic::FIELD_PRODUCT_ID                   => $productId,
            ComputedRejectionStatistic::FIELD_MANUAL_REJECTED_PRODUCT_COST => Arr::get($rejectionStatistics, ComputedRejectionStatistic::FIELD_MANUAL_REJECTED_PRODUCT_COST),
            ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE  => Arr::get($rejectionStatistics, ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE),
            ComputedRejectionStatistic::FIELD_CRM_REJECTED_PRODUCT_COST    => Arr::get($rejectionStatistics, ComputedRejectionStatistic::FIELD_CRM_REJECTED_PRODUCT_COST),
            ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE     => Arr::get($rejectionStatistics, ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE),
            ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE => Arr::get($rejectionStatistics, ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE),
            ComputedRejectionStatistic::FIELD_ASSIGNED_PRODUCT_COST        => Arr::get($rejectionStatistics, ComputedRejectionStatistic::FIELD_ASSIGNED_PRODUCT_COST),
        ];

        return ComputedRejectionStatistic::query()->updateOrCreate([
            ComputedRejectionStatistic::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
            ComputedRejectionStatistic::FIELD_PRODUCT_ID => $productId,
        ], $data);
    }

    /**
     * @param int $companyId
     * @param int $productId
     * @return ComputedRejectionStatistic|null
     */
    public function getRejectionStatisticsByCompanyIdAndProductId(int $companyId, int $productId): ComputedRejectionStatistic|null
    {
        return ComputedRejectionStatistic::query()
            ->where(ComputedRejectionStatistic::FIELD_COMPANY_ID, $companyId)
            ->where(ComputedRejectionStatistic::FIELD_PRODUCT_ID, $productId)
            ->first();
    }

    /**
     * @param Company $company
     * @return float|null
     */
    public function getCompanyLeadRejectionPercentage(Company $company): ?float
    {
        return $this->getCompanyLeadRejection($company)?->{ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE};
    }

    /**
     * @param  Company  $company
     * @return ComputedRejectionStatistic|null
     */
    public function getCompanyLeadRejection(Company $company): ?ComputedRejectionStatistic
    {
        return $this->getRejectionStatisticsByCompanyIdAndProductId(
            $company->{Company::FIELD_ID},
            $this->productRepository->getLeadProductId()
        );
    }

    /**
     * @param  Company  $company
     * @return ComputedRejectionStatistic|null
     */
    public function getCompanyDirectLeadsRejection(Company $company): ?ComputedRejectionStatistic
    {
        return $this->getRejectionStatisticsByCompanyIdAndProductId(
            $company->{Company::FIELD_ID},
            $this->productRepository->getDirectLeadProductId()
        );
    }

    /**
     * @param array $companyIds
     * @return Collection
     */
    public function getLeadRejectionPercentageForCompanies(array $companyIds): Collection
    {
        return $this->getProductRejectionPercentageForCompanies(
            $companyIds,
            $this->productRepository->getLeadProductId()
        );
    }

    /**
     * @param array $legacyCompanyIds
     * @return Collection
     */
    public function getLeadRejectionPercentageForCompaniesByLegacyIds(array $legacyCompanyIds): Collection
    {
        return $this->getProductRejectionPercentageForCompaniesByLegacyIds(
            $legacyCompanyIds,
            $this->productRepository->getLeadProductId()
        );
    }

    /**
     * @param array $companyIds
     * @param int $productId
     * @return Collection
     */
    public function getProductRejectionPercentageForCompanies(array $companyIds, int $productId): Collection
    {
        return ComputedRejectionStatistic::query()
            ->whereIn(ComputedRejectionStatistic::FIELD_COMPANY_ID, $companyIds)
            ->where(ComputedRejectionStatistic::FIELD_PRODUCT_ID, $productId)
            ->get();
    }

    /**
     * @param array $legacyCompanyIds
     * @param int $productId
     * @return Collection
     */
    public function getProductRejectionPercentageForCompaniesByLegacyIds(array $legacyCompanyIds, int $productId): Collection
    {
        return ComputedRejectionStatistic::query()
            ->whereIn(ComputedRejectionStatistic::FIELD_LEGACY_COMPANY_ID, $legacyCompanyIds)
            ->where(ComputedRejectionStatistic::FIELD_PRODUCT_ID, $productId)
            ->get();
    }

    /**
     * @param Collection $rejections
     * @param int $legacyCompanyId
     * @return ComputedRejectionStatistic|null
     */
    public function getComputedRejectionStatisticsByLegacyIdFromRejectionsCollection(Collection $rejections, int $legacyCompanyId): ComputedRejectionStatistic|null
    {
        return $rejections
            ->where(ComputedRejectionStatistic::FIELD_LEGACY_COMPANY_ID, $legacyCompanyId)
            ->first() ?? null;
    }

    /**
     * @param int $companyId
     * @param ProductRejectionCalculationService $rejectionService
     * @return void
     */
    public function resetCrmRejectionsByCompanyId(int $companyId, ProductRejectionCalculationService $rejectionService): void
    {
        /** @var Company $company */
        $company = Company::query()->findOrFail($companyId);

        ComputedRejectionStatistic::query()
            ->where(ComputedRejectionStatistic::FIELD_COMPANY_ID, $companyId)
            ->update([
                ComputedRejectionStatistic::FIELD_CRM_RESET_TIME => Carbon::now()
            ]);

        $productIds = ComputedRejectionStatistic::query()
            ->select(ComputedRejectionStatistic::FIELD_PRODUCT_ID)
            ->where(ComputedRejectionStatistic::FIELD_COMPANY_ID, $companyId)
            ->pluck(ComputedRejectionStatistic::FIELD_PRODUCT_ID)
            ->unique()
            ->toArray();

        foreach($productIds as $productId) {
            $rejectionStatistics = $rejectionService->computeRejectionStatistics($company, $productId);

            $this->createOrUpdateRejectionStatistic(
                $company,
                $productId,
                $rejectionStatistics
            );
        }
    }

    /**
     * @param int $companyId
     * @param ProductEnum $product
     *
     * @return float
     */
    public function getCRMRejectionPercentageForCompanyAndProduct(int $companyId, ProductEnum $product): float
    {
        return ComputedRejectionStatistic::query()
            ->where(ComputedRejectionStatistic::FIELD_COMPANY_ID, $companyId)
            ->whereHas(ComputedRejectionStatistic::RELATION_PRODUCT, fn(Builder $query) => $query->where(Product::FIELD_NAME, $product))
            ->first()->crm_rejection_percentage ?? 0;
    }
}
