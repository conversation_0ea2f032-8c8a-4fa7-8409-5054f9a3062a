<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\NonPurchasingCompanyLocationRepositoryInterface;
use App\Models\CompanyUserRelationship;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\Location;
use App\Models\Legacy\LegacyNonPurchasingCompanyLocation;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use Illuminate\Support\Collection;

class LegacyNonPurchasingCompanyLocationRepository implements NonPurchasingCompanyLocationRepositoryInterface
{
    /**
     * @param int $locationId
     * @return array|null
     */
    public function getCompanyIdsByLocation(int $locationId): ?array
    {
        return LegacyNonPurchasingCompanyLocation::query()
            ->select(DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_ID)
            ->where(LegacyNonPurchasingCompanyLocation::FIELD_LOCATION_ID, $locationId)
            ->join(
                DatabaseHelperService::database().'.'.Company::TABLE,
                DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
                '=',
                LegacyNonPurchasingCompanyLocation::TABLE.'.'.LegacyNonPurchasingCompanyLocation::FIELD_COMPANY_ID
            )
            ->pluck(Company::FIELD_ID)
            ->toArray();
    }

    /**
     * @param int $locationId
     * @return Collection<LegacyNonPurchasingCompanyLocation>|null
     */
    public function getLegacyCompanyIdsByLocation(int $locationId): ?Collection
    {
        return LegacyNonPurchasingCompanyLocation::query()
            ->where(LegacyNonPurchasingCompanyLocation::FIELD_LOCATION_ID, $locationId)
            ->get()
            ->pluck(LegacyNonPurchasingCompanyLocation::FIELD_COMPANY_ID);
    }

    public function getLegacyCompanyIdsByLocationForAccountManager(int $locationId, int $accountManagerUserId): ?Collection
    {
        $query = LegacyNonPurchasingCompanyLocation::query()
            ->where(LegacyNonPurchasingCompanyLocation::TABLE .'.'. LegacyNonPurchasingCompanyLocation::FIELD_LOCATION_ID,
                $locationId)
            ->join(
                EloquentCompany::TABLE,
                EloquentCompany::TABLE.'.'. EloquentCompany::ID,
                '=',
                LegacyNonPurchasingCompanyLocation::TABLE .'.'. LegacyNonPurchasingCompanyLocation::FIELD_COMPANY_ID
            )
            ->join(
                DatabaseHelperService::database().'.'.Company::TABLE,
                DatabaseHelperService::database().'.'.Company::TABLE.'.'. Company::FIELD_LEGACY_ID,
                '=',
                EloquentCompany::TABLE.'.'. EloquentCompany::ID
            )
            ->join(
                DatabaseHelperService::database().'.'.CompanyUserRelationship::TABLE,
                DatabaseHelperService::database().'.'.CompanyUserRelationship::TABLE.'.'.CompanyUserRelationship::FIELD_COMPANY_ID,
                "=",
                DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_ID
            )
            ->where(DatabaseHelperService::database().'.'.CompanyUserRelationship::TABLE.'.'.CompanyUserRelationship::FIELD_USER_ID,
                $accountManagerUserId);
;
        return $query->pluck(LegacyNonPurchasingCompanyLocation::TABLE .'.'. LegacyNonPurchasingCompanyLocation::FIELD_COMPANY_ID);
    }

    /**
     * @param Company $company
     * @return Collection<int, Location>
     */
    public function getStateLocationsByCompany(Company $company): Collection
    {
        return LegacyNonPurchasingCompanyLocation::query()
                                           ->where(LegacyNonPurchasingCompanyLocation::FIELD_COMPANY_ID, $company->legacy_id)
                                           ->with([LegacyNonPurchasingCompanyLocation::RELATION_LOCATION])
                                           ->get()
                                           ->map(fn(LegacyNonPurchasingCompanyLocation $npl) => $npl->location)
                                           ->where(Location::TYPE, Location::TYPE_STATE)
                                           ->unique(Location::STATE_KEY);
    }
}
