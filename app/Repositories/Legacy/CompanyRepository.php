<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\CompanyRepositoryContract;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\LegacyCompanyAdminStatus;
use App\Enums\LegacyCompanyBasicStatus;
use App\Jobs\DispatchPubSubEvent;
use App\Models\AccountManager;
use App\Models\AccountManagerClient;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentConfiguration;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\EloquentUser;
use App\Models\Legacy\Industry;
use App\Models\Legacy\IndustryType;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use \App\Models\Odin\Industry as OdinIndustry;
use App\Models\User;
use App\Repositories\CompanyAccountManagerRepository;
use App\Repositories\LocationRepository;
use App\Services\DatabaseHelperService;
use App\Services\Legacy\APIConsumer;
use App\Services\Legacy\ReferenceListsService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use Exception;

class CompanyRepository implements CompanyRepositoryContract
{
    const SEARCH_OPTION_TYPE_ACCOUNT_MANAGERS          = "account_managers";
    const SEARCH_OPTION_TYPE_CUSTOMER_SUCCESS_MANAGERS = "customer_success_managers";
    const SEARCH_OPTION_TYPE_STATES                    = "states";
    const SEARCH_OPTION_TYPE_LEAD_BUYING_STATUSES      = "lead_buying_statuses";
    const SEARCH_OPTION_TYPE_PAYMENT_METHODS           = "payment_methods";
    const SEARCH_OPTION_TYPE_COMPANY_STATUSES          = "company_statuses";
    const SEARCH_OPTION_TYPE_COMPANY_TYPES             = "company_types";
    const SEARCH_OPTION_TYPE_CAMPAIGNS_STATUSES        = "campaigns_statuses";
    const SEARCH_OPTION_TYPE_INDUSTRIES                = "industries";

    const CAMPAIGN_STATUS_ACTIVE          = "active";
    const CAMPAIGN_STATUS_ONE_PAUSED      = "one_paused";
    const CAMPAIGN_STATUS_PAUSED          = "paused";
    const CAMPAIGN_STATUS_OVER_BUDGET     = "over_budget";
    const CAMPAIGN_STATUS_ONE_OVER_BUDGET = "one_over_budget";

    const CAMPAIGN_STATUS_NAMES = [
        self::CAMPAIGN_STATUS_ACTIVE          => "Active",
        self::CAMPAIGN_STATUS_ONE_PAUSED      => "At Least One Campaign Paused",
        self::CAMPAIGN_STATUS_PAUSED          => "All Campaigns Paused",
        self::CAMPAIGN_STATUS_OVER_BUDGET     => "All Campaigns Over Budget",
        self::CAMPAIGN_STATUS_ONE_OVER_BUDGET => "At Least One Campaign Over Budget"
    ];

    const REQUEST_COMPANY_STATUS    = 'company_status';
    const REQUEST_COMPANY_IDS       = 'ids';

    const API_BASE_ENDPOINT                         = '/repositories/companies/';
    const API_UPDATE_COMPANY_STATUS_ENDPOINT        = '/status';
    const API_UPDATE_COMPANY_DETAILS_ENDPOINT       = '/';
    const API_UPDATE_COMPANY_BASIC_DETAILS_ENDPOINT = '/basic-details';
    const API_SET_COMPANY_LEAD_BUDGET_ENDPOINT      = '/set-lead-budget';
    const API_SET_COMPANY_LEAD_CONTACT_ENDPOINT     = '/set-lead-contact';
    const API_SET_COMPANY_SERVICES_ENDPOINT         = '/set-company-services';
    const API_DELETE_COMPANY_SERVICE_ENDPOINT       = '/delete-company-service';
    const API_GET_COMPANIES_PAYMENT_METHOD_STATUSES = self::API_BASE_ENDPOINT . 'companies-payment-method-statuses';

    /**
     * @param APIConsumer $apiConsumer
     * @param LocationRepository $locationRepository
     * @param LegacyNonPurchasingCompanyLocationRepository $nonPurchasingCompanyLocationRepository
     */
    public function __construct(
        protected APIConsumer                            $apiConsumer,
        protected LocationRepository                     $locationRepository,
        protected LegacyNonPurchasingCompanyLocationRepository $nonPurchasingCompanyLocationRepository
    ) {}

    /**
     * @return Collection<int, EloquentUser>
     */
    public function getAllAccountAndCustomerSuccessManagers(): Collection
    {
        $managerIds = EloquentCompany::query()
            ->selectRaw('DISTINCT ' . EloquentCompany::ACCOUNT_MANAGER_ID . ' AS manager_id')
            ->where(EloquentCompany::ACCOUNT_MANAGER_ID, '>', 0)
            ->get()
            ->pluck('manager_id')->merge(
                EloquentCompany::query()
                    ->selectRaw('DISTINCT ' . EloquentCompany::SALES_CONSULTANT_ID . ' AS manager_id')
                    ->where(EloquentCompany::SALES_CONSULTANT_ID, '>', 0)
                    ->get()
                    ->pluck('manager_id')
            )
            ->unique();

        return EloquentUser::query()->where(EloquentUser::STATUS, EloquentUser::FIELD_STATUS_VALUE_ACTIVE)
            ->whereIn(EloquentUser::ID, $managerIds)
            ->get();
    }

    /**
     * @param int $companyId
     * @return hasMany
     */
    public function getCompanyCampaigns(int $companyId, int $status = null): hasMany
    {
        /**
         * @var EloquentCompany $company
         */
        $company = EloquentCompany::query()->findOrFail($companyId);
        $campaignsQuery = $company->campaigns();
        if($status === LeadCampaign::STATUS_ACTIVE || $status === LeadCampaign::STATUS_INACTIVE) $campaignsQuery->where(LeadCampaign::STATUS, $status);

        return $campaignsQuery;
    }

    /**
     * @param int $companyId
     * @return hasMany
     */
    public function getCompanyInvoices(int $companyId): hasMany
    {
        /**
         * @var EloquentCompany $company
         */
        $company = EloquentCompany::query()->findOrFail($companyId);
        return $company->invoices()->orderByDesc(EloquentInvoice::TIMESTAMP_ADDED);
    }

    /**
     * @param int $companyId
     * return int
     */
    public function getCompanyTotalSpend(int $companyId): int
    {
        return EloquentInvoice::query()
                   ->selectRaw('ROUND(Sum((' . EloquentInvoiceItem::QUANTITY . ' * ' . EloquentInvoiceItem::ITEM_EX_TAX_PRICE . ') + ' . EloquentInvoiceItem::TOTAL_TAX_AMOUNT . '),2) as TotalAmountPaid')
                   ->join(EloquentInvoiceItem::TABLE, EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::INVOICE_ID, '=', EloquentInvoice::TABLE . '.' . EloquentInvoice::INVOICE_ID)
                   ->where(EloquentInvoice::TABLE . '.' . EloquentInvoice::COMPANY_ID, '=', $companyId)
                   ->where(EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, EloquentInvoice::VALUE_STATUS_PAID)
                   ->pluck('TotalAmountPaid')[0] ?? 0;
    }

    /**
     * @param array $select
     * @param array $relations
     * @param string $companyName
     * @param string $entityName
     * @param array $companyStatuses
     * @param array $companyTypes
     * @param array $industries
     * @param int $accountManagerId
     * @param int|null $salesUserId
     * @param string $state
     * @param string $campaignsStatus
     * @param string $leadBuyingStatus
     * @param string $paymentSource
     * @param bool $prepaidOnly
     * @param string $superPremiumOptIn
     * @param bool $restrictCurrentAccountManager
     * @return Builder
     */
    public function getSearchCompaniesQuery(
        array  $select = [],
        array  $relations = [],
        string $companyName = '',
        string $entityName = '',
        array  $companyStatuses = [EloquentCompany::STATUS_PRESALES, EloquentCompany::STATUS_ACTIVE],
        array  $companyTypes = [EloquentCompany::TYPE_INSTALLER, EloquentCompany::TYPE_MANUFACTURER, EloquentCompany::TYPE_NON_PROFIT, EloquentCompany::TYPE_SERVICE],
        array  $industries = [],
        int    $accountManagerId = 0,
        ?int   $salesUserId = null,
        string $state = '',
        string $campaignsStatus = '',
               $leadBuyingStatus = 'all',
        string $paymentSource = 'all',
        bool   $prepaidOnly = false,
        string $superPremiumOptIn = 'all',
        bool   $restrictCurrentAccountManager = false
    ): Builder
    {
        $searchQuery = EloquentCompany::where(EloquentCompany::TABLE . '.' . EloquentCompany::TYPE, '!=', EloquentCompany::TYPE_ADMIN)
            ->with($relations)
            ->select($select);

        if (!empty($companyName)) {
            $searchQuery->where(EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_NAME, 'LIKE', "%{$companyName}%");
        } else if (!empty($entityName)) {
            $searchQuery->where(EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_LEGAL_ENTITY_NAME, 'LIKE', "%{$entityName}%");
        }

        if (!empty($companyStatuses)) {
            $searchQuery->whereIn(EloquentCompany::TABLE . '.' . EloquentCompany::STATUS, $companyStatuses);
        }

        if (!empty($companyTypes)) {
            $searchQuery->whereIn(EloquentCompany::TABLE . '.' . EloquentCompany::TYPE, $companyTypes);
        }

        if (!empty($industries)
            && count(array_intersect([Industry::KEY_SOLAR, Industry::KEY_ROOFING], $industries)) < 2) {
            if (in_array(Industry::KEY_SOLAR, $industries)) {
                $searchQuery->where(EloquentCompany::TYPE, '!=', EloquentCompany::TYPE_ROOFER);
            } else if (in_array(Industry::KEY_ROOFING, $industries)) {
                $searchQuery->where(EloquentCompany::TYPE, '=', EloquentCompany::TYPE_ROOFER);
            }
        }

        if (!empty($restrictCurrentAccountManager)) {
            $searchQuery->where(EloquentCompany::TABLE . '.' . EloquentCompany::ACCOUNT_MANAGER_ID, Auth::user()->{User::FIELD_LEGACY_USER_ID});
        }

        if (!empty($accountManagerId)
            && empty($restrictCurrentAccountManager)) {
            $searchQuery->leftJoin(DatabaseHelperService::database() . '.' . AccountManagerClient::TABLE, function($join){
                $join->on(DatabaseHelperService::database() . '.' . AccountManagerClient::TABLE . '.' . AccountManagerClient::FIELD_COMPANY_REFERENCE, '=', DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE . '.' . EloquentCompany::REFERENCE)
                     ->where(AccountManagerClient::TABLE . '.' . AccountManagerClient::FIELD_STATUS, AccountManagerClient::STATUS_ACTIVE);
            });
            if ($accountManagerId === -1) {
                $searchQuery->whereNull(AccountManagerClient::TABLE . '.' . AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID);
            } else {
                $searchQuery->where(AccountManagerClient::TABLE . '.' . AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID, $accountManagerId);
            }
        }

        if (!empty($salesUserId)) {
            if ($salesUserId === -1) {
                $searchQuery->where(function ($query) {
                    $query
                        ->whereNull(EloquentCompany::TABLE . '.' . EloquentCompany::SALES_CONSULTANT_ID)
                        ->orWhere(EloquentCompany::TABLE . '.' . EloquentCompany::SALES_CONSULTANT_ID, '=', 0);
                });
            } else {
                $searchQuery->where(EloquentCompany::TABLE . '.' . EloquentCompany::SALES_CONSULTANT_ID, '=', $salesUserId);
            }
        }

        if (!empty($state)) {
            $searchQuery->whereHas(EloquentCompany::RELATION_ADDRESSES, function ($query) use ($state) {
                $query->where(EloquentAddress::TABLE . '.' . EloquentAddress::STATE_ABBR, '=', $state);
            });
        }

        if (!empty($campaignsStatus)) {
            switch ($campaignsStatus) {
                case self::CAMPAIGN_STATUS_ACTIVE:
                    $searchQuery->whereHas(EloquentCompany::RELATION_CAMPAIGNS, function ($query) {
                        $query->where(LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE);
                    })
                        ->whereDoesntHave(EloquentCompany::RELATION_CAMPAIGNS, function ($query) {
                            $query->where(LeadCampaign::STATUS, LeadCampaign::STATUS_INACTIVE);
                        });
                    break;
                case self::CAMPAIGN_STATUS_PAUSED:
                    $searchQuery->whereHas(EloquentCompany::RELATION_CAMPAIGNS, function ($query) {
                        $query->where(LeadCampaign::STATUS, LeadCampaign::STATUS_INACTIVE);
                    })
                        ->whereDoesntHave(EloquentCompany::RELATION_CAMPAIGNS, function ($query) {
                            $query->where(LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE);
                        });
                    break;
                case self::CAMPAIGN_STATUS_ONE_PAUSED:
                    $searchQuery->whereHas(EloquentCompany::RELATION_CAMPAIGNS, function ($query) {
                        $query->where(LeadCampaign::STATUS, LeadCampaign::STATUS_INACTIVE);
                    });
                    break;
                case self::CAMPAIGN_STATUS_OVER_BUDGET:
                    $leadCampaignId = LeadCampaign::TABLE . '.' . LeadCampaign::ID;
                    $overBudgetSubTable = 'over_budget_sub_table';

                    $searchQuery->whereHas(EloquentCompany::RELATION_CAMPAIGNS, function ($query) use ($overBudgetSubTable, $leadCampaignId) {
                        $query
                            ->joinSub($this->getOverBudgetSubQuery(), $overBudgetSubTable, function ($query) use ($overBudgetSubTable) {
                                $query->on($overBudgetSubTable . '.' . LeadCampaign::ID, '=', LeadCampaign::TABLE . '.' . LeadCampaign::ID);
                            })
                            ->where(LeadCampaign::TABLE . '.' . LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE)
                            ->havingRaw(implode(' ', [
                                "COUNT(DISTINCT $leadCampaignId)",
                                "=",
                                "SUM(IF({$overBudgetSubTable}.over_budget = true, 1, 0))"
                            ]));
                    });
                    break;
                case self::CAMPAIGN_STATUS_ONE_OVER_BUDGET:
                    $overBudgetSubTable = 'over_budget_sub_table';

                    $searchQuery->whereHas(EloquentCompany::RELATION_CAMPAIGNS, function ($query) use ($overBudgetSubTable) {
                        $query
                            ->joinSub($this->getOverBudgetSubQuery(), $overBudgetSubTable, function ($query) use ($overBudgetSubTable) {
                                $query->on($overBudgetSubTable . '.' . LeadCampaign::ID, '=', LeadCampaign::TABLE . '.' . LeadCampaign::ID);
                            })
                            ->where(LeadCampaign::TABLE . '.' . LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE)
                            ->where("{$overBudgetSubTable}.over_budget", '=', true);
                    });
                    break;
            }
        }

        if (!empty($prepaidOnly)) {
            $searchQuery->whereHas(EloquentCompany::RELATION_CONFIGURATIONS, function ($query) {
                $query
                    ->where(EloquentConfiguration::TABLE . '.' . EloquentConfiguration::NAME, '=', EloquentConfiguration::NAME_IS_PREPAID)
                    ->where(EloquentConfiguration::TABLE . '.' . EloquentConfiguration::VALUE, '=', EloquentConfiguration::VALUE_ACTIVE);
            });
        }

        if ($leadBuyingStatus !== 'all') {
            $searchQuery->where(EloquentCompany::TABLE . '.' . EloquentCompany::BUYING_LEADS, '=', (int)$leadBuyingStatus);
        }

        if ($paymentSource !== 'all') {
            $searchQuery->where(EloquentCompany::TABLE . '.' . EloquentCompany::PAYMENT_SOURCE, '=', $paymentSource);
        }

        if ($superPremiumOptIn !== 'all') {
            $searchQuery->whereHas(EloquentCompany::RELATION_CAMPAIGNS, function ($query) use ($superPremiumOptIn) {
                $query->where(LeadCampaign::ALLOW_NON_BUDGET_PREMIUM_LEADS, $superPremiumOptIn);
            });
        }

        return $searchQuery;
    }

    /**
     * @return \Illuminate\Database\Query\Builder
     */
    public function getOverBudgetSubQuery(): \Illuminate\Database\Query\Builder
    {
        $lastModifiedLeadLimit = LeadCampaign::TABLE . '.' . LeadCampaign::LAST_MODIFIED_LEAD_LIMIT;
        $maxDailySpend = LeadCampaign::TABLE . '.' . LeadCampaign::MAX_DAILY_SPEND;
        $maxDailyLead = LeadCampaign::TABLE . '.' . LeadCampaign::MAX_DAILY_LEAD;
        $maxBudgetUsage = LeadCampaign::TABLE . '.' . LeadCampaign::MAXIMUM_BUDGET_USAGE;

        return DB::table(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE)
            ->select([
                LeadCampaign::TABLE . '.' . LeadCampaign::ID,
                LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID,
                LeadCampaign::TABLE . '.' . LeadCampaign::STATUS,
                DB::raw(
                    implode(
                        ' ',
                        [
                            "IF($maxDailySpend > 0, SUM(cost), COUNT(cost))", //Budget spent
                            "/ (FLOOR((UNIX_TIMESTAMP() - IF($lastModifiedLeadLimit > DATE_SUB(NOW(), INTERVAL 1 MONTH), UNIX_TIMESTAMP($lastModifiedLeadLimit), UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MONTH)))) / 86400) + 1)", //Budget timeframe
                            "* 100",
                            "/ IF($maxDailySpend > 0, $maxDailySpend, $maxDailyLead)", //Daily budget
                            "> $maxBudgetUsage", //Check if budget usage is over max budget usage
                            "AS `over_budget`"
                        ]
                    )
                )
            ])
            ->join(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignSalesTypeConfiguration::TABLE, function ($join) {
                $join->on(LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID, '=', LeadCampaign::TABLE . '.' . LeadCampaign::ID);
            })
            ->leftJoin(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE, function ($join) use ($lastModifiedLeadLimit) {
                $join->on(LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::ID, '=', EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID)
                    ->where(EloquentQuoteCompany::CHARGEABLE, true)
                    ->where(EloquentQuoteCompany::DELIVERED, true)
                    ->where(EloquentQuoteCompany::INCLUDE_IN_BUDGET, true)
                    ->whereRaw(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY . " >= IF($lastModifiedLeadLimit > DATE_SUB(NOW(), INTERVAL 1 MONTH), UNIX_TIMESTAMP($lastModifiedLeadLimit), UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MONTH)))");
            })
            ->groupBy(LeadCampaign::TABLE . '.' . LeadCampaign::ID);
    }

    /**
     * @param Builder $searchQuery
     * @param string $rejectionRateCol
     * @return Builder|\Illuminate\Database\Query\Builder
     */
    public function getRejectionRate(Builder $searchQuery, string $rejectionRateCol)
    {
        $chargeStatus = EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::CHARGE_STATUS;
        $cost = EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::COST;
        $rejectedStatus = EloquentQuoteCompany::VALUE_CHARGE_STATUS_REJECTED;

        return $searchQuery
                    ->leftJoin(EloquentQuoteCompany::TABLE, function ($query) {
                        $query
                            ->on(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::COMPANY_ID, '=', EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_ID)
                            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::DELIVERED, '=', EloquentQuoteCompany::IS_DELIVERED)
                            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::CHARGEABLE, '=', EloquentQuoteCompany::IS_CHARGEABLE)
                            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::INCLUDE_IN_BUDGET, '=', true)
                            ->where(EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', Carbon::today()->subDays(29)->timestamp);
                    })
                    ->addSelect(DB::raw("SUM(IF($chargeStatus = '$rejectedStatus', $cost, 0)) / SUM($cost) * 100 AS $rejectionRateCol"))
                    ->groupBy(EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_ID);
    }

    /**
     * @return array
     */
    public function getCompaniesSearchOptions(): array
    {
        $accountManagers = app(CompanyAccountManagerRepository::class)->getAllAccountManagers([AccountManager::RELATION_USER]);
        $states = app(DatabaseLocationRepository::class)->getStates()->toArray();
        $industries = app(ReferenceListsService::class)->industries()->pluck(Industry::FIELD_DISPLAY_NAME, Industry::FIELD_KEY)->toArray();

        $companyStatuses = array_map(
            function ($status) {
                return ucwords($status);
            },
            EloquentCompany::STATUSES
        );
        $companyStatuses = array_combine(EloquentCompany::STATUSES, $companyStatuses);

        return [
            self::SEARCH_OPTION_TYPE_ACCOUNT_MANAGERS          => $accountManagers,
            self::SEARCH_OPTION_TYPE_STATES                    => $states,
            self::SEARCH_OPTION_TYPE_CAMPAIGNS_STATUSES        => self::CAMPAIGN_STATUS_NAMES,
            self::SEARCH_OPTION_TYPE_LEAD_BUYING_STATUSES      => [
                'Leads Active'     => EloquentCompany::BUYING_LEADS_STATUS_ACTIVE,
                'Leads Paused'     => EloquentCompany::BUYING_LEADS_STATUS_PAUSED,
                'Not Buying Leads' => EloquentCompany::BUYING_LEADS_STATUS_INACTIVE,
                'Admin Locked'     => EloquentCompany::BUYING_LEADS_STATUS_ADMIN_LOCKED
            ],
            self::SEARCH_OPTION_TYPE_PAYMENT_METHODS           => EloquentCompany::PAYMENT_SOURCES,
            self::SEARCH_OPTION_TYPE_COMPANY_STATUSES          => $companyStatuses,
            self::SEARCH_OPTION_TYPE_COMPANY_TYPES             => EloquentCompany::TYPE_DISPLAY_NAMES,
            self::SEARCH_OPTION_TYPE_INDUSTRIES                => $industries
        ];
    }

    /**
     * @param int $companyId
     * @return int
     */
    public function getCompanyFailedInvoiceCount(int $companyId): int
    {
        $invoiceFailedStatuses = [
            EloquentInvoice::VALUE_STATUS_PAID_FAILED,
            EloquentInvoice::VALUE_STATUS_CHARGEBACK,
            EloquentInvoice::VALUE_STATUS_COLLECTION
        ];

        return EloquentInvoice::query()
            ->where(EloquentInvoice::TABLE . '.' . EloquentInvoice::COMPANY_ID, $companyId)
            ->whereIn(EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, $invoiceFailedStatuses)->count();
    }

    /**
     * @inheritDoc
     */
    public function updateCompanyStatus(string $companyReference, string $companyStatus): bool
    {
        return (bool)$this->apiConsumer->patch(self::API_BASE_ENDPOINT . "{$companyReference}" . self::API_UPDATE_COMPANY_STATUS_ENDPOINT,
            [
                self::REQUEST_COMPANY_STATUS => $companyStatus
            ]
        )->json(APIConsumer::RESPONSE_RESULT);
    }

    /**
     * @inheritDoc
     */
    public function updateCompanyDetails(string $companyReference, array $data): bool
    {
        return (bool)$this->apiConsumer
            ->patch(self::API_BASE_ENDPOINT . "{$companyReference}" . self::API_UPDATE_COMPANY_DETAILS_ENDPOINT, $data)
            ->json(APIConsumer::RESPONSE_RESULT);
    }

    /**
     * @inheritDoc
     */
    public function updateBasicDetails(string $companyReference, array $data): bool
    {
        DispatchPubSubEvent::dispatch(EventCategory::ADMIN2, EventName::COMPANY_BASIC_DETAILS_UPDATED, [
            'company_reference' => $companyReference,
            'data' => $data
        ]);

        return true;
    }

    /**
     * @inheritDoc
     */
    public function updateConfigurableFields(string $companyReference, array $data): bool
    {
        DispatchPubSubEvent::dispatch(
            EventCategory::ADMIN2,
            EventName::COMPANY_CONFIGURABLE_FIELDS_UPDATED,
            [
                'company_reference' => $companyReference,
                'data'              => $data
            ]
        );

        return true;
    }

    /**
     * The new query that will get companies by industry in the Admin 2.0 database.
     *
     * @param string $industry
     * @param Collection $nonPurchasingLocationCompanies
     * @return Builder
     */
    public static function getCompanyQuery(string $industry, Collection $nonPurchasingLocationCompanies): Builder
    {
        self::validateNonPurchasingLocationCompanies($nonPurchasingLocationCompanies);

        $companyLegacyIds = self::getCompanyLegacyIdsByIndustry($industry);
        $companyLegacyIds = array_intersect($nonPurchasingLocationCompanies->toArray(), $companyLegacyIds);

        if (empty($companyLegacyIds)) return EloquentCompany::query()->where(EloquentCompany::TABLE.'.'.EloquentCompany::ID, 0); // Return empty query (no companies)

        return EloquentCompany::query()
            ->whereIn(EloquentCompany::TABLE.'.'.EloquentCompany::ID, $companyLegacyIds);
    }

    /**
     * @param Collection $nonPurchasingLocationCompanies
     * @return void
     */
    private static function validateNonPurchasingLocationCompanies(Collection $nonPurchasingLocationCompanies): void
    {
        $everyNonPurchasingLocationCompanyIsAnInt = $nonPurchasingLocationCompanies->every(function ($companyId) {
            return is_int($companyId);
        });

        if (!$everyNonPurchasingLocationCompanyIsAnInt) {
            throw new InvalidArgumentException('Every company id must be an integer in "$nonPurchasingLocationCompanies".');
        }
    }

    /**
     * @param string $industry
     * @return array
     */
    private static function getCompanyLegacyIdsByIndustry(string $industry): array
    {
        try {
            \App\Models\Odin\Industry::query()->where(\App\Models\Odin\Industry::FIELD_SLUG, $industry)->firstOrFail();
        } catch (ModelNotFoundException $exception) {
            throw new InvalidArgumentException("Industry is invalid. \"$industry\" passed.");
        }

        return Company::query()
            ->select(Company::FIELD_LEGACY_ID)
            ->whereHas(Company::RELATION_INDUSTRIES, function ($query) use ($industry) {
                /**
                 * @var Builder $query
                 */
                $query->where(\App\Models\Odin\Industry::FIELD_SLUG, $industry);
            })
            ->pluck(Company::FIELD_LEGACY_ID)
            ->toArray();
    }

    private function getAccountManager(): ?User
    {
        return Auth::user()->hasRole('account-manager') ? Auth::user() : null;
    }

    private function getNonPurchasingLocationCompanies(Location $countyLocation, ?User $accountManagerUser): Collection
    {
        /** @var Collection|null $nonPurchasingLocationCompanies*/
        if ($accountManagerUser) {
            $nonPurchasingLocationCompanies = $this->nonPurchasingCompanyLocationRepository->getLegacyCompanyIdsByLocationForAccountManager($countyLocation->{Location::ID}, $accountManagerUser->id);
        }
        else {
            $nonPurchasingLocationCompanies = $this->nonPurchasingCompanyLocationRepository->getLegacyCompanyIdsByLocation($countyLocation->{Location::ID});
        }

        if (is_null($nonPurchasingLocationCompanies)) {
            return collect();
        }

        return $nonPurchasingLocationCompanies;
    }

    public function getCompaniesAgainstNonPurchasingLocationQuery(
        Location $countyLocation,
        string $industry,
        bool   $filterByAccountManager = true,
    ): Builder
    {
        $accountManagerUser = $filterByAccountManager ? $this->getAccountManager() : null;
        $nonPurchasingLocationCompanies = $this->getNonPurchasingLocationCompanies($countyLocation, $accountManagerUser);

        return self::getCompanyQuery($industry, $nonPurchasingLocationCompanies);
    }

    /**
     * @return string[]
     */
    public function getCompanyTypes(): array
    {
        return EloquentCompany::COMPANY_TYPES;
    }
    /**
     * @param int $id
     * @return mixed
     */
    public function find(int $id): mixed
    {
        return EloquentCompany::query()->find($id);
    }

    /**
     * @param int $companyId
     *
     * @return EloquentCompany
     */
    public function findOrFail(int $companyId): EloquentCompany
    {
        /** @var EloquentCompany $company */
        $company = EloquentCompany::query()->findOrFail($companyId);

        return $company;
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function getCompaniesPaymentMethodStatuses(array $companyIds): array
    {
        /**
         * Use POST in order to avoid hitting the GET query limit length
         */
        return $this->apiConsumer->post(
                self::API_GET_COMPANIES_PAYMENT_METHOD_STATUSES,
                [
                    self::REQUEST_COMPANY_IDS => $companyIds
                ],
                360
            )->json(APIConsumer::RESPONSE_RESULT) ?? [];
    }

    /**
     * @inheritDoc
     */
    public function setLeadBudgetOfCreatedCompany(string $companyReference, array $data): array
    {
        return $this->apiConsumer
            ->patch(self::API_BASE_ENDPOINT . "{$companyReference}" . self::API_SET_COMPANY_LEAD_BUDGET_ENDPOINT, $data)
            ->json() ?? [];
    }

    /**
     * @inheritDoc
     */
    public function setLeadContactOfCreatedCompany(string $companyReference, array $data): array
    {
        return $this->apiConsumer
            ->patch(self::API_BASE_ENDPOINT . "{$companyReference}" . self::API_SET_COMPANY_LEAD_CONTACT_ENDPOINT, $data)
            ->json() ?? [];
    }

    /**
     * Handles preparing company type for Legacy based on the given set of industries.
     * In case the industry is solar/roofing, the company type for Legacy would be prepared as installer/roofer respectively.
     * The company type would be "multi" if a company offers in multiple industries, or in an industry other than solar/roofing.
     *
     * @inheritDoc
     */
    public function getLegacyCompanyTypeFromIndustryIds(array $industryIds): string
    {
        /** @var Collection<OdinIndustry> $industries */
        $industries = OdinIndustry::query()->whereIn(OdinIndustry::FIELD_ID, $industryIds)->get();

        if ($industries->isEmpty()) throw new InvalidArgumentException('No industries found');

        if ($industries->count() === 1) {
            $industry = $industries->first()->{OdinIndustry::FIELD_NAME};
            return match ($industry) {
                OdinIndustry::INDUSTRY_SOLAR    => EloquentCompany::TYPE_INSTALLER,
                OdinIndustry::INDUSTRY_ROOFING  => EloquentCompany::TYPE_ROOFER,
                default                         => EloquentCompany::TYPE_MULTI
            };
        } else {
            return EloquentCompany::TYPE_MULTI;
        }
    }

    /**
     * @inheritDoc
     */
    public function setCompanyServices(string $companyReference, array $services): array
    {
        return $this->apiConsumer
                    ->post(
                        route : self::API_BASE_ENDPOINT . "{$companyReference}" . self::API_SET_COMPANY_SERVICES_ENDPOINT,
                        data  : $services)
                    ->json() ?? [];
    }

    /**
     * @inheritDoc
     */
    public function setCompanyType(string $companyReference, array $data): array
    {
        return $this->apiConsumer
                    ->patch(
                        route : self::API_BASE_ENDPOINT . "{$companyReference}" . self::API_UPDATE_COMPANY_BASIC_DETAILS_ENDPOINT,
                        data  : $data)
                    ->json() ?? [];
    }

    /**
     * @inheritDoc
     */
    public function removeCompanyService(string $companyReference, array $service): array
    {
        return $this->apiConsumer
                    ->delete(
                        route : self::API_BASE_ENDPOINT . "{$companyReference}" . self::API_DELETE_COMPANY_SERVICE_ENDPOINT,
                        data  : $service)
                    ->json() ?? [];
    }

    /**
     * @param EloquentCompany $company
     * @return int
     */
    public function getActiveCampaignsCount(EloquentCompany $company): int
    {
        return $company
            ->{EloquentCompany::RELATION_MI_COMPANY}
            ->{Company::RELATION_FUTURE_CAMPAIGNS}
            ->where(CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE)
            ->count();
    }

    /**
     * @param EloquentCompany $company
     * @return int
     */
    public function getCampaignsWithReactivationCount(EloquentCompany $company): int
    {
        return $company
            ->{EloquentCompany::RELATION_MI_COMPANY}
            ->{Company::RELATION_FUTURE_CAMPAIGNS}()
            ->where(CompanyCampaign::FIELD_STATUS, CampaignStatus::PAUSED_TEMPORARILY)
            ->count();
    }

    /**
     * @param EloquentCompany $company
     * @return int
     */
    public function getLeadsPurchasedCount(EloquentCompany $company): int
    {
        return $company->{EloquentCompany::RELATION_QUOTE_COMPANIES}()
            ->where(EloquentQuoteCompany::CHARGEABLE, EloquentQuoteCompany::IS_CHARGEABLE)
            ->where(EloquentQuoteCompany::DELIVERED, EloquentQuoteCompany::IS_DELIVERED)
            ->count();
    }

    /**
     * @param EloquentCompany $company
     * @return bool
     */
    public function hasValidPaymentMethod(EloquentCompany $company): bool
    {
        if($company->{EloquentCompany::ALLOW_LEAD_SALES_WITHOUT_CC}) {
            return true;
        }

        $exists = $company->paymentMethods()->exists();

        if (!$exists) return false;

        try {
            $paymentMethods = $company->paymentMethods()->preferred();
        } catch (Exception $e) {
            logger()->error("Check payment method Installer [{$company->companyid}]: {$e->getMessage()}");

            return false;
        }

        $date = Carbon::now()->startOfMonth();

        foreach ($paymentMethods as $paymentMethod) {
            $expireDate = Carbon::createFromFormat('y/m', $paymentMethod->expiryYear() . '/' . $paymentMethod->expiryMonth())->startOfMonth();
            if ($date <= $expireDate) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param EloquentCompany $company
     * @return int
     */
    public function getBasicStatus(EloquentCompany $company): int
    {
        return LegacyCompanyBasicStatus::get(
            adminApproved: $company->is_admin_approved,
            validPaymentMethod: $this->hasValidPaymentMethod($company),
            activeCampaignsCount: $this->getActiveCampaignsCount($company),
            reactivateCampaignsInFutureCount: $this->getCampaignsWithReactivationCount($company),
            leadsPurchasedCount: $this->getLeadsPurchasedCount($company),
            imported: $company->imported
        );
    }

    /**
     * @param EloquentCompany $company
     * @return int|null
     */
    public function getAdminStatus(EloquentCompany $company): ?int
    {
        return LegacyCompanyAdminStatus::get(
            archived: $company->is_archived,
            status: $company->status,
            adminLocked: $company->is_admin_locked
        );
    }

    /**
     * This should only be used for "Expert" ratings
     *
     * @param string $companyType
     * @return array
     */
    public function getIndustriesForCompanyType(string $companyType): array
    {
        $industries = IndustryType::with(IndustryType::RELATION_INDUSTRY)->get();

        switch ($companyType) {
            case EloquentCompany::TYPE_ROOFER:
                $industries = $industries->where('industry.key', 'roofing')->where('key', 'installer');
                break;

            case EloquentCompany::TYPE_INSTALLER:
            case EloquentCompany::TYPE_AGGREGATOR:
                $industries = $industries->where('industry.key', 'solar')->where('key', 'installer');
                break;

            case EloquentCompany::TYPE_MANUFACTURER:
                $industries = $industries->where('industry.key', 'solar')->where('key', 'panel-manufacturer');
        }

        return $industries->pluck('id')->toArray();
    }

    /**
     * @param EloquentCompany $company
     * @return bool
     */
    private function usesFutureCampaigns(EloquentCompany $company): bool
    {
        return $company
            ->{EloquentCompany::RELATION_MI_COMPANY}
            ->{Company::RELATION_FUTURE_CAMPAIGNS}
            ->count();
    }
}
