<?php

namespace Database\Seeders;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

class PermissionsSeeder extends Seeder
{
    /**
     * Create the initial roles and permissions.
     *
     * @return void
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app(PermissionRegistrar::class)->forgetCachedPermissions();

        $this->call(PermissionManagementSeeder::class);
        $this->call(PermissionConsumerProductSeeder::class);
        $this->call(PermissionCompanyLeadUpdateChargeableStatusSeeder::class);
        $this->call(LeadAllocationAndAdjustmentPermissionSeeder::class);
        $this->call(RecycleLeadsPermissionSeeder::class);
        $this->call(TemplateManagementAndSmsTemplatePermissionSeeder::class);
        $this->call(CompanyOverviewPermissionsSeeder::class);

        $this->call(RemoveOrRenameOldPermissionsSeeder::class);

        // create permissions
        foreach (PermissionType::cases() as $permissionType) {
            Permission::findOrCreate($permissionType->value);
        }

        // create roles and assign existing permissions
        $basic = Role::findOrCreate(RoleType::BASIC->value);
        $basic->givePermissionTo(PermissionType::DASHBOARD->value);

        $admin = Role::findOrCreate(RoleType::ADMIN->value);
        $admin->givePermissionTo(
            [
                PermissionType::DASHBOARD->value,
                PermissionType::ADMIN->value,
                PermissionType::LEAD_PROCESSING->value,
                PermissionType::LEAD_PROCESSING_MANAGEMENT->value,
                PermissionType::INDUSTRY_MANAGEMENT->value,
                PermissionType::INDUSTRY_CONFIGURATION->value,
                PermissionType::EMAIL_TEMPLATE->value,
                PermissionType::COMPANY->value,
                PermissionType::ADVERTISING->value,
                PermissionType::ADVERTISING_ADMIN->value,
                PermissionType::TASK->value,
                PermissionType::CMS->value,
                PermissionType::TEAM_MANAGEMENT->value,
                PermissionType::VIEW_BUNDLE->value,
                PermissionType::FLOW_MANAGEMENT->value,
                PermissionType::COMPANY_QUALITY_SCORE_MANAGEMENT->value,
                PermissionType::OPPORTUNITY_NOTIFICATION->value,
                PermissionType::RULESET_MANAGEMENT->value,
                PermissionType::SILO_MANAGEMENT_VIEW->value,
                PermissionType::SILO_MANAGEMENT_CREATE->value,
                PermissionType::SILO_MANAGEMENT_UPDATE->value,
                PermissionType::SILO_MANAGEMENT_DELETE->value,
                PermissionType::COMPANY_EXPORT_DATA->value,
                PermissionType::EXPERT_REVIEWS_VIEW->value,
                PermissionType::EXPERT_REVIEWS_CREATE->value,
                PermissionType::EXPERT_REVIEWS_UPDATE->value,
                PermissionType::EXPERT_REVIEWS_DELETE->value,
                PermissionType::USER_MANAGEMENT->value,
                PermissionType::PERMISSION_TEST_LEADS->value,
                PermissionType::COMPANY_LINK_VIEW->value,
                PermissionType::COMPANY_LINK_CREATE->value,
                PermissionType::COMPANY_LINK_DELETE->value,
                PermissionType::COMPANY_CONFIGURE->value,
                PermissionType::COMPANY_UPDATE_INDUSTRIES->value,
                PermissionType::GLOBAL_CONFIGURATIONS_VIEW->value,
                PermissionType::GLOBAL_CONFIGURATIONS_EDIT->value,
                PermissionType::CONTRACT_MANAGEMENT_VIEW->value,
                PermissionType::CONTRACT_MANAGEMENT_EDIT->value,
                PermissionType::CONTRACT_MANAGEMENT_SEND->value,
                PermissionType::COMPANY_USERS_VIEW->value,
                PermissionType::EMAIL_TEMPLATE_DELETE->value,
                PermissionType::COMPANY_USERS_VIEW->value,
                PermissionType::CAMPAIGNS_CREATE->value,
                PermissionType::CAMPAIGNS_UPDATE->value,
                PermissionType::CAMPAIGNS_DELETE->value,
                PermissionType::CAMPAIGNS_VIEW->value,
                PermissionType::VIEW_LEAD_PII->value,
                PermissionType::ZIP_CODE_TARGETING_EXCEPTION->value,
                PermissionType::PRIVACY_MANAGEMENT_VIEW->value,
                PermissionType::PRIVACY_MANAGEMENT_REQUEST_CREATE->value,
                PermissionType::PRIVACY_MANAGEMENT_REQUEST_SEARCH->value,
                PermissionType::PRIVACY_MANAGEMENT_REQUEST_REDACT->value,
                PermissionType::COMPANY_CAMPAIGN_DELIVERY_LOGS_VIEW->value,
                PermissionType::COMPANY_MERGE->value,
                PermissionType::ACTIVITY_LOGS_CAMPAIGNS_VIEW->value,
                PermissionType::RELATIONSHIP_MANAGER_VIEW->value,
                PermissionType::RELATIONSHIP_MANAGER_EDIT->value,
                PermissionType::RELATIONSHIP_MANAGER_CREATE->value,
                PermissionType::RELATIONSHIP_MANAGER_DELETE->value,
                PermissionType::COMPANY_BASIC_STATUS_EDIT->value,
                PermissionType::CONSUMER_SEARCH_EXPORT->value,
                PermissionType::COMPANY_CAMPAIGN_CUSTOM_PRICING_LOGS_VIEW->value,
                PermissionType::AFFILIATES->value,
                PermissionType::MARKETING_CAMPAIGN_VIEW->value,
                PermissionType::MARKETING_CAMPAIGNS_EDIT->value,
                PermissionType::MARKETING_CAMPAIGNS_CREATE->value,
                PermissionType::CONSUMER_SEARCH->value,
                PermissionType::AFFILIATES_SHADOW->value,
                PermissionType::AFFILIATES_CREATE->value,
                PermissionType::AFFILIATES_UPDATE->value,
                PermissionType::AFFILIATES_DELETE->value,
                PermissionType::MINIMUM_PRICE_MANAGEMENT_VIEW->value,
                PermissionType::MINIMUM_PRICE_MANAGEMENT_EDIT->value,
                PermissionType::COMPANY_BASIC_STATUS_EDIT->value,
                PermissionType::COMPANY_ADMIN_STATUS_EDIT->value,
                PermissionType::CONSUMER_REVIEWS_MANAGE->value,
                PermissionType::COMPANY_DELETE->value,
                PermissionType::COMPANY_METRICS_VIEW->value,
                PermissionType::COMPANY_METRICS_EDIT->value,
                PermissionType::COMPANY_METRICS_SEND->value,
                PermissionType::FLOW_ENGINES_CONFIGURABLE_VARIABLES_VIEW->value,
                PermissionType::FLOW_ENGINES_CONFIGURABLE_VARIABLES_SAVE->value,
                PermissionType::FLOW_ENGINES_CONFIGURABLE_VARIABLES_DELETE->value,
                PermissionType::REVENUE_STREAMS_REPORT_VIEW->value,
            ]
        );

        $leadProcessor = Role::findOrCreate(RoleType::LEAD_PROCESSOR->value);
        $leadProcessor->givePermissionTo(
            [
                PermissionType::DASHBOARD->value,
                PermissionType::LEAD_PROCESSING->value,
            ]
        );

        $leadProcessingManager = Role::findOrCreate(RoleType::LEAD_PROCESSING_MANAGEMENT->value);
        $leadProcessingManager->givePermissionTo(
            [
                PermissionType::DASHBOARD->value,
                PermissionType::LEAD_PROCESSING->value,
                PermissionType::LEAD_PROCESSING_MANAGEMENT->value,
                PermissionType::TEAM_MANAGEMENT->value,
            ]
        );

        $salesBaitManager = Role::findOrCreate(RoleType::SALES_BAIT_MANAGEMENT->value);
        $salesBaitManager->givePermissionTo(
            [
                PermissionType::DASHBOARD->value,
                PermissionType::ADMIN->value,
                PermissionType::EMAIL_TEMPLATE->value,
                PermissionType::TEAM_MANAGEMENT->value,
            ]
        );

        $industryManager = Role::findOrCreate(RoleType::INDUSTRY_MANAGEMENT->value);
        $industryManager->givePermissionTo(
            [
                PermissionType::DASHBOARD->value,
                PermissionType::INDUSTRY_MANAGEMENT->value,
            ]
        );

        $bundleAdmin = Role::findOrCreate(RoleType::BUNDLE_ADMIN->value);
        $bundleAdmin->givePermissionTo(
            [
                PermissionType::VIEW_BUNDLE->value,
                PermissionType::MANAGE_BUNDLE->value,
                PermissionType::MANAGE_BUNDLE_INVOICE->value,
            ]
        );

        $bundleIssuer = Role::findOrCreate(RoleType::BUNDLE_ISSUER->value);
        $bundleIssuer->givePermissionTo(
            [
                PermissionType::VIEW_BUNDLE->value,
                PermissionType::MANAGE_BUNDLE_INVOICE->value,
            ]
        );

        $emailTemplateAdmin = Role::findOrCreate(RoleType::EMAIL_TEMPLATE_ADMIN->value);
        $emailTemplateAdmin->givePermissionTo(
            [
                PermissionType::DASHBOARD->value,
                PermissionType::EMAIL_TEMPLATE->value,
            ]
        );

        $opportunityNotificationAdmin = Role::findOrCreate(RoleType::OPPORTUNITY_NOTIFICATION_ADMIN->value);
        $opportunityNotificationAdmin->givePermissionTo(
            [
                PermissionType::DASHBOARD->value,
                PermissionType::EMAIL_TEMPLATE->value,
                PermissionType::RULESET_MANAGEMENT->value,
                PermissionType::OPPORTUNITY_NOTIFICATION->value,
            ]
        );

        $hrManager = Role::findOrCreate(RoleType::HR_MANAGER->value);
        $hrManager->givePermissionTo(
            [
                PermissionType::DASHBOARD->value,
                PermissionType::SALES_MANAGEMENT->value,
            ]
        );

        $alertProcessor = Role::findOrCreate(RoleType::ALERT_PROCESSOR->value);
        $alertProcessor->givePermissionTo(
            [
                PermissionType::DASHBOARD->value,
            ]
        );
        $lookerAdminRole = Role::findOrCreate(RoleType::LOOKER_ADMIN->value);
        $lookerAdminRole->givePermissionTo(
            [
                PermissionType::VIEW_LOOKER_REPORT->value,
            ]
        );

        $contractManager = Role::findOrCreate(RoleType::CONTRACT_MANAGER->value);
        $contractManager->givePermissionTo(
            [
                PermissionType::CONTRACT_MANAGEMENT_VIEW->value,
                PermissionType::CONTRACT_MANAGEMENT_EDIT->value,
                PermissionType::CONTRACT_MANAGEMENT_SEND->value,
                PermissionType::CONTRACT_MANAGEMENT_UPLOAD->value,
                PermissionType::CONTRACT_MANAGEMENT_BYPASS->value,
            ]
        );
        $mailboxUser = Role::findOrCreate(RoleType::MAILBOX_USER->value);
        $mailboxUser->givePermissionTo(
            [
                PermissionType::DASHBOARD->value,
                PermissionType::MAILBOX_LIST_EMAILS->value,
                PermissionType::MAILBOX_SEND_EMAILS->value,
            ]
        );

        $privacyOfficer = Role::findOrCreate(RoleType::PRIVACY_OFFICER->value);
        $privacyOfficer->givePermissionTo(
            [
                PermissionType::PRIVACY_MANAGEMENT_VIEW->value,
                PermissionType::PRIVACY_MANAGEMENT_REQUEST_CREATE->value,
                PermissionType::PRIVACY_MANAGEMENT_REQUEST_SEARCH->value,
            ]
        );
        $privacyAdmin = Role::findOrCreate(RoleType::PRIVACY_ADMIN->value);
        $privacyAdmin->givePermissionTo(
            [
                PermissionType::PRIVACY_MANAGEMENT_VIEW->value,
                PermissionType::PRIVACY_MANAGEMENT_REQUEST_CREATE->value,
                PermissionType::PRIVACY_MANAGEMENT_REQUEST_SEARCH->value,
                PermissionType::PRIVACY_MANAGEMENT_REQUEST_REDACT->value,
            ]
        );

        $leadRefundsReviewer = Role::findOrCreate(RoleType::LEAD_REFUNDS_REVIEWER->value);
        $leadRefundsReviewer->givePermissionTo([PermissionType::LEAD_REFUNDS_VIEW]);

        $leadRefundsRequester = Role::findOrCreate(RoleType::LEAD_REFUNDS_REQUESTER->value);
        $leadRefundsRequester->givePermissionTo([PermissionType::LEAD_REFUNDS_VIEW]);

        $leadRefundsViewer = Role::findOrCreate(RoleType::LEAD_REFUNDS_VIEWER->value);
        $leadRefundsViewer->givePermissionTo([PermissionType::LEAD_REFUNDS_VIEW]);

        $marketingManager = Role::findOrCreate(RoleType::MARKETING_MANAGER->value);

        $marketingManager->givePermissionTo([
            PermissionType::MARKETING_CAMPAIGN_VIEW->value,
            PermissionType::MARKETING_CAMPAIGNS_EDIT->value,
            PermissionType::MARKETING_CAMPAIGNS_CREATE->value,
        ]);

        $relationshipManagerAdmin = Role::findOrCreate(RoleType::RELATIONSHIP_MANAGER_ADMIN->value);
        $relationshipManagerAdmin->givePermissionTo(
            [
                PermissionType::RELATIONSHIP_MANAGER_VIEW->value,
                PermissionType::RELATIONSHIP_MANAGER_EDIT->value,
                PermissionType::RELATIONSHIP_MANAGER_CREATE->value,
                PermissionType::RELATIONSHIP_MANAGER_DELETE->value,
            ]
        );
        $financeOwner = Role::findOrCreate(RoleType::FINANCE_OWNER->value);
        $financeOwner->givePermissionTo(
            [
                PermissionType::BILLING_CREATE_INVOICE->value,
                PermissionType::BILLING_CANCEL_INVOICE->value,
                PermissionType::BILLING_VIEW_INVOICE->value,
                PermissionType::BILLING_VIEW_INVOICE_REPORT->value,
                PermissionType::BILLING_MANAGEMENT_VIEW->value,
                PermissionType::BILLING_VIEW_COMPANY_CREDITS->value,
                PermissionType::BILLING_APPLY_COMPANY_CREDITS->value,
                PermissionType::BILLING_EXTEND_COMPANY_CREDITS->value,
                PermissionType::BILLING_EXPIRE_COMPANY_CREDITS->value,
                PermissionType::BILLING_CREATE_CREDIT_TYPE->value,
                PermissionType::BILLING_VIEW_CREDIT_TYPE->value,
                PermissionType::BILLING_UPDATE_CREDIT_TYPE->value,
                PermissionType::BILLING_REPORTS_VIEW->value,
                PermissionType::BILLING_ACTION_APPROVALS_VIEW->value,
                PermissionType::BILLING_ACTION_APPROVALS_REQUEST->value,
                PermissionType::BILLING_ACTION_APPROVALS_REVIEW->value,
                PermissionType::BILLING_PROFILES_VIEW->value,
                PermissionType::BILLING_PROFILES_CREATE->value,
                PermissionType::BILLING_PROFILES_UPDATE->value,
                PermissionType::BILLING_PROFILE_POLICIES_VIEW->value,
                PermissionType::BILLING_PROFILE_POLICIES_SAVE->value,
                PermissionType::BILLING_INVOICE_TEMPLATES_VIEW->value,
                PermissionType::BILLING_INVOICE_TEMPLATES_SAVE->value,
                PermissionType::BILLING_INVOICE_TRANSACTIONS_VIEW->value,
                PermissionType::COMPANY_MANAGE_UNDER_REVIEW_LEADS->value,
                PermissionType::COMPANY_PRE_ASSIGN_AM->value,
                PermissionType::CAMPAIGNS_EXCLUDE_FROM_AD_AUTOMATION->value,
                PermissionType::COMPANY_COMMISSION_VIEW->value,
                PermissionType::COMPANY_USER_RELATIONSHIPS_EDIT->value,
                PermissionType::BILLING_OVERVIEW_VIEW->value,
                PermissionType::BILLING_INVOICES_VIEW->value,
                PermissionType::BILLING_ACTION_REQUESTS_VIEW->value,
                PermissionType::BILLING_BILLING_PROFILES_VIEW->value,
                PermissionType::BILLING_REPORTS_RECEIVABLE_VIEW->value,
                PermissionType::BILLING_REPORTS_REVENUE_VIEW->value,
                PermissionType::BILLING_REPORTS_AGED_VIEW->value,
                PermissionType::BILLING_REPORTS_BALANCE_VIEW->value,
                PermissionType::BILLING_REPORTS_REFUNDS_VIEW->value,
                PermissionType::BILLING_REPORTS_COLLECTIONS_VIEW->value,
                PermissionType::BILLING_REPORTS_WRITE_OFFS_VIEW->value,
                PermissionType::BILLING_REPORTS_CHARGEBACKS_VIEW->value,
                PermissionType::BILLING_REPORTS_CREDIT_MOVEMENTS_VIEW->value,
                PermissionType::BILLING_REPORTS_COMPANY_OVERVIEW_VIEW->value,
                PermissionType::BILLING_REPORTS_CREDIT_OUTSTANDING_VIEW->value,
                PermissionType::BILLING_REPORTS_OVERDUE_INVOICES_VIEW->value,
                PermissionType::BILLING_REPORTS_EXPORT->value,
            ]
        );

        $financeController = Role::findOrCreate(RoleType::FINANCE_CONTROLLER->value);
        $financeController->givePermissionTo(
            [
                PermissionType::BILLING_CREATE_INVOICE->value,
                PermissionType::BILLING_CANCEL_INVOICE->value,
                PermissionType::BILLING_VIEW_INVOICE->value,
                PermissionType::BILLING_VIEW_INVOICE_REPORT->value,
                PermissionType::BILLING_MANAGEMENT_VIEW->value,
                PermissionType::BILLING_VIEW_COMPANY_CREDITS->value,
                PermissionType::BILLING_APPLY_COMPANY_CREDITS->value,
                PermissionType::BILLING_EXTEND_COMPANY_CREDITS->value,
                PermissionType::BILLING_EXPIRE_COMPANY_CREDITS->value,
                PermissionType::BILLING_CREATE_CREDIT_TYPE->value,
                PermissionType::BILLING_VIEW_CREDIT_TYPE->value,
                PermissionType::BILLING_UPDATE_CREDIT_TYPE->value,
                PermissionType::BILLING_REPORTS_VIEW->value,
                PermissionType::BILLING_PROFILES_VIEW->value,
                PermissionType::BILLING_PROFILES_CREATE->value,
                PermissionType::BILLING_PROFILES_UPDATE->value,
                PermissionType::BILLING_PROFILE_POLICIES_VIEW->value,
                PermissionType::BILLING_PROFILE_POLICIES_SAVE->value,
                PermissionType::BILLING_INVOICE_TEMPLATES_VIEW->value,
                PermissionType::BILLING_INVOICE_TEMPLATES_SAVE->value,
                PermissionType::BILLING_INVOICE_TRANSACTIONS_VIEW->value,
                PermissionType::BILLING_ACTION_APPROVALS_VIEW->value,
                PermissionType::BILLING_ACTION_APPROVALS_REQUEST->value,
                PermissionType::BILLING_INVOICES_VIEW->value,
                PermissionType::BILLING_ACTION_REQUESTS_VIEW->value,
                PermissionType::BILLING_BILLING_PROFILES_VIEW->value,
                PermissionType::BILLING_REPORTS_RECEIVABLE_VIEW->value,
                PermissionType::BILLING_REPORTS_AGED_VIEW->value,
                PermissionType::BILLING_REPORTS_CREDIT_MOVEMENTS_VIEW->value,
                PermissionType::BILLING_REPORTS_OVERDUE_INVOICES_VIEW->value,
                PermissionType::BILLING_REPORTS_CREDIT_OUTSTANDING_VIEW->value,
            ]
        );

        $financeAnalyst = Role::findOrCreate(RoleType::FINANCE_ANALYST->value);
        $financeAnalyst->givePermissionTo(
            [
                PermissionType::BILLING_INVOICES_VIEW->value,
                PermissionType::BILLING_ACTION_REQUESTS_VIEW->value,
                PermissionType::BILLING_BILLING_PROFILES_VIEW->value,
                PermissionType::BILLING_REPORTS_RECEIVABLE_VIEW->value,
                PermissionType::BILLING_REPORTS_AGED_VIEW->value,
                PermissionType::BILLING_REPORTS_CREDIT_MOVEMENTS_VIEW->value,
                PermissionType::BILLING_REPORTS_CREDIT_OUTSTANDING_VIEW->value,
                PermissionType::BILLING_VIEW_INVOICE->value,
                PermissionType::BILLING_VIEW_INVOICE_REPORT->value,
                PermissionType::BILLING_MANAGEMENT_VIEW->value,
                PermissionType::BILLING_VIEW_COMPANY_CREDITS->value,
                PermissionType::BILLING_VIEW_CREDIT_TYPE->value,
                PermissionType::BILLING_REPORTS_VIEW->value,
                PermissionType::BILLING_PROFILES_VIEW->value,
                PermissionType::BILLING_PROFILE_POLICIES_VIEW->value,
                PermissionType::BILLING_INVOICE_TEMPLATES_VIEW->value,
                PermissionType::BILLING_INVOICE_TRANSACTIONS_VIEW->value,
                PermissionType::BILLING_ACTION_APPROVALS_VIEW->value,
                PermissionType::BILLING_ACTION_APPROVALS_REQUEST->value,
                PermissionType::BILLING_REPORTS_OVERDUE_INVOICES_VIEW->value
            ]
        );

        $financeViewer = Role::findOrCreate(RoleType::FINANCE_VIEWER->value);
        $financeViewer->givePermissionTo([
            PermissionType::BILLING_REPORTS_EXPORT->value,
            PermissionType::BILLING_MANAGEMENT_VIEW->value,
            PermissionType::BILLING_OVERVIEW_VIEW->value,
            PermissionType::BILLING_INVOICES_VIEW->value,
            PermissionType::BILLING_ACTION_REQUESTS_VIEW->value,
            PermissionType::BILLING_BILLING_PROFILES_VIEW->value,
            PermissionType::BILLING_REPORTS_RECEIVABLE_VIEW->value,
            PermissionType::BILLING_REPORTS_REVENUE_VIEW->value,
            PermissionType::BILLING_REPORTS_AGED_VIEW->value,
            PermissionType::BILLING_REPORTS_BALANCE_VIEW->value,
            PermissionType::BILLING_INVOICE_TRANSACTIONS_VIEW->value,
            PermissionType::BILLING_REPORTS_REFUNDS_VIEW->value,
            PermissionType::BILLING_REPORTS_COLLECTIONS_VIEW->value,
            PermissionType::BILLING_REPORTS_WRITE_OFFS_VIEW->value,
            PermissionType::BILLING_REPORTS_CHARGEBACKS_VIEW->value,
            PermissionType::BILLING_REPORTS_CREDIT_MOVEMENTS_VIEW->value,
            PermissionType::BILLING_REPORTS_COMPANY_OVERVIEW_VIEW->value,
            PermissionType::BILLING_REPORTS_CREDIT_OUTSTANDING_VIEW->value,
            PermissionType::BILLING_REPORTS_OVERDUE_INVOICES_VIEW->value,

        ]);

        $affiliatesAdmin = Role::findOrCreate(RoleType::AFFILIATES_ADMIN->value);
        $affiliatesAdmin->givePermissionTo(
            [
                PermissionType::AFFILIATES->value,
                PermissionType::AFFILIATES_SHADOW->value,
                PermissionType::AFFILIATES_CREATE->value,
                PermissionType::AFFILIATES_UPDATE->value,
                PermissionType::AFFILIATES_DELETE->value,
            ]
        );

        Role::findOrCreate(RoleType::DEACTIVATED->value);

        $qualityAssuranceManager = Role::findOrCreate(RoleType::QUALITY_ASSURANCE_MANAGER->value);
        $qualityAssuranceManager->givePermissionTo(
            [
                PermissionType::AGED_LEAD_QUEUE_REFRESH_QUEUE->value,
                PermissionType::GLOBAL_CONFIGURATIONS_EDIT->value,
                PermissionType::GLOBAL_CONFIGURATIONS_VIEW->value,
            ]
        );

        Role::findOrCreate(RoleType::SALES_MANAGER->value)->givePermissionTo([
            PermissionType::COMPANY_PRE_ASSIGN_AM->value
        ]);

        Role::findOrCreate(RoleType::QA_AUTOMATION_MANAGER->value)->givePermissionTo([
            PermissionType::QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_VIEW->value,
            PermissionType::QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_EDIT->value,
            PermissionType::QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_DELETE->value,
            PermissionType::QA_AUTOMATION_RULE_MANAGEMENT_VIEW->value,
            PermissionType::QA_AUTOMATION_RULE_MANAGEMENT_EDIT->value,
            PermissionType::QA_AUTOMATION_RULE_MANAGEMENT_DELETE->value,
        ]);
    }
}
