<?php

namespace Database\Seeders;

use App\Models\ClientToken;
use App\Models\ClientTokenService;
use Illuminate\Database\Seeder;

class FlowEnginesClientTokenSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $tokens = [
            ClientTokenService::FLOW_ENGINES_API_SERVICE_KEY     => 'Flow Engines',
            ClientTokenService::DEV_FLOW_ENGINES_API_SERVICE_KEY => 'Dev Flow Engines',
        ];

        foreach ($tokens as $key => $name) {
            $tokenService = ClientTokenService::query()
                ->updateOrCreate([
                    ClientTokenService::FIELD_SERVICE_KEY  => $key,
                    ClientTokenService::FIELD_SERVICE_NAME => $name,
                ]);

            if (!$tokenService->clientToken) {
                $tokenService->clientToken()->create([
                    ClientToken::FIELD_CLIENT_TOKEN => bin2hex(random_bytes(64)),
                ]);
            }
        }

    }
}
