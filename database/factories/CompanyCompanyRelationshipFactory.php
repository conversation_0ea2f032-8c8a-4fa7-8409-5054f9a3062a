<?php

namespace Database\Factories;

use App\Enums\Company\CompanyRelationshipEnum;
use App\Models\CompanyCompanyRelationship;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class CompanyCompanyRelationshipFactory extends Factory
{
    protected $model = CompanyCompanyRelationship::class;

    public function definition(): array
    {
        return [
            'relationship' => $this->faker->randomElement(CompanyRelationshipEnum::cases()),
            'created_at'   => Carbon::now(),
            'updated_at'   => Carbon::now(),

            'company_id'        => Company::factory(),
            'target_company_id' => Company::factory(),
            'created_by_id'     => User::factory(),
        ];
    }
}
