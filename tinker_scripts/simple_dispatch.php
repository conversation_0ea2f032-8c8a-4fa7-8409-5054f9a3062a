<?php

// Simple one-liner to dispatch the job with an existing prospect
// Copy and paste this into tinker:

// Option 1: Use existing prospect (replace 1 with actual prospect ID)
App\Jobs\Prospects\VerifyNewBuyerProspectJob::dispatch(1);

// Option 2: Create a new prospect and dispatch job
$prospect = App\Models\Prospects\NewBuyerProspect::factory()->create(['decision_maker_phone' => '+1234567890']); App\Jobs\Prospects\VerifyNewBuyerProspectJob::dispatch($prospect->id); echo "Dispatched job for prospect ID: {$prospect->id}";

// Option 3: Get first prospect and dispatch
$prospect = App\Models\Prospects\NewBuyerProspect::first(); if($prospect) { App\Jobs\Prospects\VerifyNewBuyerProspectJob::dispatch($prospect->id); echo "Dispatched job for prospect ID: {$prospect->id}"; } else { echo "No prospects found"; }
