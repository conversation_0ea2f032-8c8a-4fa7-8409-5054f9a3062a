<?php

use App\Jobs\Prospects\VerifyNewBuyerProspectJob;
use App\Models\Prospects\NewBuyerProspect;

/**
 * Tinker script to run all current NewBuyerProspects through the VerifyNewBuyerProspectJob
 * 
 * Usage:
 * php artisan tinker
 * include 'tinker_scripts/verify_all_new_buyer_prospects.php';
 */

echo "Starting verification of all NewBuyerProspects...\n";

// Get all NewBuyerProspects
$prospects = NewBuyerProspect::all();
$totalCount = $prospects->count();

echo "Found {$totalCount} NewBuyerProspects to verify.\n";

if ($totalCount === 0) {
    echo "No prospects found. Exiting.\n";
    return;
}

// Ask for confirmation before proceeding
echo "This will dispatch {$totalCount} verification jobs. Continue? (y/N): ";
$handle = fopen("php://stdin", "r");
$confirmation = trim(fgets($handle));
fclose($handle);

if (strtolower($confirmation) !== 'y' && strtolower($confirmation) !== 'yes') {
    echo "Operation cancelled.\n";
    return;
}

$processedCount = 0;
$errorCount = 0;

echo "Dispatching verification jobs...\n";

foreach ($prospects as $prospect) {
    try {
        // Dispatch the verification job
        VerifyNewBuyerProspectJob::dispatch($prospect->id);
        $processedCount++;
        
        // Show progress every 10 prospects
        if ($processedCount % 10 === 0) {
            echo "Dispatched {$processedCount}/{$totalCount} jobs...\n";
        }
        
    } catch (Exception $e) {
        $errorCount++;
        echo "Error dispatching job for prospect ID {$prospect->id}: " . $e->getMessage() . "\n";
    }
}

echo "\n=== Summary ===\n";
echo "Total prospects: {$totalCount}\n";
echo "Jobs dispatched successfully: {$processedCount}\n";
echo "Errors: {$errorCount}\n";

if ($processedCount > 0) {
    echo "\nAll verification jobs have been dispatched to the queue.\n";
    echo "You can monitor the queue status with: php artisan queue:work\n";
    echo "Or check failed jobs with: php artisan queue:failed\n";
}

echo "Script completed.\n";
