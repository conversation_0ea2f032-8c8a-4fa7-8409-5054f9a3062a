<?php

use App\Jobs\Prospects\VerifyNewBuyerProspectJob;
use App\Models\Prospects\NewBuyerProspect;





$prospect = NewBuyerProspect::find(119939);

VerifyNewBuyerProspectJob::dispatch($prospect->id);

echo "Job dispatched successfully!\n";
echo "Prospect details:\n";
echo "- ID: {$prospect->id}\n";
echo "- Company: {$prospect->company_name}\n";
echo "- Phone: {$prospect->decision_maker_phone}\n";
echo "- Reference: {$prospect->reference}\n";
