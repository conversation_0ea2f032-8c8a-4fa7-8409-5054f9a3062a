<?php

namespace Tests\Feature\Dashboard;

use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Services\Dashboard\JWT\DashboardJWTService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CompanyResourceTest extends TestCase
{
    use RefreshDatabase;

    protected Company $company;

    protected User $am;
    protected User $om;
    protected User $bdm;

    protected function setUp(): void
    {
        parent::setUp();

        $this->company = Company::factory()->createQuietly();

        $user = CompanyUser::factory()->for($this->company)->create();

        $this->withHeader('X-CLIENT-BEARER', app(DashboardJWTService::class)->generate($user->id));

        Role::findOrCreate('account-manager');
        Role::findOrCreate('onboarding-manager');
        Role::findOrCreate('business-development-manager');

        $this->am = User::factory()->withRole('account-manager')->createQuietly();
        $this->om = User::factory()->withRole('onboarding-manager')->createQuietly();
        $this->bdm = User::factory()->withRole('business-development-manager')->createQuietly();

        $this->company->assign($this->am)->asAccountManager();
        $this->company->assign($this->om)->asOnboardingManager();
        $this->company->assign($this->bdm)->asBusinessDevelopmentManager();
    }

    #[Test]
    public function it_provides_a_business_contact_for_an_account_manager_as_first_choice()
    {
        $this->get(route('dashboard-api.v3.companies.show', ['companyId' => $this->company->id]))
            ->assertOk()
            ->assertJsonFragment([
                'business_contact' => [
                    'type' => str('account-manager')->headline(),
                    'name' => $this->am->name,
                    'email' => $this->am->email,
                    'phone' => $this->am->phone,
                ],
            ]);
    }

    #[Test]
    public function it_provides_a_business_contact_for_an_onboarding_manager_as_second_choice()
    {
        $this->company->unassignAccountManager();

        $this->get(route('dashboard-api.v3.companies.show', ['companyId' => $this->company->id]))
            ->assertOk()
            ->assertJsonFragment([
                'business_contact' => [
                    'type' => str('onboarding-manager')->headline(),
                    'name' => $this->om->name,
                    'email' => $this->om->email,
                    'phone' => $this->om->phone,
                ],
            ]);
    }

    #[Test]
    public function it_provides_a_business_contact_for_a_bdm_as_third_choice()
    {
        $this->company->unassignAccountManager();
        $this->company->unassignOnboardingManager();

        $this->get(route('dashboard-api.v3.companies.show', ['companyId' => $this->company->id]))
            ->assertOk()
            ->assertJsonFragment([
                'business_contact' => [
                    'type' => str('business-development-manager')->headline(),
                    'name' => $this->bdm->name,
                    'email' => $this->bdm->email,
                    'phone' => $this->bdm->phone,
                ],
            ]);
    }

    #[Test]
    public function it_provides_no_business_contact_if_none_exist()
    {
        $this->company->unassignAccountManager();
        $this->company->unassignOnboardingManager();
        $this->company->unassignBusinessDevelopmentManager();

        $this->get(route('dashboard-api.v3.companies.show', ['companyId' => $this->company->id]))
            ->assertOk()
            ->assertJsonFragment([
                'business_contact' => null,
            ]);
    }
}
