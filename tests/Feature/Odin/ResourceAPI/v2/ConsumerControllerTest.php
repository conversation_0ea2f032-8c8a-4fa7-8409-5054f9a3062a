<?php

declare(strict_types=1);

namespace Tests\Feature\Odin\ResourceAPI\v2;

use App\Enums\Odin\Product as OdinProduct;
use App\Jobs\SaveLegacyAffiliateAndTracking;
use App\Models\Odin\Consumer;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Models\WatchdogVideo;
use App\Services\Odin\API\OdinAuthoritativeAPILegacySyncService;
use App\Services\Odin\API\OdinAuthoritativeAPIService;
use App\Services\QAAutomation\QAAutomationService;
use Database\Seeders\ProductsSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Config;
use PHPUnit\Framework\Attributes\Test;
use Queue;
use SolarInvestments\Testing\SkipTestWhenRunningCI;
use Str;
use Tests\TestCase;

class ConsumerControllerTest extends TestCase
{
    use RefreshDatabase, SkipTestWhenRunningCI;

    function setUp(): void
    {
        parent::setUp();

        Config::set('services.solar_classifier.url', '');
        Config::set('services.solar_classifier.api_key', '');
    }

    #[Test]
    public function it_sends_watchdog_video_id_on_successful_consumer_creation()
    {
        Queue::fake();

        $mock = $this->mock(OdinAuthoritativeAPILegacySyncService::class);

        $mock->expects('post')->andReturn(
            $this->createStub(Response::class),
        );

        $mock->expects('handleSyncError');

        $this->mock(QAAutomationService::class)
            ->expects('qualifyConsumerProduct')
            ->andReturnFalse();

        $service = IndustryService::factory()->create([
            'name' => 'Test',
            'slug' => 'test',
        ]);

        $this->seed(ProductsSeeder::class);

        ServiceProduct::create([
            'industry_service_id' => $service->id,
            'product_id' => Product::where('name', OdinProduct::LEAD->value)->first()->id,
        ]);

        $email = fake()->email;

        $watchdog_video_id = Str::ulid()->jsonSerialize();

        $response = $this->withToken(token: base64_encode('test'))->post('odin-api/v2/consumer/create', [
            'watchdog_video_id' => $watchdog_video_id,
            'consumer' => [
                'email' => $email,
                'first_name' => fake()->firstName,
                'last_name' => fake()->lastName,
                'address_1' => fake()->address(),
                'city' => fake()->city,
                'state' => fake()->stateAbbr(),
                'zip_code' => substr(fake()->postcode, 0, 5),
            ],
            'service' => $service->slug,
        ])
            ->assertOk();

        $consumer = Consumer::where('email', $email)->first();

        $response
            ->assertJson([
                'data' => [
                    'status' => false,
                    'uuid' => $consumer->reference,
                ],
            ]);

        $this->assertDatabaseHas('watchdog_videos', [...compact('watchdog_video_id'), 'consumer_id' => $consumer->id]);

        Queue::assertPushed(SaveLegacyAffiliateAndTracking::class);
    }

    #[Test]
    public function it_does_not_send_watchdog_video_id_if_not_in_request()
    {
        $mock = $this->mock(OdinAuthoritativeAPILegacySyncService::class);

        $mock->expects('post')->andReturn(
            $this->createStub(Response::class),
        )->once();

        $mock->expects('handleSyncError')->times(2);

        $mock->expects('patch->json')->andReturn([
            'data' => [
                'status' => false,
                'uuid' => Str::ulid()->jsonSerialize(),
            ],
        ])->once();

        $service = IndustryService::factory()->create([
            'name' => 'Test',
            'slug' => 'test',
        ]);

        $this->seed(ProductsSeeder::class);

        ServiceProduct::create([
            'industry_service_id' => $service->id,
            'product_id' => Product::where('name', OdinProduct::LEAD->value)->first()->id,
        ]);

        $email = fake()->email;

        $this->withToken(base64_encode('test'))->post('odin-api/v2/consumer/create', [
            'consumer' => [
                'email' => $email,
                'first_name' => fake()->firstName,
                'last_name' => fake()->lastName,
                'address_1' => fake()->address(),
                'city' => fake()->city,
                'state' => fake()->stateAbbr(),
                'zip_code' => substr(fake()->postcode, 0, 5),
            ],
            'service' => $service->slug,
        ])
            ->assertOk()
            ->assertJson([
                'data' => [
                    'status' => false,
                    'uuid' => Consumer::where('email', $email)->first()->reference,
                ],
            ]);

        $this->assertDatabaseCount('watchdog_videos', 0);
    }

    #[Test]
    public function it_does_not_send_watchdog_video_id_on_failed_consumer_creation()
    {
        $this->mock(OdinAuthoritativeAPIService::class)->expects('create')->andReturnNull();

        $service = IndustryService::factory()->create([
            'name' => 'Test',
            'slug' => 'test',
        ]);

        $this->seed(ProductsSeeder::class);

        ServiceProduct::create([
            'industry_service_id' => $service->id,
            'product_id' => Product::where('name', OdinProduct::LEAD->value)->first()->id,
        ]);

        $email = fake()->email;

        $this->withToken(base64_encode('test'))->post('odin-api/v2/consumer/create', [
            'watchdog_video_id' => Str::ulid()->jsonSerialize(),
            'consumer' => [
                'email' => $email,
                'first_name' => fake()->firstName,
                'last_name' => fake()->lastName,
                'address_1' => fake()->address(),
                'city' => fake()->city,
                'state' => fake()->stateAbbr(),
                'zip_code' => substr(fake()->postcode, 0, 5),
            ],
            'service' => $service->slug,
        ])->assertServerError();

        $this->assertDatabaseCount('watchdog_videos', 0);
    }

    #[Test]
    public function it_stores_the_same_watchdog_video_id_as_long_as_the_consumer_and_watchdog_video_id_combo_is_unique()
    {
        Queue::fake();

        $mock = $this->mock(OdinAuthoritativeAPILegacySyncService::class);

        $mock->expects('post')->andReturn(
            $this->createStub(Response::class),
        );

        $mock->expects('handleSyncError');

        $service = IndustryService::factory()->create([
            'name' => 'Test',
            'slug' => 'test',
        ]);

        $this->seed(ProductsSeeder::class);

        ServiceProduct::create([
            'industry_service_id' => $service->id,
            'product_id' => Product::where('name', OdinProduct::LEAD->value)->first()->id,
        ]);

        $email = fake()->email;

        $watchdog_video_id = Str::ulid()->jsonSerialize();

        WatchdogVideo::factory()->create([
            'watchdog_video_id' => $watchdog_video_id,
            'consumer_id' => Consumer::factory()->create()->id,
        ]);

        $response = $this->withToken(token: base64_encode('test'))->post('odin-api/v2/consumer/create', [
            'watchdog_video_id' => $watchdog_video_id,
            'consumer' => [
                'email' => $email,
                'first_name' => fake()->firstName,
                'last_name' => fake()->lastName,
                'address_1' => fake()->address(),
                'city' => fake()->city,
                'state' => fake()->stateAbbr(),
                'zip_code' => substr(fake()->postcode, 0, 5),
            ],
            'service' => $service->slug,
        ])
            ->assertOk();

        $consumer = Consumer::where('email', $email)->first();

        $response
            ->assertJson([
                'data' => [
                    'status' => false,
                    'uuid' => $consumer->reference,
                ],
            ]);

        $this->assertDatabaseHas('watchdog_videos', [...compact('watchdog_video_id'), 'consumer_id' => $consumer->id]);

        $this->assertDatabaseCount('watchdog_videos', 2);

        Queue::assertPushed(SaveLegacyAffiliateAndTracking::class);
    }
}
