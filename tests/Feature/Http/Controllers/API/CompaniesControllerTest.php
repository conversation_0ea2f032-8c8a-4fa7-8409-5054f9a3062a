<?php

declare(strict_types=1);

namespace Tests\Feature\Http\Controllers\API;

use App\Enums\CompanyConsolidatedStatus;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Jobs\DispatchPubSubEvent;
use App\Models\Action;
use App\Models\ActionCategory;
use App\Models\CompanySlug;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\IndustryService;
use App\Models\User;
use App\Repositories\Legacy\CompanyRepository;
use App\Repositories\Legacy\CompanySuccessManagerRepository;
use App\Services\PubSub\PubSubService;
use App\Services\UserAuthorizationService;
use Database\Seeders\DirectLeadsProductSeeder;
use Database\Seeders\ProductsSeeder;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CompaniesControllerTest extends TestCase
{
    use RefreshDatabase;

    protected Company $company;

    protected EloquentCompany $legacyCompany;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        Schema::connection('readonly')->disableForeignKeyConstraints();

        EloquentCompany::truncate();

        LeadCampaign::truncate();

        Schema::connection('readonly')->enableForeignKeyConstraints();

        IndustryService::factory()->create();

        $this->company = Company::factory()->has(CompanyConfiguration::factory(), 'configuration')->createQuietly();

        $this->user = User::factory()->create();

        $role = Role::findOrCreate('admin');
        $role->givePermissionTo(Permission::findOrCreate('company/admin-status/edit'));
        $role->givePermissionTo(Permission::findOrCreate('company/basic-status/edit'));
        $this->user->assignRole($role);
    }

    #[Test]
    public function it_gets_basic_info()
    {
        $this->actingAs($this->user)->get(route('internal-api.v1.companies.get-profile-data', ['company_id' => $this->company->id]))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'company' => [
                        'id',
                        'legacy_id',
                        'name',
                        'entity_name',
                        'status',
                        'sales_status',
                        'industries',
                        'services',
                        'type',
                        'companyFields',
                        'lastChanged',
                        'lastQuote',
                        'lastReview',
                        'lastRevised',
                        'lastLogin',
                        'logoFileSquare',
                        'logoFileRectangle',
                        'mediaCount',
                        'attachmentCount',
                        'profilePageLink',
                        'website',
                        'dateRegistered',
                        'payingBy',
                        'paymentSource',
                        'dateOfNextCharge',
                        'activePaymentGateway',
                        'licenses',
                        'socialMediaLinks',
                        'prescreened',
                        'activeSlug',
                    ],
                    'adminUrl',
                    'display_profile',
                ],
            ]);
    }

    #[Test]
    public function it_gets_basic_info_sourced_from()
    {
        $this->actingAs($this->user)->get(route('internal-api.v1.companies.get-profile-data', ['company_id' => $this->company->id]))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'company' => [
                        'sourced_from',
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_updates_company_details()
    {
        Queue::fake(DispatchPubSubEvent::class);

        $this->partialMock(CompanyRepository::class)->shouldReceive('setCompanyServices')->once()->andReturn([
            'status' => true,
        ]);

        $this->company = Company::factory()->withLegacyCompany()->has(CompanySlug::factory([
            'slug' => 'old-company-slug',
            'redirect_company_slug_id' => null,
        ]))->createQuietly([
            'name' => 'Old Company Name',
            'entity_name' => 'Old Entity Name',
            'prescreened_at' => null,
            'admin_approved' => false,
        ]);

        $this->company->legacyCompany()->update([
            'type' => 'admin',
        ]);

        $this->actingAs($this->user)
            ->patch(
                route('internal-api.v1.companies.update-company-details', ['company_id' => $this->company->id]),
                [
                    'name' => 'New Company Name',
                    'entity_name' => 'New Entity Name',
                    'services' => [IndustryService::first()->id],
                    'type' => 'installer',
                    'prescreened' => true,
                    'slug' => 'new-company-slug',
                    'admin_approved' => true,
                ]
            )
            ->assertOk()
            ->assertJson([
                'data' => [
                    'status' => true,
                    'legacy_status' => true,
                ],
            ]);

        $this->assertDatabaseHas('companies', [
            'id' => $this->company->id,
            'name' => 'New Company Name',
            'entity_name' => 'New Entity Name',
        ]);

        $this->assertNotNull($this->company->refresh()->prescreened_at);

        $this->assertDatabaseCount('company_slugs', 2);

        $this->assertDatabaseHas('company_slugs', [
            'slug' => 'new-company-slug',
            'company_id' => $this->company->id,
        ]);

        $this->assertDatabaseHas('company_slugs', [
            'slug' => 'old-company-slug',
            'company_id' => $this->company->id,
            'redirect_company_slug_id' => CompanySlug::where('slug', 'new-company-slug')->first()->id,
        ]);

        Queue::assertPushed(DispatchPubSubEvent::class, function (DispatchPubSubEvent $job) {
            return $job->category === EventCategory::ADMIN2
                && $job->name === EventName::COMPANY_BASIC_DETAILS_UPDATED
                && $job->data['company_reference'] === $this->company->reference
                && $job->data['data']['type'] === 'installer';
        });
    }

    #[Test]
    public function it_gets_revenue_overview_for_v1()
    {
        $this->markTestSkipped('fails locally, revisit');

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.companies.get-revenue-overview', ['company_id' => $this->company->id]))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'v1' => [
                        'outstanding_invoice',
                        'last_lead',
                        'revenue_all_time',
                        'chargeable_last_30_days',
                        'revenue_last_30_days',
                        'revenue_last_6_months',
                        'graph_data',
                        'revenue_year_to_date',
                        'last_30_days_start_date',
                        'last_6_month_start_date',
                        'start_of_year_start_date',
                        'average_daily_spend_last_30_days',
                        'count_of_leads_last_30_days',
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_gets_revenue_overview_for_v1_with_correct_average_daily_spend_and_count_of_leads_within_last_30_days()
    {
        $this->markTestSkipped('fails locally, revisit');

        $cases = [
            // Filtered in
            [
                'delivered_at' => now()->subDays(10),
                'chargeable' => true,
                'delivered' => true,
                'cost' => 100,
            ],
            // Filtered in
            [
                'delivered_at' => now()->subDays(29),
                'chargeable' => true,
                'delivered' => true,
                'cost' => 100,
            ],
            // Filtered out because not delivered
            [
                'delivered_at' => now()->subDays(10),
                'chargeable' => true,
                'delivered' => false,
                'cost' => 100,
            ],
            // Filtered out because past 30 days
            [
                'delivered_at' => now()->subDays(30),
                'chargeable' => true,
                'delivered' => true,
                'cost' => 100,
            ],
        ];

        foreach ($cases as $case) {
            $this->company->productAssignments()->createQuietly(attributes: $case);
        }

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.companies.get-revenue-overview', ['company_id' => $this->company->id]))
            ->assertOk()
            ->assertJsonFragment([
                'average_daily_spend_last_30_days' => round(200 / 30, 2),
                'count_of_leads_last_30_days' => 2,
            ]);
    }

    #[Test]
    public function it_gets_revenue_overview_for_v2()
    {
        if (! Schema::connection('readonly')->hasColumn('tblcompany', 'billing_version')) {
            Schema::connection('readonly')->table('tblcompany', function (Blueprint $table) {
                $table->string('billing_version')->default('v1');
            });
        }

        if (! Schema::connection('readonly')->hasColumn('tblquotecompany', 'billing_version')) {
            Schema::connection('readonly')->table('tblquotecompany', function (Blueprint $table) {
                $table->string('billing_version')->default('v1');
            });
        }

        $this->company = Company::factory()->withLegacyCompany()->createQuietly();

        $this->company->legacyCompany->mergeFillable(['billing_version']);

        $this->company->legacyCompany()->update([
            'billing_version' => 'v2',
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.companies.get-revenue-overview', ['company_id' => $this->company->id]))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'last_30_days_start_date',
                    'last_6_months_start_date',
                    'start_of_year_start_date',
                    'a2_all_time_paid',
                    'legacy_all_time_paid',
                    'total_paid_all_time',
                    'count_paid_all_time',
                    'total_paid_last_30_days',
                    'count_paid_last_30_days',
                    'total_paid_last_6_months',
                    'count_paid_last_6_months',
                    'total_paid_year_to_date',
                    'count_paid_year_to_date',
                    'total_billable_all_time',
                    'count_billable_all_time',
                    'count_billable_last_30_days',
                    'total_billable_last_30_days',
                    'count_billable_last_6_months',
                    'total_billable_last_6_months',
                    'count_billable_year_to_date',
                    'total_billable_year_to_date',
                    'average_daily_spend_last_30_days',
                    'total_rejected_all_time',
                    'count_rejected_all_time',
                    'total_cancelled_all_time',
                    'count_cancelled_all_time',
                    'last_delivered_at',
                    'total_to_invoice',
                    'requested_at',
                ],
            ]);
    }

    #[Test]
    public function it_gets_company_actions()
    {
        $fromUser = User::factory()->create();

        $companyUser = CompanyUser::factory()->create([
            'company_id' => $this->company->id,
        ]);

        Action::factory()->create([
            'from_user_id' => $fromUser->id,
            'for_id' => $companyUser->id,
            'for_relation_type' => Action::RELATION_TYPE_COMPANY_USER,
            'action_category_id' => ActionCategory::create([
                'name' => 'Test Category',
            ])->id,
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.companies.get-company-actions', ['company_id' => $this->company->id]))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'actions' => [
                        'current_page',
                        'data' => [
                            '*' => [
                                'id',
                                'subject',
                                'message',
                                'from',
                                'from_user_id',
                                'to',
                                'to_company',
                                'target_type',
                                'target_id',
                                'created_timestamp',
                                'updated_timestamp',
                                'pinned',
                                'category',
                                'action_category_id',
                                'display_date',
                                'tags',
                                'tag_by_email',
                                'previous_sales_status',
                                'updated_sales_status',
                            ],
                        ],
                        'first_page_url',
                        'from',
                        'last_page',
                        'last_page_url',
                        'links' => [
                            '*' => [
                                'url',
                                'label',
                                'active',
                            ],
                        ],
                        'next_page_url',
                        'path',
                        'per_page',
                        'prev_page_url',
                        'to',
                        'total',
                    ],
                ],
            ]);
    }

    #[Test, DataProvider('text_search_dataset')]
    public function it_gets_company_actions_via_text_search($search, $prep, $count)
    {
        $prep($this);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.companies.get-company-actions', ['company_id' => $this->company->id, 'search' => $search]))
            ->assertOk()
            ->assertJsonCount($count, 'data.actions.data');
    }

    public static function text_search_dataset(): array
    {
        return [
            'searching on subject' => [
                'Test Subject',
                function (self $class) {
                    $fromUser = User::factory()->create();

                    $companyUser = CompanyUser::factory()->create([
                        'company_id' => $class->company->id,
                    ]);

                    Action::factory()->create([
                        'subject' => 'Test Subject',
                        'from_user_id' => $fromUser->id,
                        'for_id' => $companyUser->id,
                        'for_relation_type' => Action::RELATION_TYPE_COMPANY_USER,
                        'action_category_id' => ActionCategory::create([
                            'name' => 'Test Category',
                        ])->id,
                    ]);

                    Action::factory()->create([
                        'subject' => 'T3st Subject',
                        'from_user_id' => $fromUser->id,
                        'for_id' => $companyUser->id,
                        'for_relation_type' => Action::RELATION_TYPE_COMPANY_USER,
                        'action_category_id' => ActionCategory::create([
                            'name' => 'Test Category',
                        ])->id,
                    ]);
                },
                1,
            ],
            'searching on message' => [
                'Test Message',
                function (self $class) {
                    $fromUser = User::factory()->create();

                    $companyUser = CompanyUser::factory()->create([
                        'company_id' => $class->company->id,
                    ]);

                    Action::factory()->create([
                        'message' => 'Test Message',
                        'from_user_id' => $fromUser->id,
                        'for_id' => $companyUser->id,
                        'for_relation_type' => Action::RELATION_TYPE_COMPANY_USER,
                        'action_category_id' => ActionCategory::create([
                            'name' => 'Test Category',
                        ])->id,
                    ]);

                    Action::factory()->create([
                        'message' => 'T3st Message',
                        'from_user_id' => $fromUser->id,
                        'for_id' => $companyUser->id,
                        'for_relation_type' => Action::RELATION_TYPE_COMPANY_USER,
                        'action_category_id' => ActionCategory::create([
                            'name' => 'Test Category',
                        ])->id,
                    ]);
                },
                1,
            ],
            'searching on subject with company relation type' => [
                'Test Subject',
                function (self $class) {
                    $fromUser = User::factory()->create();

                    Action::factory()->create([
                        'subject' => 'Test Subject',
                        'from_user_id' => $fromUser->id,
                        'for_id' => $class->company->id,
                        'for_relation_type' => Action::RELATION_TYPE_COMPANY,
                        'action_category_id' => ActionCategory::create([
                            'name' => 'Test Category',
                        ])->id,
                    ]);

                    Action::factory()->create([
                        'subject' => 'T3st Subject',
                        'from_user_id' => $fromUser->id,
                        'for_id' => $class->company->id,
                        'for_relation_type' => Action::RELATION_TYPE_COMPANY,
                        'action_category_id' => ActionCategory::create([
                            'name' => 'Test Category',
                        ])->id,
                    ]);
                },
                1,
            ],
        ];
    }

    #[Test]
    public function it_gets_campaigns_for_company()
    {
        $this->seed(ProductsSeeder::class);

        $this->company = Company::factory()->withLegacyCompany()->createQuietly();

        LeadCampaign::factory()->create([
            'company_id' => $this->company->legacy_id,
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.companies.get-campaigns-for-company', ['company_id' => $this->company->id]))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'campaigns' => [
                        'current_page',
                        'data' => [
                            '*' => [
                                'id',
                                'uuid',
                                'name',
                                'lead_status',
                                'appointment_status',
                                'zipcode_count',
                                'state_count',
                                'utility_count',
                                'lead_budget',
                                'appointment_budgets',
                                'sales_types',
                                'lead_categories',
                                'appointments_active',
                                'industry',
                                'service',
                                'status',
                                'reactivate_at',
                            ],
                        ],
                        'first_page_url',
                        'from',
                        'last_page',
                        'last_page_url',
                        'links' => [
                            '*' => [
                                'url',
                                'label',
                                'active',
                            ],
                        ],
                        'next_page_url',
                        'path',
                        'per_page',
                        'prev_page_url',
                        'to',
                        'total',
                    ],

                ],
            ]);
    }

    #[Test]
    public function it_gets_company_actions_via_category_multiselect()
    {
        $fromUser = User::factory()->create();

        $companyUser = CompanyUser::factory()->create([
            'company_id' => $this->company->id,
        ]);

        $categoryOneId = ActionCategory::create(['name' => 'Test Category 1'])->id;
        $categoryTwoId = ActionCategory::create(['name' => 'Test Category 2'])->id;

        $actionOne = Action::factory()->create([
            'from_user_id' => $fromUser->id,
            'for_id' => $companyUser->id,
            'for_relation_type' => Action::RELATION_TYPE_COMPANY_USER,
            'action_category_id' => $categoryOneId,
        ]);

        $actionTwo = Action::factory()->create([
            'from_user_id' => $fromUser->id,
            'for_id' => $companyUser->id,
            'for_relation_type' => Action::RELATION_TYPE_COMPANY_USER,
            'action_category_id' => $categoryTwoId,
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.companies.get-company-actions', [
                'company_id' => $this->company->id,
                'categories' => [
                    $categoryOneId,
                ],
            ]))
            ->assertOk()
            ->assertJsonCount(1, 'data.actions.data')
            ->assertJsonFragment([
                'id' => $actionOne->id,
            ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.companies.get-company-actions', [
                'company_id' => $this->company->id,
                'categories' => [
                    $categoryOneId,
                    $categoryTwoId,
                ],
            ]))
            ->assertOk()
            ->assertJsonCount(2, 'data.actions.data')
            ->assertJsonFragment([
                'id' => $actionOne->id,
            ])
            ->assertJsonFragment([
                'id' => $actionTwo->id,
            ]);
    }

    #[Test]
    public function it_updates_basic_info(): void
    {
        Config::set('services.payment_gateways.stripe.gateway_mode', false);
        Config::set('services.payment_gateways.stripe.api_key_secret_test', 'test');

        $this->seed(ProductsSeeder::class);

        $this->seed(DirectLeadsProductSeeder::class);

        Permission::firstOrCreate(['name' => 'can-reallocate-all']);

        Permission::firstOrCreate(['name' => 'can-reallocate-team']);

        $this->company = Company::factory()->withLegacyCompany()->has(CompanySlug::factory([
            'slug' => 'old-company-slug',
            'redirect_company_slug_id' => null,
        ]))->has(CompanyConfiguration::factory(), 'configuration')->createQuietly([
            'name' => 'Old Company Name',
            'entity_name' => 'Old Entity Name',
            'prescreened_at' => null,
            'admin_approved' => false,
        ]);

        $this->company->legacyCompany()->update([
            'type' => 'admin',
        ]);

        $this->mock(PubSubService::class)->shouldReceive('handle')->once();

        $this->actingAs($this->user)
            ->patch(
                route('internal-api.v1.companies.update-basic-info', ['company_id' => $this->company->id]),
                [
                    'name' => 'New Company Name',
                    'entity_name' => 'New Entity Name',
                    'services' => [IndustryService::first()->id],
                    'type' => 'installer',
                    'prescreened' => true,
                    'slug' => 'new-company-slug',
                    'admin_approved' => true,
                    'companyname' => 'New Company Name',
                    'website' => 'new-company-name.test',
                    'address' => [
                        'name' => 'New Address',
                        'location_id' => null,
                        'address_id' => null,
                        'address_1' => '123 New Address',
                        'address_2' => 'Suite 123',
                        'city' => 'New City',
                        'state' => 'CA',
                        'zip_code' => '12345',
                    ],
                ]
            )
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'company',
                ],
            ]);

        $this->company->refresh();

        $this->assertDatabaseHas('companies', [
            'id' => $this->company->id,
            'legacy_id' => $this->company->legacy_id,
            'reference' => $this->company->reference,
            'name' => 'New Company Name',
            'entity_name' => 'Old Entity Name',
            'website' => 'new-company-name.test',
            'consolidated_status' => $this->company->consolidated_status,
            'sales_status' => $this->company->sales_status,
            'campaigns_are_partially_suspended' => $this->company->campaigns_are_partially_suspended,
            'status' => $this->company->status,
            'admin_status' => $this->company->admin_status,
            'admin_locked' => $this->company->admin_locked,
            'admin_approved' => $this->company->admin_approved,
            'archived' => $this->company->archived,
            'imported' => $this->company->imported,
            'link_to_logo' => $this->company->link_to_logo,
            'uuid' => '',
            'watchdog_id' => null,
            'deleted_at' => null,
            'never_exceed_budget' => $this->company->never_exceed_budget,
            'relationship_manager_id' => null,
            'active_opt_in_name_id' => null,
        ]);

        $this->assertDatabaseHas('addresses', [
            'legacy_id' => null,
            'address_1' => '123 New Address',
            'address_2' => 'Suite 123',
            'city' => 'New City',
            'county' => null,
            'state' => 'CA',
            'zip_code' => '12345',
            'country' => '',
            'latitude' => 0.0,
            'longitude' => 0.0,
            'place_id' => '',
            'utc' => -8,
            'imported' => 0,
            'zip_code_location_id' => null,
            'county_location_id' => null,
            'state_location_id' => null,
        ]);

        $this->assertDatabaseHas('company_locations', [
            'company_id' => $this->company->id,
            'address_id' => $this->company->locations()->first()->address_id,
            'name' => 'New Address',
            'phone' => null,
            'imported' => false,
        ]);

        $this->assertDatabaseHas('company_data', [
            'company_id' => $this->company->id,
        ]);

        $this->assertDatabaseHas('company_configurations', [
            'company_id' => $this->company->id,
            'allow_leads_no_cc' => false,
            'enable_tcpa_playback' => false,
            'never_exceed_budget' => false,
            'disallow_ranking' => false,
            'receive_off_hour_leads' => false,
            'appointments_active' => false,
            'require_appointments_calendar' => false,
            'missed_products_active' => false,
            'mi_appointments_active' => false,
            'reviews_enabled' => false,
            'unrestricted_zip_code_targeting' => false,
        ]);
    }

    #[Test]
    public function it_updates_basic_info_sourced_from()
    {
        Config::set('services.payment_gateways.stripe.gateway_mode', false);
        Config::set('services.payment_gateways.stripe.api_key_secret_test', 'test');

        $this->seed(ProductsSeeder::class);

        $this->seed(DirectLeadsProductSeeder::class);

        Permission::firstOrCreate(['name' => 'can-reallocate-all']);

        Permission::firstOrCreate(['name' => 'can-reallocate-team']);

        $this->company = Company::factory()->withLegacyCompany()->has(CompanySlug::factory([
            'slug' => 'old-company-slug',
            'redirect_company_slug_id' => null,
        ]))->has(CompanyConfiguration::factory(), 'configuration')->createQuietly([
            'name' => 'Old Company Name',
            'entity_name' => 'Old Entity Name',
            'prescreened_at' => null,
            'admin_approved' => false,
        ]);

        $this->company->legacyCompany()->update([
            'type' => 'admin',
        ]);

        $this->mock(PubSubService::class)->shouldReceive('handle')->once();

        $this->actingAs($this->user)
            ->patch(
                route('internal-api.v1.companies.update-basic-info', ['company_id' => $this->company->id]),
                [
                    'name' => 'New Company Name',
                    'entity_name' => 'New Entity Name',
                    'services' => [IndustryService::first()->id],
                    'type' => 'installer',
                    'prescreened' => true,
                    'slug' => 'new-company-slug',
                    'admin_approved' => true,
                    'companyname' => 'New Company Name',
                    'website' => 'new-company-name.test',
                    'address' => [
                        'name' => 'New Address',
                        'location_id' => null,
                        'address_id' => null,
                        'address_1' => '123 New Address',
                        'address_2' => 'Suite 123',
                        'city' => 'New City',
                        'state' => 'CA',
                        'zip_code' => '12345',
                    ],
                    'sourced_from' => 'referral',
                ]
            )
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'company' => [
                        'sourced_from',
                    ],
                ],
            ]);

        $this->assertDatabaseHas('companies', [
            'sourced_from' => 'referral',
        ]);
    }

    #[Test, DataProvider('updatesBasicInfoAdminApprovedDataProvider')]
    public function it_updates_basic_info_admin_approved_and_admin_locked(string $role): void
    {
        $this->markTestSkipped('Admin locked and approved field will be removed with company status refactor');

        $this->seed(ProductsSeeder::class);

        $this->seed(DirectLeadsProductSeeder::class);

        $role = Role::findOrCreate($role);

        $role->givePermissionTo(Permission::findOrCreate('companies'));

        $role->givePermissionTo(Permission::findOrCreate('can-reallocate-all'));

        Config::set('services.payment_gateways.stripe.gateway_mode', false);

        Config::set('services.payment_gateways.stripe.api_key_secret_test', 'test');

        Queue::fake(DispatchPubSubEvent::class);

        $this->company = Company::factory()->withLegacyCompany()->createQuietly([
            'admin_approved' => false,
            'admin_locked' => false,
        ]);

        $this->company->configuration()->createQuietly(CompanyConfiguration::factory()->raw());

        $this->user = User::factory()->create();

        $this->user->assignRole($role);

        $this->actingAs($this->user->assignRole('admin'))
            ->patch(
                route('internal-api.v1.companies.update-basic-info', ['company_id' => $this->company->id]),
                [
                    'companyname' => $this->company->name,
                    'name' => $this->company->name,
                    'website' => $this->company->website,
                    'prescreened' => true,
                    'admin_status' => Company::ADMIN_STATUS_ADMIN_LOCKED,
                    'status' => Company::STATUS_LEADS_ACTIVE,
                    'address' => [
                        'location_id' => null,
                        'address_id' => null,
                        'name' => null,
                        'address_1' => '123 New Address',
                        'address_2' => null,
                        'city' => 'New City',
                        'state' => 'CA',
                        'zip_code' => '12345',
                    ],
                    'admin_approved' => true,
                    'admin_locked' => true,
                ]
            )
            ->assertOk();

        $this->assertDatabaseHas('companies', [
            'id' => $this->company->id,
            'admin_approved' => true,
            'admin_locked' => true,
            'consolidated_status' => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS,
        ]);
    }

    public static function updatesBasicInfoAdminApprovedDataProvider(): array
    {
        return [
            'sales manager' => ['sales-manager'],
            'relationship manager' => ['relationship-manager'],
        ];
    }

    #[Test]
    public function it_gets_company_profile(): void
    {
        $this->seed(ProductsSeeder::class);
        $this->seed(DirectLeadsProductSeeder::class);

        $this->user->givePermissionTo(Permission::findOrCreate('can-reallocate-all'))
            ->assignRole(Role::findOrCreate('account-manager'));

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.companies.get-company-profile', $this->company->id))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'company',
                    'account_managers' => [
                        '*' => [
                            'id',
                            'type',
                            'name',
                            'user_id',
                            'include_in_sales_round_robin',
                        ],
                    ],
                    'success_managers',
                ],
            ])
            ->assertJsonFragment([
                'id' => $this->user->id,
                'type' => null,
                'name' => $this->user->name,
                'user_id' => $this->user->id,
                'include_in_sales_round_robin' => null,
            ])
            ->assertJsonCount(1);
    }
}
