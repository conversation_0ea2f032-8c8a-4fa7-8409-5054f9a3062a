<?php

namespace Tests\Feature\Http\Controllers\API;

use App\Enums\Odin\Industry;
use App\Enums\TaskCategory as TaskCategoryEnum;
use App\Models\Legacy\Location;
use App\Models\Odin\NonPurchasingCompanyLocation;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyLocation;
use App\Models\Sales\Task;
use App\Models\TaskCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class NonPurchasingServiceAreaControllerTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();

        Location::query()->delete();
    }

    #[Test]
    public function get_companies_in_county_by_zip_code_with_no_companies_and_industry(): void
    {
        $industry = \App\Models\Odin\Industry::factory()->create([
            'slug' => 'solar',
        ]);

        $this->actingAs(User::factory()->create())
            ->get(route('internal-api.v1.non-purchasing-service-areas.get-companies-in-county-by-zip-code', [
                'zip_code' => '12345',
                'industry' => $industry->slug,
            ]))
            ->assertBadRequest()
            ->assertJson([
                'data' => [
                    'companies' => [],
                ],
            ]);
    }

    #[Test]
    public function get_companies_in_county_by_zip_code_with_no_companies_and_no_industry(): void
    {
        $location = Location::factory()->create([
            'type' => Location::TYPE_ZIP_CODE,
            'zip_code' => '12345',
            'county_key' => 'MO',
            'state_key' => 'CA',
        ]);

        Location::factory()->create([
            'type' => Location::TYPE_COUNTY,
            'zip_code' => $location->zip_code,
            'county_key' => $location->county_key,
            'state_key' => $location->state_key,
        ]);

        $this->assertDatabaseCount(Location::TABLE, 2);

        $this->actingAs(User::factory()->create())
            ->get(route('internal-api.v1.non-purchasing-service-areas.get-companies-in-county-by-zip-code', [
                'zip_code' => '12345',
                'industry' => 'non-existent-industry',
                'disable_filtering' => true,
            ]))
            ->assertBadRequest()
            ->assertJson([
                'data' => [
                    'companies' => [],
                ],
            ]);
    }

    #[Test]
    public function get_companies_in_county_by_zip_code_with_companies(): void
    {
        Role::findOrCreate('account-manager');

        $company = Company::factory()->createQuietly();

        CompanyIndustry::factory()->create([
            'company_id' => $company->id,
            'industry_id' => \App\Models\Odin\Industry::query()->updateOrCreate([
                'slug' => Industry::SOLAR->value,
            ], [
                'name' => Industry::SOLAR->value,
            ])->id,
        ]);

        $location = Location::factory()->create([
            'type' => Location::TYPE_ZIP_CODE,
            'zip_code' => '12345',
            'county_key' => 'MO',
            'state_key' => 'CA',
        ]);

        $countyLocation = Location::factory()->create([
            'type' => Location::TYPE_COUNTY,
            'zip_code' => $location->zip_code,
            'county_key' => $location->county_key,
            'state_key' => $location->state_key,
        ]);

        NonPurchasingCompanyLocation::create([
            'company_id' => $company->id,
            'location_id' => $countyLocation->id,
        ]);

        CompanyLocation::factory()->create([
            'company_id' => $company->id,
            'address_id' => Address::factory()->createQuietly([
                'legacy_id' => $location->id,
                'zip_code' => '12345',
            ])->id,
        ]);

        $this->actingAs(User::factory()->create())
            ->get(route('internal-api.v1.non-purchasing-service-areas.get-companies-in-county-by-zip-code', [
                'zip_code' => '12345',
                'industry' => Industry::SOLAR->value,
                'disable_filtering' => true,
            ]))
            ->assertOk()
            ->assertJsonCount(1, 'data.companies')
            ->assertJson([
                'data' => [
                    'companies' => [
                        0 => [
                            'id' => $company->id,
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function get_companies_in_county_by_zip_code_with_companies_and_filter_by_account_manager(): void
    {
        $company = Company::factory()->createQuietly();

        CompanyIndustry::factory()->create([
            'company_id' => $company->id,
            'industry_id' => \App\Models\Odin\Industry::updateOrCreate([
                'slug' => Industry::SOLAR->value,
            ], [
                'name' => Industry::SOLAR->value,
            ])->id,
        ]);

        $location = Location::factory()->create([
            'type' => Location::TYPE_ZIP_CODE,
            'zip_code' => '12345',
            'county_key' => 'MO',
            'state_key' => 'CA',
        ]);

        $countyLocation = Location::factory()->create([
            'type' => Location::TYPE_COUNTY,
            'zip_code' => $location->zip_code,
            'county_key' => $location->county_key,
            'state_key' => $location->state_key,
        ]);

        NonPurchasingCompanyLocation::create([
            'company_id' => $company->id,
            'location_id' => $countyLocation->id,
        ]);

        CompanyLocation::factory()->create([
            'company_id' => $company->id,
            'address_id' => Address::factory()->createQuietly([
                'zip_code' => '12345',
            ])->id,
        ]);

        $user = User::factory()->withRole('account-manager')->create();

        $company->assign($user)->as('account-manager');

        $task = Task::factory()->create([
            'task_category_id' => TaskCategory::factory()->create([
                'name' => TaskCategoryEnum::CATEGORY_SYSTEM->value,
            ])->id,
        ]);

        $this->actingAs($user)
            ->get(route('internal-api.v1.non-purchasing-service-areas.get-companies-in-county-by-zip-code', [
                'zip_code' => '12345',
                'industry' => Industry::SOLAR->value,
                'task_id' => $task->id,
                'disable_filtering' => true,
            ]))
            ->assertOk()
            ->assertJsonCount(1, 'data.companies')
            ->assertJson([
                'data' => [
                    'companies' => [
                        0 => [
                            'id' => $company->id,
                        ],
                    ],
                ],
            ]);
    }
}
