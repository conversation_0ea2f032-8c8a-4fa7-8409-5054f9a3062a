<?php

namespace Tests\Feature\Http\Controllers\API\Dashboard;

use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Company\CompanySystemStatus;
use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class InactiveCampaignsControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setup(): void
    {
        parent::setUp();

        Carbon::setTestNow(now());
    }

    #[Test]
    public function it_returns_a_list_of_inactive_campaigns()
    {
        $user = User::factory()->withRole('account-manager')->create();

        $company = Company::factory()->createQuietly([
            'id' => 1,
            'system_status' => CompanySystemStatus::ELIGIBLE,
        ]);

        $company->assign($user)->asAccountManager();

        $temporarilyInactiveCampaign = CompanyCampaign::factory()->for($company)->createQuietly([
            'id' => 1,
            'status' => CampaignStatus::PAUSED_TEMPORARILY,
        ]);

        $permanentlyInactiveCampaign = CompanyCampaign::factory()->for($company)->createQuietly([
            'id' => 2,
            'status' => CampaignStatus::PAUSED_PERMANENTLY,
        ]);

        $temporaryInactivation = CampaignReactivation::factory()->for($temporarilyInactiveCampaign, 'campaign')->create([
            'id' => 1,
            'reason' => 'Leads suck for a bit!',
            'reactivate_at' => now()->addDay(),
        ]);

        $permanentInactivation = CampaignReactivation::factory()->for($permanentlyInactiveCampaign, 'campaign')->create([
            'id' => 2,
            'reason' => 'Leads suck forever!',
        ]);

        $this->actingAs($user)
            ->get(route('internal-api.v2.campaigns.inactive'))
            ->assertExactJson([
                'data' => [
                    [
                        'id' => $permanentInactivation->id,
                        'type' => CampaignStatus::PAUSED_PERMANENTLY->getDisplayName(),
                        'since' => now()->toDateTimeString(),
                        'reactivates' => null,
                        'reason' => $permanentInactivation->reason,
                        'campaign' => [
                            'id' => $permanentlyInactiveCampaign->id,
                            'name' => $permanentlyInactiveCampaign->name,
                            'spend' => 0,
                        ],
                        'company' => [
                            'id' => $company->id,
                            'name' => $company->name,
                        ],
                    ],
                    [
                        'id' => $temporaryInactivation->id,
                        'type' => CampaignStatus::PAUSED_TEMPORARILY->getDisplayName(),
                        'since' => now()->toDateTimeString(),
                        'reactivates' => now()->addDay()->toDateTimeString(),
                        'reason' => $temporaryInactivation->reason,
                        'campaign' => [
                            'id' => $temporarilyInactiveCampaign->id,
                            'name' => $temporarilyInactiveCampaign->name,
                            'spend' => 0,
                        ],
                        'company' => [
                            'id' => $company->id,
                            'name' => $company->name,
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_accounts_for_spend_as_part_of_the_response()
    {
        $user = User::factory()->withRole('account-manager')->create();

        $company = Company::factory()->createQuietly([
            'system_status' => CompanySystemStatus::ELIGIBLE,
        ]);

        $company->assign($user)->asAccountManager();

        $campaign = CompanyCampaign::factory()->for($company)->createQuietly([
            'status' => CampaignStatus::PAUSED_TEMPORARILY,
        ]);

        $inactivation = CampaignReactivation::factory()->for($campaign, 'campaign')->create([
            'reason' => 'Leads suck for a bit!',
            'reactivate_at' => now()->addDay(),
        ]);

        $container = BudgetContainer::factory()->for($campaign, 'companyCampaign')->create();

        $budget = Budget::factory()->for($container)->createQuietly();

        $lead = ProductAssignment::factory()->chargeableAndDelivered()->for($budget)->for($company)->createQuietly([
            'cost' => 300,
        ]);

        $this->actingAs($user)
            ->get(route('internal-api.v2.campaigns.inactive'))
            ->assertExactJson([
                'data' => [
                    [
                        'id' => $inactivation->id,
                        'type' => CampaignStatus::PAUSED_TEMPORARILY->getDisplayName(),
                        'since' => now()->toDateTimeString(),
                        'reactivates' => now()->addDay()->toDateTimeString(),
                        'reason' => $inactivation->reason,
                        'campaign' => [
                            'id' => $campaign->id,
                            'name' => $campaign->name,
                            'spend' => $lead->cost,
                        ],
                        'company' => [
                            'id' => $company->id,
                            'name' => $company->name,
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_returns_the_inactivation_date()
    {
        $user = User::factory()->withRole('account-manager')->create();

        $company = Company::factory()->createQuietly([
            'id' => 1,
            'system_status' => CompanySystemStatus::ELIGIBLE,
        ]);

        $company->assign($user)->asAccountManager();

        $temporarilyInactiveCampaign = CompanyCampaign::factory()->for($company)->createQuietly([
            'id' => 1,
            'status' => CampaignStatus::PAUSED_TEMPORARILY,
        ]);

        $temporaryInactivation = CampaignReactivation::factory()->for($temporarilyInactiveCampaign, 'campaign')->create([
            'id' => 1,
            'reason' => 'Leads suck for a bit!',
            'reactivate_at' => now()->addDay(),
        ]);

        $this->actingAs($user)
            ->get(route('internal-api.v2.campaigns.inactive'))
            ->assertExactJson([
                'data' => [
                    [
                        'id' => $temporaryInactivation->id,
                        'type' => CampaignStatus::PAUSED_TEMPORARILY->getDisplayName(),
                        'since' => $temporaryInactivation->created_at->toDateTimeString(),
                        'reactivates' => now()->addDay()->toDateTimeString(),
                        'reason' => $temporaryInactivation->reason,
                        'campaign' => [
                            'id' => $temporarilyInactiveCampaign->id,
                            'name' => $temporarilyInactiveCampaign->name,
                            'spend' => 0,
                        ],
                        'company' => [
                            'id' => $company->id,
                            'name' => $company->name,
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_sorts_by_spend_above_all_else()
    {
        $user = User::factory()->withRole('account-manager')->create();

        $company = Company::factory()->createQuietly([
            'system_status' => CompanySystemStatus::ELIGIBLE,
        ]);

        $company->assign($user)->asAccountManager();

        $campaign = CompanyCampaign::factory()->for($company)->createQuietly([
            'status' => CampaignStatus::PAUSED_PERMANENTLY,
        ]);

        $inactivation = CampaignReactivation::factory()->for($campaign, 'campaign')->create();

        $container = BudgetContainer::factory()->for($campaign, 'companyCampaign')->create();

        $budget = Budget::factory()->for($container)->createQuietly();

        $lead = ProductAssignment::factory()->chargeableAndDelivered()->for($budget)->for($company)->createQuietly([
            'cost' => 500,
        ]);

        $campaignTwo = CompanyCampaign::factory()->for($company)->createQuietly([
            'status' => CampaignStatus::PAUSED_PERMANENTLY,
        ]);

        $inactivationTwo = CampaignReactivation::factory()->for($campaignTwo, 'campaign')->create();

        $containerTwo = BudgetContainer::factory()->for($campaignTwo, 'companyCampaign')->create();

        $budgetTwo = Budget::factory()->for($containerTwo)->createQuietly();

        $leadTwo = ProductAssignment::factory()->chargeableAndDelivered()->for($budgetTwo)->for($company)->createQuietly([
            'cost' => 300,
        ]);

        $this->actingAs($user)
            ->get(route('internal-api.v2.campaigns.inactive'))
            ->assertExactJson([
                'data' => [
                    [
                        'id' => $inactivation->id,
                        'type' => CampaignStatus::PAUSED_PERMANENTLY->getDisplayName(),
                        'since' => $inactivation->created_at->toDateTimeString(),
                        'reactivates' => null,
                        'reason' => '',
                        'campaign' => [
                            'id' => $campaign->id,
                            'name' => $campaign->name,
                            'spend' => 500,
                        ],
                        'company' => [
                            'id' => $company->id,
                            'name' => $company->name,
                        ],
                    ],
                    [
                        'id' => $inactivationTwo->id,
                        'type' => CampaignStatus::PAUSED_PERMANENTLY->getDisplayName(),
                        'since' => $inactivationTwo->created_at->toDateTimeString(),
                        'reactivates' => null,
                        'reason' => '',
                        'campaign' => [
                            'id' => $campaignTwo->id,
                            'name' => $campaignTwo->name,
                            'spend' => 300,
                        ],
                        'company' => [
                            'id' => $company->id,
                            'name' => $company->name,
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_sorts_by_type_after_spend()
    {
        $user = User::factory()->withRole('account-manager')->create();

        $company = Company::factory()->createQuietly([
            'system_status' => CompanySystemStatus::ELIGIBLE,
        ]);

        $company->assign($user)->asAccountManager();

        $campaign = CompanyCampaign::factory()->for($company)->createQuietly([
            'status' => CampaignStatus::PAUSED_TEMPORARILY,
        ]);

        $inactivation = CampaignReactivation::factory()->for($campaign, 'campaign')->create();

        $container = BudgetContainer::factory()->for($campaign, 'companyCampaign')->create();

        $budget = Budget::factory()->for($container)->createQuietly();

        $lead = ProductAssignment::factory()->chargeableAndDelivered()->for($budget)->for($company)->createQuietly([
            'cost' => 500,
        ]);

        $campaignTwo = CompanyCampaign::factory()->for($company)->createQuietly([
            'status' => CampaignStatus::PAUSED_PERMANENTLY,
        ]);

        $inactivationTwo = CampaignReactivation::factory()->for($campaignTwo, 'campaign')->create();

        $containerTwo = BudgetContainer::factory()->for($campaignTwo, 'companyCampaign')->create();

        $budgetTwo = Budget::factory()->for($containerTwo)->createQuietly();

        $leadTwo = ProductAssignment::factory()->chargeableAndDelivered()->for($budgetTwo)->for($company)->createQuietly([
            'cost' => 500,
        ]);

        $this->actingAs($user)
            ->get(route('internal-api.v2.campaigns.inactive'))
            ->assertExactJson([
                'data' => [
                    [
                        'id' => $inactivationTwo->id,
                        'type' => CampaignStatus::PAUSED_PERMANENTLY->getDisplayName(),
                        'since' => $inactivationTwo->created_at->toDateTimeString(),
                        'reactivates' => null,
                        'reason' => '',
                        'campaign' => [
                            'id' => $campaignTwo->id,
                            'name' => $campaignTwo->name,
                            'spend' => 500,
                        ],
                        'company' => [
                            'id' => $company->id,
                            'name' => $company->name,
                        ],
                    ],
                    [
                        'id' => $inactivation->id,
                        'type' => CampaignStatus::PAUSED_TEMPORARILY->getDisplayName(),
                        'since' => $inactivation->created_at->toDateTimeString(),
                        'reactivates' => null,
                        'reason' => '',
                        'campaign' => [
                            'id' => $campaign->id,
                            'name' => $campaign->name,
                            'spend' => 500,
                        ],
                        'company' => [
                            'id' => $company->id,
                            'name' => $company->name,
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_sorts_by_inactivation_date_after_type()
    {
        $user = User::factory()->withRole('account-manager')->create();

        $company = Company::factory()->createQuietly([
            'system_status' => CompanySystemStatus::ELIGIBLE,
        ]);

        $company->assign($user)->asAccountManager();

        $campaign = CompanyCampaign::factory()->for($company)->createQuietly([
            'status' => CampaignStatus::PAUSED_PERMANENTLY,
        ]);

        $inactivation = CampaignReactivation::factory()->for($campaign, 'campaign')->create([
            'created_at' => now()->subMonth(),
        ]);

        $container = BudgetContainer::factory()->for($campaign, 'companyCampaign')->create();

        $budget = Budget::factory()->for($container)->createQuietly();

        $lead = ProductAssignment::factory()->chargeableAndDelivered()->for($budget)->for($company)->createQuietly([
            'cost' => 500,
        ]);

        $campaignTwo = CompanyCampaign::factory()->for($company)->createQuietly([
            'status' => CampaignStatus::PAUSED_PERMANENTLY,
        ]);

        $inactivationTwo = CampaignReactivation::factory()->for($campaignTwo, 'campaign')->create();

        $containerTwo = BudgetContainer::factory()->for($campaignTwo, 'companyCampaign')->create();

        $budgetTwo = Budget::factory()->for($containerTwo)->createQuietly();

        $leadTwo = ProductAssignment::factory()->chargeableAndDelivered()->for($budgetTwo)->for($company)->createQuietly([
            'cost' => 500,
        ]);

        $this->actingAs($user)
            ->get(route('internal-api.v2.campaigns.inactive'))
            ->assertExactJson([
                'data' => [
                    [
                        'id' => $inactivation->id,
                        'type' => CampaignStatus::PAUSED_PERMANENTLY->getDisplayName(),
                        'since' => $inactivation->created_at->toDateTimeString(),
                        'reactivates' => null,
                        'reason' => '',
                        'campaign' => [
                            'id' => $campaign->id,
                            'name' => $campaign->name,
                            'spend' => 500,
                        ],
                        'company' => [
                            'id' => $company->id,
                            'name' => $company->name,
                        ],
                    ],
                    [
                        'id' => $inactivationTwo->id,
                        'type' => CampaignStatus::PAUSED_PERMANENTLY->getDisplayName(),
                        'since' => $inactivationTwo->created_at->toDateTimeString(),
                        'reactivates' => null,
                        'reason' => '',
                        'campaign' => [
                            'id' => $campaignTwo->id,
                            'name' => $campaignTwo->name,
                            'spend' => 500,
                        ],
                        'company' => [
                            'id' => $company->id,
                            'name' => $company->name,
                        ],
                    ],
                ],
            ]);
    }
}
