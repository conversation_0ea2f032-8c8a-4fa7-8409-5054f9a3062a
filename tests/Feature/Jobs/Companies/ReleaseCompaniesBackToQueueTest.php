<?php

namespace Tests\Feature\Jobs\companys;

use App\Enums\ActivityType;
use App\Jobs\Companies\ReleaseCompaniesBackToQueue;
use App\Models\ActivityFeed;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Models\Sales\Task;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Queue;
use Tests\TestCase;

class ReleaseCompaniesBackToQueueTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_dispatches_onto_the_queue()
    {
        Queue::fake();

        ReleaseCompaniesBackToQueue::dispatch();

        Queue::assertPushed(ReleaseCompaniesBackToQueue::class);
    }

    #[Test]
    public function a_company_is_released_back_to_the_queue_if_no_tasks_or_contact_in_5_days()
    {
        $this->travel(-5)->days();

        $sdr = User::factory()->withRole('sales-development-manager')->create();

        $bdm = User::factory()->withRole('business-development-manager')->create();

        $company = Company::factory()->createQuietly();

        $company->assign($sdr)->asSalesDevelopmentRepresentative();
        $company->assign($bdm)->asBusinessDevelopmentManager();

        $this->assertNotNull($company->salesDevelopmentRepresentative);
        $this->assertNotNull($company->businessDevelopmentManager);

        $this->travelBack();

        ReleaseCompaniesBackToQueue::dispatch();

        $company->refresh();

        $this->assertNull($company->salesDevelopmentRepresentative);
        $this->assertNull($company->businessDevelopmentManager);
    }

    #[Test]
    public function a_company_is_released_even_if_they_only_have_an_sdr_assigned()
    {
        $this->travel(-5)->days();

        $sdr = User::factory()->withRole('sales-development-manager')->create();

        $company = Company::factory()->createQuietly();

        $company->assign($sdr)->asSalesDevelopmentRepresentative();

        $this->travelBack();

        ReleaseCompaniesBackToQueue::dispatch();

        $company->refresh();

        $this->assertNull($company->salesDevelopmentRepresentative);
        $this->assertNull($company->businessDevelopmentManager);
    }

    #[Test]
    public function a_company_is_ignored_if_it_already_has_an_account_manager()
    {
        $this->travel(-5)->days();

        $sdr = User::factory()->withRole('sales-development-manager')->create();

        $bdm = User::factory()->withRole('business-development-manager')->create();

        $company = Company::factory()->createQuietly();

        $company->assign($sdr)->asSalesDevelopmentRepresentative();
        $company->assign($bdm)->asBusinessDevelopmentManager();
        $company->assign(User::factory()->withRole('account-manager')->create())->asAccountManager();

        $this->travelBack();

        ReleaseCompaniesBackToQueue::dispatch();

        $company->refresh();

        $this->assertNotNull($company->salesDevelopmentRepresentative);
        $this->assertNotNull($company->businessDevelopmentManager);
    }

    #[Test]
    public function a_company_is_ignored_if_it_has_not_been_five_days_since_assignment()
    {
        $this->travel(-4)->days();

        $sdr = User::factory()->withRole('sales-development-manager')->create();

        $bdm = User::factory()->withRole('business-development-manager')->create();

        $company = Company::factory()->createQuietly();

        $company->assign($sdr)->asSalesDevelopmentRepresentative();
        $company->assign($bdm)->asBusinessDevelopmentManager();

        $this->travelBack();

        ReleaseCompaniesBackToQueue::dispatch();

        $company->refresh();

        $this->assertNotNull($company->salesDevelopmentRepresentative);
        $this->assertNotNull($company->businessDevelopmentManager);
    }

    #[Test, DataProvider('contactTypeProvider')]
    public function a_company_is_ignored_if_it_has_not_been_five_days_since_last_contact($contactType)
    {
        $this->travel(-6)->days();

        $sdr = User::factory()->withRole('sales-development-manager')->create();

        $bdm = User::factory()->withRole('business-development-manager')->create();

        $company = Company::factory()->createQuietly();

        $company->assign($sdr)->asSalesDevelopmentRepresentative();
        $company->assign($bdm)->asBusinessDevelopmentManager();

        $this->travel(1)->days();

        ActivityFeed::factory()
            ->for($company, 'company')
            ->for($bdm, 'user')
            ->create([
                'item_type' => $contactType,
            ]);

        $this->travelBack();

        $this->assertCount(1, $company->refresh()->activityFeeds);

        ReleaseCompaniesBackToQueue::dispatch();

        $company->refresh();

        $this->assertNotNull($company->salesDevelopmentRepresentative);
        $this->assertNotNull($company->businessDevelopmentManager);
    }

    #[Test]
    public function a_company_is_ignored_if_it_has_future_tasks()
    {
        $this->travel(-6)->days();

        $sdr = User::factory()->withRole('sales-development-manager')->create();

        $bdm = User::factory()->withRole('business-development-manager')->create();

        $company = Company::factory()->createQuietly();

        $company->assign($sdr)->asSalesDevelopmentRepresentative();
        $company->assign($bdm)->asBusinessDevelopmentManager();

        $this->travelBack();

        Task::factory()->create([
            'assigned_user_id' => $bdm->id,
            'payload' => ['company_id' => $company->id],
            'available_at' => now()->addDay()
        ]);

        $this->assertCount(1, $company->refresh()->tasks);

        ReleaseCompaniesBackToQueue::dispatch();

        $company->refresh();

        $this->assertNotNull($company->salesDevelopmentRepresentative);
        $this->assertNotNull($company->businessDevelopmentManager);
    }

    #[Test]
    public function a_company_is_ignored_if_it_bought_a_lead_within_120_days()
    {
        $this->travel(-120)->days();

        $company = Company::factory()->createQuietly();

        ProductAssignment::factory()->for($company)->create();

        $this->travel(-5)->days();

        $sdr = User::factory()->withRole('sales-development-manager')->create();

        $bdm = User::factory()->withRole('business-development-manager')->create();

        $company->assign($sdr)->asSalesDevelopmentRepresentative();
        $company->assign($bdm)->asBusinessDevelopmentManager();

        $this->travelBack();

        ReleaseCompaniesBackToQueue::dispatch();

        $company->refresh();

        $this->assertNotNull($company->salesDevelopmentRepresentative);
        $this->assertNotNull($company->businessDevelopmentManager);
    }

    #[Test]
    public function a_company_is_released_if_it_bought_a_lead_greater_than_120_days_ago()
    {
        $this->travel(-365)->days();

        $company = Company::factory()->createQuietly();

        ProductAssignment::factory()->for($company)->create();

        $this->travel(-5)->days();

        $sdr = User::factory()->withRole('sales-development-manager')->create();

        $bdm = User::factory()->withRole('business-development-manager')->create();

        $company->assign($sdr)->asSalesDevelopmentRepresentative();
        $company->assign($bdm)->asBusinessDevelopmentManager();

        $this->travelBack();

        ReleaseCompaniesBackToQueue::dispatch();

        $company->refresh();

        $this->assertNull($company->salesDevelopmentRepresentative);
        $this->assertNull($company->businessDevelopmentManager);
    }

    public static function contactTypeProvider()
    {
        return [
            [ActivityType::CALL->value],
            [ActivityType::TEXT->value],
            [ActivityType::EMAIL->value],
        ];
    }
}
