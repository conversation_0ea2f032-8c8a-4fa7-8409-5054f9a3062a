<?php

namespace Tests\Unit\Observers;

use App\Jobs\Prospects\VerifyNewBuyerProspectJob;
use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class NewBuyerProspectObserverTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_dispatches_verification_job_when_new_buyer_prospect_is_created()
    {
        Queue::fake();

        $prospect = NewBuyerProspect::factory()->create();

        Queue::assertPushed(VerifyNewBuyerProspectJob::class, function ($job) use ($prospect) {
            return $job->newBuyerProspectId === $prospect->id;
        });
    }
}
