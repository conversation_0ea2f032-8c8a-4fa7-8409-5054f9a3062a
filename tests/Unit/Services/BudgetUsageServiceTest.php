<?php

namespace Tests\Unit\Services;

use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\Locations\LocationType;
use App\Enums\Timezone;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use App\Models\USZipCode;
use App\Repositories\Campaigns\Modules\Budget\BudgetRepository;
use App\Services\Campaigns\Modules\Budget\BudgetUsageService as BudgetUsageService;
use App\Services\OutreachCadence\TimeZoneHelperService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use function PHPUnit\Framework\assertEquals;
use function PHPUnit\Framework\assertFalse;
use function PHPUnit\Framework\assertGreaterThan;
use function PHPUnit\Framework\assertLessThan;
use function PHPUnit\Framework\assertTrue;

class BudgetUsageServiceTest extends TestCase
{
    use DatabaseTransactions;

    const int BUDGET_LIMIT = 500;
    const int PRODUCT_COST = 100;

    const int OFFSET = -7;

    private BudgetUsageService $budgetUsageService;
    private BudgetRepository $budgetRepository;
    private TimeZoneHelperService $timezoneHelperService;
    private Company $company;
    private CompanyCampaign $campaign;
    private Budget $budget;
    private Carbon $initialNow;
    private int $productCount;
    private string $timezoneString;

    protected function setUp(): void
    {
        parent::setUp();

        $this->budgetUsageService = new BudgetUsageService();
        $this->budgetRepository = new BudgetRepository();
        $this->timezoneHelperService = app(TimeZoneHelperService::class);

        /** @var Company $company */
        $company = Company::factory()
            ->has(CompanyCampaign::factory()->state([
                    CompanyCampaign::FIELD_MAXIMUM_BUDGET_USAGE => 100,
                ])->has(BudgetContainer::factory()
                    ->has(Budget::factory()->state([
                        Budget::FIELD_TYPE              => BudgetType::TYPE_DAILY_SPEND,
                        Budget::FIELD_KEY               => 'verified',
                        Budget::FIELD_VALUE             => self::BUDGET_LIMIT,
                        Budget::FIELD_STATUS            => 1,
                        Budget::FIELD_LAST_MODIFIED_AT  => now(),
                        Budget::FIELD_LAST_MODIFIED_UTC => self::OFFSET,
                    ]))->count(1)
                ), 'futureCampaigns'
            )->count(1)
            ->has(CompanyLocation::factory(), 'locations')
            ->count(1)
            ->createQuietly()
            ->first();
        $address = Address::factory()->createQuietly()->first();
        $company->locations()->first()->update([
            CompanyLocation::FIELD_ADDRESS_ID => $address->id,
        ]);
        $company->locations()->first()->address()->update([
            Address::FIELD_UTC   => self::OFFSET,
            Address::FIELD_STATE => 'CO',
        ]);

        $this->company = $company;
        $this->campaign = $this->company->futureCampaigns()->first();
        $locationModule = $this->campaign->locationModule()->firstOrCreate();

        $zipCodeString = "99999";
        USZipCode::query()
            ->updateOrCreate([
                'zip_code' => $zipCodeString
            ], [
                'zip_type' => "S",
                'utc' => self::OFFSET,
                'dst' => "N",
            ]);

        $location = Location::factory()->create([
            Location::FIELD_TYPE     => LocationType::POSTAL_CODE,
            Location::FIELD_ZIP_CODE => $zipCodeString,
        ]);

        CompanyCampaignLocationModuleLocation::factory()
            ->create([
                CompanyCampaignLocationModuleLocation::FIELD_COMPANY_ID  => $company->id,
                CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID   => $locationModule->id,
                CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID => $location->id,
                CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE    => $zipCodeString,
            ]);

        $this->budget = $company->futureCampaigns()->first()->budgetContainer->budgets()->first();

        $timezone = Timezone::from(self::OFFSET);
        $this->timezoneString = Timezone::getTimezoneString($timezone);

        // Set initial time to 8am local
        $hour = 8;
        Carbon::setTestNow(now($this->timezoneString)->setHour($hour)->setMinute(0)->setSecond(0));

        // Budget was updated yesterday
        $this->budget->update([
            Budget::FIELD_LAST_MODIFIED_AT  => now()->subDay(),
            Budget::FIELD_LAST_MODIFIED_UTC => self::OFFSET,
        ]);

        // how many products to hit budget
        $assignmentCount = floor(self::BUDGET_LIMIT / self::PRODUCT_COST);
        $this->productCount = $assignmentCount;
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Carbon::setTestNow(null);
    }

    #[Test]
    public function company_should_return_correct_timezone(): void
    {
        $companyTimezone = $this->budgetRepository->getCampaignUTCOffset($this->campaign);

        assertEquals(self::OFFSET, $companyTimezone);
    }

    #[Test]
    public function it_should_have_no_budget_usage_at_8(): void
    {
        Carbon::setTestNow(now($this->timezoneString)->setHour(8));

        $usage = $this->budgetUsageService->getCurrentUsage($this->budget);
        $budgetIsExceeded = $this->budgetUsageService->isBudgetExceeded($this->budget);

        assertEquals(0, $usage);
        assertFalse($budgetIsExceeded);
    }

    #[Test]
    public function it_should_be_over_budget_after_12(): void
    {
        $this->createProductsTodayAtMidday($this->productCount + 1);
        Carbon::setTestNow(now($this->timezoneString)->setHour(13));

        $usage = $this->budgetUsageService->getCurrentUsage($this->budget);
        $percentage = $this->budgetUsageService->getCurrentUsagePercent($this->budget);
        $budgetIsExceeded = $this->budgetUsageService->isBudgetExceeded($this->budget);

        assertEquals(($this->productCount + 1) * self::PRODUCT_COST, $usage);
        assertGreaterThan(100, $percentage);
        assertTrue($budgetIsExceeded);
    }

    #[Test]
    public function it_should_still_be_over_budget_before_midnight(): void
    {
        $this->createProductsTodayAtMidday($this->productCount + 1);
        Carbon::setTestNow(now($this->timezoneString)->setHour(23));

        $usage = $this->budgetUsageService->getCurrentUsage($this->budget);
        $percentage = $this->budgetUsageService->getCurrentUsagePercent($this->budget);
        $budgetIsExceeded = $this->budgetUsageService->isBudgetExceeded($this->budget);

        assertEquals(($this->productCount + 1) * self::PRODUCT_COST, $usage);
        assertGreaterThan(100, $percentage);
        assertTrue($budgetIsExceeded);
    }

    #[Test]
    public function it_should_be_under_budget_after_midnight(): void
    {
        $this->createProductsTodayAtMidday($this->productCount + 1);
        // set clock to after midnight (currently this is failing between 12am -> 1am, probably DST
        //TODO: get this to pass with ->addHour() instead of 2 hours
        Carbon::setTestNow(now($this->timezoneString)->endOfDay()->addHours(2));

        $usage = $this->budgetUsageService->getCurrentUsage($this->budget);
        $percentage = $this->budgetUsageService->getCurrentUsagePercent($this->budget);
        $budgetIsExceeded = $this->budgetUsageService->isBudgetExceeded($this->budget);

        assertEquals(($this->productCount + 1) * self::PRODUCT_COST, $usage);
        assertLessThan(100, $percentage);
        assertFalse($budgetIsExceeded);
    }

    private function createProductsTodayAtMidday(int $productCount): void
    {
        $soldAt = now($this->timezoneString)->hour(12);
        $this->createProductAssignments($productCount, $soldAt->utc());
    }

    private function createProductAssignments(int $quantity, Carbon $deliveredAt): void
    {
        ProductAssignment::factory()->count($quantity)->createQuietly([
            ProductAssignment::FIELD_BUDGET_ID    => $this->budget->id,
            ProductAssignment::FIELD_COMPANY_ID   => $this->company->id,
            ProductAssignment::FIELD_DELIVERED    => true,
            ProductAssignment::FIELD_CHARGEABLE   => true,
            ProductAssignment::FIELD_DELIVERED_AT => $deliveredAt,
            ProductAssignment::FIELD_COST         => self::PRODUCT_COST,
        ]);

        ProductRejection::truncate();
    }
}