<?php

namespace Tests\Unit\Jobs\Prospects;

use App\Jobs\Prospects\VerifyNewBuyerProspectJob;
use App\Models\Prospects\NewBuyerProspect;
use App\Services\Odin\ConsumerProductVerification\IpQuality\IpQualityScoreService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class VerifyNewBuyerProspectJobTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_calls_verification_service_with_correct_prospect()
    {
        $prospect = NewBuyerProspect::factory()->create();
        
        $mockService = Mockery::mock(IpQualityScoreService::class);
        $mockService->shouldReceive('verifyNewBuyerProspect')
            ->once()
            ->with(Mockery::on(function ($arg) use ($prospect) {
                return $arg instanceof NewBuyerProspect && $arg->id === $prospect->id;
            }));

        $this->app->instance(IpQualityScoreService::class, $mockService);

        $job = new VerifyNewBuyerProspectJob($prospect->id);
        $job->handle($mockService);
    }

    #[Test]
    public function it_throws_exception_when_prospect_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $mockService = Mockery::mock(IpQualityScoreService::class);
        $mockService->shouldNotReceive('verifyNewBuyerProspect');

        $job = new VerifyNewBuyerProspectJob(999999); // Non-existent ID
        $job->handle($mockService);
    }
}
