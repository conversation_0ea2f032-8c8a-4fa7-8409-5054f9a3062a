import {defineStore} from "pinia";
import {ref} from "vue";
import SharedApiService from "../../vue/components/Shared/services/api.js";

export const useCompanyStore = defineStore('company', () => {
    const company = ref({});
    const relationshipOptions = ref([]);
    const sharedApi = SharedApiService.make();

    const initialize = (initCompany, initRelationshipOptions) => {
        company.value = initCompany;
        relationshipOptions.value = initRelationshipOptions;
    }

    return {
        company,
        relationshipOptions,
        sharedApi,
        initialize
    }
});
