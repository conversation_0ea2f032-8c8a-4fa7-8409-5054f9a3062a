<template>
    <div class="inline-flex items-center gap-2">
        <event-side-line-icon :icon="invoiceHelper.ITEM_PRESENTATION[payload.item_details.billableType]?.icon" :icon-color="iconColor" :dark-mode="darkMode"/>
        <p class="text-xs font-semibold text-gray-400">{{totalEvents}}</p>
        <p class="text-xs font-semibold text-gray-400">x</p>
    </div>
    <div class="flex-auto">
        <div class="flex items-center justify-between gap-x-4">
            <div class="flex flex-wrap items-center gap-1 py-0.5 text-xs leading-5 text-gray-500">
                <component v-if="computedEventTitleComponent" :is="computedEventTitleComponent" :item-details="payload.item_details"/>
                <span v-else class="font-medium text-gray-900">{{ invoiceHelper.ITEM_PRESENTATION[payload.item_details.billableType]?.added_title }}</span> by
                <badge
                    class="flex items-center"
                    color="gray"
                    :dark-mode="darkMode"
                >
                    <simple-icon
                        size="xs"
                        :dark-mode="darkMode"
                        :icon="invoiceHelper.AUTHOR_STYLES[payload.author_details.author_type]?.icon"
                        :tooltip="invoiceHelper.AUTHOR_STYLES[payload.author_details.author_type]?.title ?? 'Item added'"
                    />
                    <div class="ml-1">
                        {{payload.author_details.name}}
                    </div>
                    <div v-if="payload.author_details.author_type !== invoiceHelper.AUTHOR_TYPES.SYSTEM" class="ml-1">
                        ({{ payload.author_details.author_id }})
                    </div>
                </badge>
                <time class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ daysAgo + ' (' + createdAt + ')' }}</time>
            </div>
            <div @click="show = !show" class="text-xs cursor-pointer font-semibold text-primary-500">{{show ? "Show less" : "Show more"}}</div>
        </div>
        <div v-if="show" class="flex justify-between gap-2 text-xs leading-6 text-gray-500 py-1 px-5 my-1 rounded-md" :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']">
            <div class="inline-flex flex-col">
                <div class="uppercase">Quantity</div>
                <div class="font-medium text-gray-900">{{payload.item_details.quantity}}</div>
            </div>
            <div class="inline-flex flex-col">
                <div class="uppercase">Unit Price</div>
                <div class="font-medium text-gray-900">{{$filters.centsToFormattedDollars(payload.item_details.unitPrice)}}</div>
            </div>
            <div class="inline-flex flex-col">
                <div class="uppercase">Type</div>
                <div class="font-medium text-gray-900">{{payload.item_details.billableType}}</div>
            </div>
            <div class="inline-flex flex-col">
                <div class="uppercase">Description</div>
                <div class="font-medium text-gray-900">{{payload.item_details.description}}</div>
            </div>
        </div>
    </div>
</template>
<script>
import EventSideLineIcon from "../EventSideLineIcon.vue";
import useInvoiceHelper from "../../../../../../composables/useInvoiceHelper";
import SimpleIcon from "../../../../Mailbox/components/SimpleIcon.vue";
import Badge from "../../../../Shared/components/Badge.vue";
const invoiceHelper = useInvoiceHelper()
import ProductAddedEventTitle from "./ProductAddedEventTitle.vue";
export default {
    name: "InvoiceItemAddedEvent",
    components: {Badge, SimpleIcon, EventSideLineIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        createdAt: {
            type: String,
            default: null
        },
        daysAgo: {
            type: String,
            default: ""
        },
        title: {
            type: String,
            default: ""
        },
        payload: {
            type: Object,
            default: {}
        },
        icon: {
            type: String,
            default: null
        },
        iconColor: {
            type: String,
            default: null
        },
        eventType: {
            type: String,
            default: null,
        },
        totalEvents: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            invoiceHelper,
            show: false,
        }
    },
    computed: {
        computedEventTitleComponent(){
            return {
                'product': ProductAddedEventTitle
            }[this.payload.item_details?.billableType]
        }
    }
}
</script>
