<template>
    <div class="inline-flex items-center gap-2">
        <event-side-line-icon
            :icon="invoiceHelper.getStatusStyle([payload.status_details.new_status.id]).icon"
            :icon-color="invoiceHelper.getStatusStyle([payload.status_details.new_status.id]).icon_color"
            :dark-mode="darkMode"
        />
        <p class="text-xs font-semibold text-gray-400">{{totalEvents}}</p>
        <p class="text-xs font-semibold text-gray-400">x</p>
    </div>
    <div class="flex flex-wrap gap-1 items-center py-0.5 text-xs leading-5 text-gray-500">
        <span class="font-medium" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']"> {{ title }} </span> from
        <badge :color="invoiceHelper.getStatusStyle([payload.status_details.old_status.id]).color" :dark-mode="darkMode">{{payload.status_details.old_status.name}}</badge> to
        <badge :color="invoiceHelper.getStatusStyle([payload.status_details.new_status.id]).color" :dark-mode="darkMode">{{payload.status_details.new_status.name}}</badge> by
        <badge
            class="flex items-center"
            color="gray"
            :dark-mode="darkMode"
        >
            <simple-icon
                size="xs"
                :dark-mode="darkMode"
                :icon="invoiceHelper.AUTHOR_STYLES[payload.author_details.author_type]?.icon"
                :tooltip="invoiceHelper.AUTHOR_STYLES[payload.author_details.author_type]?.title"
            />
            <div class="ml-1">
                {{payload.author_details.name}}
            </div>
            <div v-if="payload.author_details.author_type !== invoiceHelper.AUTHOR_TYPES.SYSTEM" class="ml-1">
                ({{ payload.author_details.author_id }})
            </div>
        </badge>
        <time class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ daysAgo + ' (' + createdAt + ')' }}</time>
    </div>
</template>
<script>

import EventSideLineIcon from "./EventSideLineIcon.vue";
import Badge from "../../../Shared/components/Badge.vue";
import useInvoiceHelper from "../../../../../composables/useInvoiceHelper";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
const invoiceHelper = useInvoiceHelper()
export default {
    name: "InvoiceStatusUpdated",
    components: {SimpleIcon, Badge, EventSideLineIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        createdAt: {
            type: String,
            default: null
        },
        daysAgo: {
            type: String,
            default: ""
        },
        title: {
            type: String,
            default: ""
        },
        payload: {
            type: Object,
            default: {}
        },
        icon: {
            type: String,
            default: null
        },
        iconColor: {
            type: String,
            default: null
        },
        eventType: {
            type: String,
            default: null,
        },
        totalEvents: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            invoiceHelper,
        }
    }
}
</script>
