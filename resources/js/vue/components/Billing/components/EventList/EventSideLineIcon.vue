<template>
    <div class="relative flex h-6 w-6 flex-none items-center justify-center" :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']">
        <simple-icon  v-if="icon !== null" :icon="icon" :color="darkMode ? 'text-slate-100' : 'text-slate-900'" :dark-mode="darkMode"/>
        <div v-else class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300" />
    </div>
</template>
<script>
import useSimpleIcon from "../../../../../composables/useSimpleIcon";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";

const simpleIcon = useSimpleIcon()
export default {
    name: "EventSideLineIcon",
    components: {SimpleIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        icon: {
            type: String,
            default: null,
        },
        iconColor: {
            type: String,
            default: null,
        }
    },
    data() {
        return {
            simpleIcon,
        }
    }
}
</script>
