<template>
    <div v-if="modelValue"
         class="grid gap-6 text-xs border-t font-semibold mb-1 items-center py-3"
         :class="{...itemStyles, 'grid-cols-8': disabled && !actionable, 'grid-cols-9': !disabled || actionable, 'border-dark-border ' : darkMode, 'border-light-border ' : !darkMode}"

    >
        <textarea
            v-if="modelValue.billable_type === 'manual'"
            :disabled="disabled"
            class="flex col-span-4 px-0 border-none focus:ring-0 resize-none bg-transparent"
            :class="{'': !darkMode, 'bg-dark-module text-blue-400': darkMode}"
            placeholder="Item description..."
            v-model="modelValue.description"
        />
        <a
            class="flex col-span-4  border-none focus:ring-0 resize-none bg-transparent text-primary-500"
            v-else-if="modelValue.billable_type === 'product'"
            target="_blank"
            v-bind:href="`consumer-product/?product_assignment_id=${modelValue.billable_id}`"
        >
            {{modelValue.description}}
        </a>
        <p
            class="flex col-span-4  border-none focus:ring-0 resize-none bg-transparent text-primary-500"
            v-else-if="modelValue.billable_type === 'bundle'"
        >
            {{modelValue.description}}
        </p>
        <!-- option type of credit -->
        <div v-else class="flex col-span-4 border-none relative focus:ring-0 resize-none">
            <dropdown
                :disabled="disabled"
                class="z-100 flex w-fit"
                :options="creditTypes"
                :dark-mode="darkMode"
                v-model="modelValue.billable_id"
                placeholder="Select Credit Type"
                placement="top"
            />
        </div>
        <custom-inline-input
            v-if="!disabled && modelValue.billable_type !=='product'"
            type="number"
            v-model="modelValue.quantity"
            :add-default-value="false"
        />
        <div v-else>{{ modelValue.quantity }}</div>
        <custom-inline-input
            v-if="!disabled && modelValue.billable_type !=='product'"
            type="number"
            v-model="modelValue.unit_price_in_dollars"
            @update:modelValue="handleUpdate"
            :add-default-value="false"
        />
        <div v-else>{{ $filters.centsToFormattedDollars(modelValue.unit_price) }}</div>
        <div>{{ formattedAddedBy }}</div>
        <div :class="{'flex justify-end': disabled && !actionable}">
            ${{ (modelValue.quantity * modelValue.unit_price) / 100 }}
        </div>
        <slot name="actions">
        </slot>
    </div>
</template>
<script>
import CustomInlineInput from "../../../Shared/components/CustomInlineInput.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import {useCreditManagementStore} from "../../../../../stores/credit/credit-management.store";
import {useUserStore} from "../../../../../stores/user-store";
import CustomInput from "../../../Shared/components/CustomInput.vue";

export default {
    name: "InvoiceItem",
    components: {CustomInput, Dropdown, CustomInlineInput},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            required: true,
            default: {}
        },
        disabled: {
            type: Boolean,
            default: false
        },
        actionable: {
            type: Boolean,
            default: false
        }
    },
    emits: ['update:modelValue'],
    data() {
        return {
            creditStore: useCreditManagementStore(),
            userStore: useUserStore(),
            creditTypes: [],
            addedBy: null,
        }
    },
    computed: {
        formattedAddedBy() {
            const { id, name } = this.modelValue?.added_by ?? {};

            // Drafting
            if (!id && !name) {
                return `${this.userStore.user.name} (Me)`;
            }

            // System generated
            if (name && !id) {
                return name;
            }

            const isCurrentUser = id === this.userStore.user.id;
            const suffix = isCurrentUser ? "(Me)" : `(${id})`;

            return `${name} ${suffix}`;
        },
        itemStyles() {
            const styles = {
                default: "",
                product: "",
                credit: "",
            }

            return styles[this.modelValue.billable_type] ?? styles.default;
        },
    },
    created() {
        if (this.modelValue.billable_type === 'credit') {
            this.getCreditTypes();
        }
    },
    methods: {
        handleUpdate() {
            this.$emit('update:modelValue', {
                ...this.modelValue,
                unit_price: Number(this.modelValue.unit_price_in_dollars) * 100
            })
        },
        async getCreditTypes() {
            try {
                const response = await this.creditStore.api.getCreditTypes({
                    is_cash: 1
                });
                this.creditTypes = response.data.data;
            } catch (error) {
                console.error(error)
            }
        },
    }
}

</script>
