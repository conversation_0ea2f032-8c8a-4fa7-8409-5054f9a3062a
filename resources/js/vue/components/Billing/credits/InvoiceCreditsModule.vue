<template>
    <div
        class="px-5 py-4 flex flex-col"
    >
        <div class="flex justify-between mb-2">
            <div class="text-lg font-bold ">
                <svg class="inline mr-1 w-4 fill-current text-purple-500" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M10.1562 0.136841C4.63325 0.136841 0.15625 4.61384 0.15625 10.1368C0.15625 15.6598 4.63325 20.1368 10.1562 20.1368C15.6792 20.1368 20.1562 15.6598 20.1562 10.1368C20.1562 4.61384 15.6792 0.136841 10.1562 0.136841ZM10.1562 1.13684C10.1562 3.52379 9.20804 5.81297 7.52021 7.5008C5.83238 9.18863 3.5432 10.1368 1.15625 10.1368C3.5432 10.1368 5.83238 11.0851 7.52021 12.7729C9.20804 14.4607 10.1563 16.7499 10.1562 19.1368C10.1562 16.7499 11.1045 14.4607 12.7923 12.7729C14.4801 11.0851 16.7693 10.1368 19.1562 10.1368C16.7693 10.1368 14.4801 9.18863 12.7923 7.5008C11.1045 5.81297 10.1563 3.52379 10.1562 1.13684Z"/>
                </svg>

                Credits
            </div>
            <CustomButton v-if="showApplyButton" @click="toggleApplyCreditForm">
                {{ showApplyCreditForm ? 'Cancel' : 'Add Credit' }}
            </CustomButton>
        </div>
        <div>
            <apply-credit-to-invoice-modal
                v-if="showApplyCreditForm"
                class="mb-10"
                :dark-mode="darkMode"
                :invoice-id="invoiceId"
                :company-id="companyId"
            />
            <div
                class="text-xs grid pb-3 font-bold text-slate-500 uppercase grid-cols-4"
            >
                <div>Type</div>
                <div>Amount</div>
                <div>Author</div>
                <div>Date</div>
            </div>
            <loading-spinner v-if="invoiceCreditsStore.loadingCreditsApplied"></loading-spinner>
            <div v-else-if="invoiceCreditsStore.creditsApplied.length === 0"
                 class="flex justify-center items-center py-2 text-xs font-semibold text-slate-500"
            >
                No credits applied
            </div>
            <div v-else v-for="invoiceCredit in invoiceCreditsStore.creditsApplied"
                 class="grid grid-cols-4 text-xs font-semibold border-t items-center py-3" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                <div><Badge color="purple" :dark-mode="darkMode">{{ $filters.toProperCase(invoiceCredit.credit_type) }}</Badge></div>
                <div>{{ $filters.centsToFormattedDollars(invoiceCredit.amount_applied) }}</div>
                <div>
                    <author-badge :dark-mode="darkMode" :author="invoiceCredit.author" />
                </div>
                <div>{{ invoiceCredit.applied_at }}</div>
            </div>
            <div class="flex uppercase text-sm font-semibold mt-2">
                <div class="mr-2 text-slate-500">
                    Total Value:
                </div>
                <div class="font-bold">
                    {{$filters.centsToFormattedDollars(invoiceCreditsStore.totalCreditsApplied)}}
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import {useInvoiceCreditsStore} from "../../../../stores/invoice/invoice-credits.store.js";
import CustomButton from "../../Shared/components/CustomButton.vue";
import CustomInlineInput from "../../Shared/components/CustomInlineInput.vue";
import CustomInlineSelect from "../../Shared/components/CustomInlineSelect.vue";
import DropdownSelector from "../../Shared/components/DropdownSelector.vue";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import Badge from "../../Shared/components/Badge.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import ApplyCreditToInvoiceModal from "./ApplyCreditToInvoiceForm.vue";
import {useCompanyCreditManagementStore} from "../../../../stores/credit/company-credit-management.store.js";
import {useInvoiceModalStore} from "../../../../stores/invoice/invoice-modal.store.js";
import AuthorBadge from "../components/AuthorBadge.vue";
import {ROLES, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";

const simpleIcon = useSimpleIcon()

export default {
    name: "InvoiceCreditsModule",
    components: {
        LoadingSpinner,
        AuthorBadge,
        ApplyCreditToInvoiceModal,
        LabeledValue,
        Badge,
        Dropdown,
        Autocomplete,
        DropdownSelector,
        CustomInlineSelect,
        CustomInlineInput,
        CustomButton,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        invoiceId: {
            type: Number,
            required: true
        },
        companyId: {
            type: Number,
            required: true
        },
    },
    data() {
        return {
            simpleIcon,
            invoiceCreditsStore: useInvoiceCreditsStore(),
            applyingCredit: false,
            showApplyCreditForm: false,
            selectedCreditType: null,
            loading: false,
            creditStore: useCompanyCreditManagementStore(),
            invoiceStore: useInvoiceModalStore(),
            rolesPermissions: useRolesPermissions()
        }
    },
    mounted() {
        this.invoiceCreditsStore.resetStore();
        this.invoiceCreditsStore.getCreditsApplied(this.invoiceId)
    },
    computed: {
        showApplyButton(){
            return this.rolesPermissions.hasRole(ROLES.FINANCE_OWNER)
                && this.invoiceStore?.invoiceTotals?.outstanding > 0
                && !this.invoiceStore?.isProcessingPayment
                && !this.invoiceCreditsStore?.loadingCreditsApplied
        },
    },
    methods: {
        toggleApplyCreditForm() {
            this.showApplyCreditForm = !this.showApplyCreditForm

            if (!this.showApplyCreditForm) {
                this.selectedCreditType = null
                this.invoiceCreditsStore.invoiceCredit = {}
            }
        }
    }
}
</script>
