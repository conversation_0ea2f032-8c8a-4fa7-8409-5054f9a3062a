<template>
    <div class="rounded-lg border" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="flex items-center gap-2 p-5 border-b rounded-t-lg sticky top-16 z-10" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
            <h5 @click="$emit('close')" class="text-slate-500 hover:text-primary-500 cursor-pointer text-sm uppercase font-bold leading-tight">Invoices</h5>
            <svg class="fill-current w-1.5" viewBox="0 0 8 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1.27336 0.519944C1.66389 0.12942 2.2969 0.12942 2.68742 0.519944L7.68742 5.51994C8.07795 5.91047 8.07795 6.54348 7.68742 6.93401L2.68742 11.934C2.2969 12.3245 1.66389 12.3245 1.27336 11.934C0.882838 11.5435 0.882838 10.9105 1.27336 10.5199L5.56633 6.22698L1.27336 1.93401C0.882838 1.54348 0.882838 0.910468 1.27336 0.519944Z"/>
            </svg>

            <div class="font-bold leading-tight">
                {{ !invoiceId ? "Create New Invoice" : "#" + invoiceId }}
            </div>
            <simple-icon
                v-if="invoiceId"
                :clickable="!refreshingInvoiceData"
                size="w-4 h-4"
                :class="refreshingInvoiceData ? 'animate-spin' : ''"
                :dark-mode="darkMode"
                :icon="simpleIcon.icons.ARROW_PATH"
                :color="simpleIcon.colors.BLUE"
                :tooltip="refreshingInvoiceData ? 'Refreshing...' : 'Refresh Invoice Data'"
                @click="handleRefresh"
            />
            <simple-icon
                v-if="invoiceId"
                clickable
                size="w-4 h-4"
                :dark-mode="darkMode"
                :icon="simpleIcon.icons.LINK"
                :color="simpleIcon.colors.BLUE"
                tooltip="Copy Link"
                @click="handleCopyLink"
            />
        </div>
        <view-create-invoice-modal-content
            :company-id="companyId"
            :company-name="companyName"
            @invoice-created-updated="handleInvoiceCreated"
            :dark-mode="darkMode"
            :readonly="readonly"
        />
    </div>

</template>
<script>
import Modal from "../Shared/components/Modal.vue";
import {useInvoiceModalStore} from "../../../stores/invoice/invoice-modal.store";
import ViewCreateInvoiceModalContent from "./ViewCreateInvoiceModalContent.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import useClipboard from "../../../composables/useClipboard.js";
import {useToastNotificationStore} from "../../../stores/billing/tost-notification.store.js";
import useQueryParams from "../../../composables/useQueryParams.js";

const simpleIcon = useSimpleIcon()
export default {
    name: "ViewCreateInvoiceModal",
    components: {SimpleIcon, ViewCreateInvoiceModalContent, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null,
        },
        companyName: {
            type: String,
            default: null
        },
        invoiceId: {
            type: Number,
            default: null,
        },
        readonly: {
            type: Boolean,
            default: null
        }
    },
    created() {
        if (this.invoiceId !== null) {
            this.getInvoiceData()
        }
    },
    data () {
        return {
            queryParamsHelper: useQueryParams(),
            invoiceStore: useInvoiceModalStore(),
            clipboard: useClipboard(),
            toastNotificationStore: useToastNotificationStore(),
            simpleIcon,
            refreshingInvoiceData: false
        }
    },
    emits: ['close','invoice-created-updated'],
    methods: {
        async handleRefresh(){
            if (this.refreshingInvoiceData) {
                return
            }

            this.refreshingInvoiceData = true
            await this.invoiceStore.refreshInvoice()
            this.refreshingInvoiceData = false
        },
        handleInvoiceCreated(invoiceUuid, message) {
            this.invoiceStore.$reset();
            this.$emit('invoice-created-updated', invoiceUuid, message);
        },
        handleClose() {
            this.invoiceStore.$reset();
            this.$emit('close');
        },
        async getInvoiceData() {
            await this.invoiceStore.retrieveInvoiceData(this.invoiceId);
        },
        handleCopyLink() {
            const url = this.queryParamsHelper.mountUrlWithSearchParams({
                invoice_id: this.invoiceId,
                tab: 'Invoices'
            }, this.queryParamsHelper.getCurrentUrl())

            this.clipboard.addToClipboard(url)
            this.toastNotificationStore.notifySuccess(
                "Invoice Link Copied to clipboard"
            )
        }
    }
}
</script>
