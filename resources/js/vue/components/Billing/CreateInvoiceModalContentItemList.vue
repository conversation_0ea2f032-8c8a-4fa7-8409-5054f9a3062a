<template>
    <div class="p-5">
        <div @click="showEvents = !showEvents" class="group flex justify-between gap-2 items-center cursor-pointer">
            <div class="text-lg font-bold group-hover:text-primary-500">
                <svg class="inline mr-1 w-4" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13.2173 3.13317L7.7173 0.123791C7.57035 0.0425919 7.4052 0 7.2373 0C7.06941 0 6.90426 0.0425919 6.7573 0.123791L1.2573 3.13442C1.10023 3.22036 0.969119 3.34689 0.87765 3.50081C0.78618 3.65472 0.737711 3.83037 0.737305 4.00942V9.98692C0.737711 10.166 0.78618 10.3416 0.87765 10.4955C0.969119 10.6494 1.10023 10.776 1.2573 10.8619L6.7573 13.8725C6.90426 13.9537 7.06941 13.9963 7.2373 13.9963C7.4052 13.9963 7.57035 13.9537 7.7173 13.8725L13.2173 10.8619C13.3744 10.776 13.5055 10.6494 13.597 10.4955C13.6884 10.3416 13.7369 10.166 13.7373 9.98692V4.01004C13.7372 3.83068 13.6889 3.65464 13.5974 3.50036C13.5059 3.34609 13.3747 3.21926 13.2173 3.13317ZM7.2373 0.998791L12.2592 3.74879L10.3979 4.76692L5.37605 2.01692L7.2373 0.998791ZM7.2373 6.49879L2.21543 3.74879L4.3348 2.58817L9.35668 5.33817L7.2373 6.49879ZM12.7373 9.98942L7.7373 12.7263V7.36317L9.7373 6.26879V8.49879C9.7373 8.6314 9.78998 8.75858 9.88375 8.85234C9.97752 8.94611 10.1047 8.99879 10.2373 8.99879C10.3699 8.99879 10.4971 8.94611 10.5909 8.85234C10.6846 8.75858 10.7373 8.6314 10.7373 8.49879V5.72129L12.7373 4.62692V9.98692V9.98942Z" fill="#0081FF"/>
                </svg>
                Item List
                <span class="text-slate-500 text-sm">(
                <loading-spinner v-if="loading" size="w-3 h-3" wrapper-style="inline-flex" margin="m-0"></loading-spinner>
                <span class="text-primary-500" v-else>{{invoiceStore.invoiceItems.length}}</span>
                )</span>
            </div>
            <simple-icon
                :class="{showEvents}"
                :dark-mode="darkMode"
                :icon="simpleIcon.icons.CHEVRON_DOWN"
                :color="simpleIcon.colors.BLUE"
                :size="simpleIcon.sizes.MD"
                :style="showEvents ? 'transition duration-150 rotate-180' : 'transition duration-150'"
            />
        </div>
        <div v-if="showEvents"
            class="grid pb-3 mt-4 font-bold text-xs text-slate-500 uppercase gap-6 pr-6"
            :class="{'grid-cols-9': actionable, 'grid-cols-8': !actionable,  'border-dark-border' : darkMode, 'border-light-border' : !darkMode}"
        >
            <div class="col-span-4">
                Description
            </div>
            <div>
                Quantity
            </div>
            <div>
                Price
            </div>
            <div>
                Added By
            </div>
            <div :class="{'flex justify-end': !actionable}">
                Amount
            </div>
        </div>

        <div v-if="showEvents" class="pr-6 relative z-10" :class="[!invoiceStore.isEditable ? 'overflow-y-auto min-h-32 max-h-64' : '']">
            <div v-if="invoiceStore.invoiceItems.length === 0"
                 class="flex justify-center items-center py-2 text-xs font-semibold text-slate-500">
                No Items Added
            </div>
            <div v-else class="flex flex-col gap-1" v-for="(item, index) in invoiceStore.invoiceItems">
                <invoice-item
                    :dark-mode="darkMode"
                    :model-value="item"
                    @update:modelValue="(val) => handleUpdate(val, index)"
                    :disabled="!invoiceStore.isEditable"
                    :actionable="actionable"
                >
                    <template v-slot:actions v-if="invoiceStore.isEditable || invoiceStore.refunding">
                        <div class="flex justify-end items-center">
                            <simple-icon v-if="invoiceStore.isEditable" :icon="simpleIcon.icons.X_MARK" clickable
                                         @click="invoiceStore.removeItem(index)"/>
                            <simple-icon v-if="invoiceStore.refunding && !inRefundList(item) && invoiceStore.refundEditable"
                                         :icon="simpleIcon.icons.PLUS" clickable @click="invoiceStore.refundItem(index)"/>
                        </div>
                    </template>
                </invoice-item>
            </div>
        </div>
        <div v-if="showEvents">
            <div v-if="invoiceStore.isEditable" class="grid gap-6 mt-4 items-center">
                <div class="flex gap-2">
                    <button-dropdown
                        class="relative z-10"
                        :dark-mode="darkMode" @selected="(option) => invoiceStore.invoiceItemAdded(option.id)"
                        :options="invoiceItemTypes" options-list-placement="left" position="bottom-0">
                        <custom-button :dark-mode="darkMode">+ Add an item</custom-button>
                    </button-dropdown>
                    <custom-button
                        :disabled="invoiceStore.uninvoicedItems.length === 0"
                        @click="invoiceStore.addRemoveUninvoiced" :dark-mode="darkMode">
                        {{ invoiceStore.uninvoicedAdded ? "Remove Uninvoiced" : "+ Uninvoiced Products" }}
                        ({{ invoiceStore?.uninvoicedItems?.length }})
                    </custom-button>
                </div>
            </div>
        </div>
        <div v-if="showEvents" class="flex gap-12 uppercase text-sm font-semibold pt-4 mt-2" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
            <div class="inline-flex items-center">
                <div class="mr-2 text-slate-500">
                    Sub total:
                </div>
                <div class="font-bold">
                    {{ $filters.centsToFormattedDollars(invoiceStore.subTotal) }}
                </div>
            </div>
            <div class="inline-flex items-center">
                <div class="mr-2 text-slate-500">
                    Credits applied:
                </div>
                <div class="font-bold">
                    {{ $filters.centsToFormattedDollars(invoiceStore.creditsAppliedToInvoice) }}
                </div>
            </div>
            <div class="inline-flex items-center">
                <div class="mr-2 text-slate-500">
                    Total Value:
                </div>
                <div class="font-bold">
                    {{ $filters.centsToFormattedDollars(totalIssuable) }}
                </div>
            </div>
        </div>
    </div>
</template>
<script>

import CustomButton from "../Shared/components/CustomButton.vue";
import {useInvoiceModalStore} from "../../../stores/invoice/invoice-modal.store";
import CustomInlineInput from "../Shared/components/CustomInlineInput.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon";
import InvoiceItem from "./components/CreateNewInvoice/InvoiceItem.vue";
import ButtonDropdown from "../Shared/components/ButtonDropdown.vue";
import {useCreditManagementStore} from "../../../stores/credit/credit-management.store.js";
import {useCompanyCreditManagementStore} from "../../../stores/credit/company-credit-management.store.js";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";

const simpleIcon = useSimpleIcon();

export default {
    name: "CreateInvoiceModalContentItemList",
    components: {
        LoadingSpinner,
        ButtonDropdown,
        InvoiceItem,
        SimpleIcon, CustomInlineInput, CustomButton
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            simpleIcon,
            invoiceStore: useInvoiceModalStore(),
            creditStore: useCompanyCreditManagementStore(),
            loading: false,
            invoiceItemTypes: [],
            showEvents: false,
        }
    },
    computed: {
        totalIssuable() {
            return Math.max(0, this.invoiceStore.subTotal - this.invoiceStore.creditsAppliedToInvoice);
        },
        actionable() {
            return !!(this.invoiceStore.isEditable || (this.invoiceStore.refunding && this.invoiceStore.refundEditable));
        }
    },
    mounted() {
        this.invoiceItemTypes.push(...Object.values(this.invoiceStore.itemTypes))
    },
    methods: {
        handleUpdate(val, index){
            this.invoiceStore.invoiceItems[index] = val
        },
        inRefundList(item) {
            return !!this.invoiceStore.refundItems.find(function (refundItem) {
                return refundItem.invoice_item_id === item.invoice_item_id
            })
        }
    },
    created() {
        this.showEvents = this.invoiceStore.isEditable ?? false
    }
}
</script>
