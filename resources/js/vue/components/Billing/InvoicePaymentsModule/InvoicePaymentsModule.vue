<template>
    <div
        class="px-5 py-4 flex flex-col"
    >
        <div class="flex justify-between mb-3">
            <div class="text-lg font-bold ">

                <svg class="inline mr-1 w-4" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15 2.2567C16.5202 3.13439 17.7826 4.39678 18.6603 5.91698C19.538 7.43719 20 9.16164 20 10.917C20 12.6724 19.5379 14.3968 18.6602 15.917C17.7825 17.4372 16.5201 18.6996 14.9999 19.5773C13.4797 20.4549 11.7552 20.917 9.99984 20.917C8.24446 20.9169 6.52002 20.4548 4.99984 19.5771C3.47965 18.6994 2.21729 17.437 1.33963 15.9168C0.46198 14.3965 -4.45897e-05 12.6721 3.22765e-09 10.9167L0.00500012 10.5927C0.0610032 8.86569 0.563548 7.18266 1.46364 5.70769C2.36373 4.23273 3.63065 3.01615 5.14089 2.17658C6.65113 1.33701 8.35315 0.903098 10.081 0.917139C11.8089 0.93118 13.5036 1.3927 15 2.2567ZM10 4.9167C9.73478 4.9167 9.48043 5.02206 9.29289 5.2096C9.10536 5.39713 9 5.65149 9 5.9167C8.20435 5.9167 7.44129 6.23277 6.87868 6.79538C6.31607 7.35799 6 8.12105 6 8.9167C6 9.71235 6.31607 10.4754 6.87868 11.038C7.44129 11.6006 8.20435 11.9167 9 11.9167V13.9167C8.83416 13.9259 8.66858 13.8944 8.51766 13.8251C8.36673 13.7557 8.23503 13.6505 8.134 13.5187L8.066 13.4177C7.92918 13.1971 7.71191 13.0383 7.46014 12.9749C7.20836 12.9116 6.94182 12.9486 6.71686 13.0782C6.49191 13.2079 6.32619 13.4199 6.25474 13.6695C6.18329 13.9191 6.21171 14.1867 6.334 14.4157C6.58924 14.8584 6.95331 15.2285 7.3917 15.491C7.8301 15.7535 8.32827 15.8997 8.839 15.9157H9C8.99979 16.1608 9.0896 16.3975 9.25238 16.5807C9.41517 16.7639 9.63958 16.881 9.883 16.9097L10 16.9167C10.2652 16.9167 10.5196 16.8113 10.7071 16.6238C10.8946 16.4363 11 16.1819 11 15.9167L11.176 15.9117C11.9556 15.8666 12.6869 15.5194 13.2145 14.9437C13.7422 14.368 14.0245 13.6092 14.0016 12.8287C13.9787 12.0481 13.6523 11.3072 13.0918 10.7635C12.5313 10.2197 11.7809 9.91595 11 9.9167V7.9167C11.358 7.9047 11.671 8.0567 11.866 8.3147L11.934 8.4157C12.0708 8.63635 12.2881 8.79514 12.5399 8.85849C12.7916 8.92184 13.0582 8.88479 13.2831 8.75517C13.5081 8.62555 13.6738 8.41352 13.7453 8.16392C13.8167 7.91433 13.7883 7.64672 13.666 7.4177C13.4109 6.97485 13.0469 6.60452 12.6085 6.34183C12.1701 6.07914 11.6718 5.9328 11.161 5.9167H11C11 5.65149 10.8946 5.39713 10.7071 5.2096C10.5196 5.02206 10.2652 4.9167 10 4.9167ZM11 11.9167C11.2652 11.9167 11.5196 12.0221 11.7071 12.2096C11.8946 12.3971 12 12.6515 12 12.9167C12 13.1819 11.8946 13.4363 11.7071 13.6238C11.5196 13.8113 11.2652 13.9167 11 13.9167V11.9167ZM9 7.9167V9.9167C8.73478 9.9167 8.48043 9.81134 8.29289 9.62381C8.10536 9.43627 8 9.18192 8 8.9167C8 8.65149 8.10536 8.39713 8.29289 8.2096C8.48043 8.02206 8.73478 7.9167 9 7.9167Z" fill="#0081FF"/>
                </svg>

                Payments
            </div>
            <CustomButton color="primary" @click="handleShowIssuePaymentForm">
                {{ showIssuePaymentForm ? 'Cancel' : 'New Payment' }}
            </CustomButton>
        </div>
        <request-invoice-charge-form
            v-if="showIssuePaymentForm"
            class="mb-10"
            :dark-mode="darkMode"
            :invoice-id="invoiceId"
            :company-id="companyId"
            @refresh="handleRefresh"
        />
        <div>
            <div v-if="!invoicePaymentsStore.loading">
                <invoice-payments-table
                    :dark-mode="darkMode"
                    :invoice-payments="invoicePaymentsStore.invoicePayments"
                    @refresh="handleRefresh"
                >
                </invoice-payments-table>
                <div class="flex uppercase text-sm font-semibold mt-4">
                    <div class="mr-2 text-slate-500">
                        Total Value:
                    </div>
                    <div class="font-bold">
                        {{ $filters.centsToFormattedDollars(invoicePaymentsStore.totalPaid) }}
                    </div>
                </div>
            </div>
            <div v-else>
                <loading-spinner/>
            </div>
        </div>
    </div>
</template>
<script>
import CustomButton from "../../Shared/components/CustomButton.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import InvoiceItem from "../components/CreateNewInvoice/InvoiceItem.vue";
import {useInvoicePaymentsStore} from "../../../../stores/invoice/invoice-payments-store.js";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import InvoicePaymentStatusBadge from "./InvoicePaymentStatusBadge.vue";
import InvoicePaymentsTable from "./InvoicePaymentsTable.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import {useInvoiceModalStore} from "../../../../stores/invoice/invoice-modal.store.js";
import ApplyCreditToInvoiceModal from "../credits/ApplyCreditToInvoiceForm.vue";
import RequestInvoiceChargeForm from "./RequestInvoiceChargeForm.vue";

export default {
    name: 'InvoicePaymentsModule',
    components: {
        RequestInvoiceChargeForm,
        ApplyCreditToInvoiceModal,
        LoadingSpinner,
        InvoicePaymentsTable,
        InvoicePaymentStatusBadge, InvoiceItem, SimpleIcon, Dropdown, CustomInput, CustomButton
    },
    props: {
        invoiceId: {
            type: Number,
            required: true
        },
        companyId: {
            type: Number,
            required: true
        },
        darkMode: {
            type: Boolean,
            required: false
        }
    },
    data: function () {
        return {
            invoicePaymentsStore: useInvoicePaymentsStore(),
            invoiceStore: useInvoiceModalStore(),
            showDetails: false,
            showIssuePaymentForm: false,
        }
    },
    mounted() {
        this.getPayments()
    },
    unmounted() {
        this.invoicePaymentsStore.$reset();
    },
    methods: {
        useSimpleIcon,
        getPayments() {
            this.invoicePaymentsStore.getInvoicePayments(this.invoiceId)
        },
        handleRefresh() {
            this.getPayments()
            this.invoiceStore.retrieveInvoiceData(this.invoiceId)
        },
        handleShowIssuePaymentForm() {
            this.showIssuePaymentForm = !this.showIssuePaymentForm

            if (!this.showIssuePaymentForm) {
                this.selectedCreditType = null
                this.invoiceCreditsStore.invoiceCredit = {}
            }
        }
    },
}
</script>
