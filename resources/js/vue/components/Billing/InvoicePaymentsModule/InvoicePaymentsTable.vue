<template>
    <div>
        <div class="grid grid-cols-8 pb-3 font-bold text-xs text-slate-500 uppercase gap-2">
            <div>Id</div>
            <div class="col-span-2">Payment method</div>
            <div class="col-span-2">Status</div>
            <div class="col-span-2">Amount</div>
            <div>Actions</div>
        </div>
        <div v-if="invoicePayments.length === 0"
             class="flex justify-center items-center py-2 text-xs font-semibold text-slate-500"
        >
            No payments
        </div>
        <div v-else v-for="item in invoicePayments">
            <div class="grid grid-cols-8 text-xs font-semibold items-center py-5 border-t gap-2" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                <div>{{ item.id }}</div>
                <div class="col-span-2">
                    <div v-if="item.payment_method_types?.length > 0" class="flex flex-col gap-1">
                        <payment-method-badge
                            :dark-mode="darkMode"
                            v-for="type in item.payment_method_types"
                            :type="type"
                        ></payment-method-badge>
                    </div>
                    <p v-else>Pending</p>
                </div>
                <div class="col-span-2 flex flex-col gap-1">
                    <div class="flex gap-1">
                        <invoice-payment-status-badge :dark-mode="darkMode" :status="item.status"></invoice-payment-status-badge>
                        <badge :dark-mode="darkMode">
                            {{ item.attempt_number }} / {{ item.max_attempts }}
                        </badge>
                    </div>
                </div>
                <div class="col-span-2">{{ $filters.centsToFormattedDollars(item.amount) }}</div>
                <div class="flex justify-center gap-1">
                    <simple-icon
                        @click="showDetails[item.id] = !showDetails[item.id]"
                        :icon="showDetails[item.id] ? this.simpleIcon.icons.CHEVRON_UP : this.simpleIcon.icons.CHEVRON_DOWN"
                        clickable
                        color="blue"
                        tooltip="Show all attempts"
                    ></simple-icon>
                    <simple-icon
                        v-if="showCancelPaymentButton(item)"
                        @click="handlePaymentScheduleCancel(item.id)"
                        :icon="this.simpleIcon.icons.X_CIRCLE"
                        :clickable="!loadingCancelingPayment"
                        :tooltip="!loadingCancelingPayment ? 'Cancel payment' : 'Loading...'"
                        :color="this.simpleIcon.colors.RED"
                    ></simple-icon>
                </div>
                <div class="col-span-8 flex gap-8 items-center text-slate-500">
                    <div>
                        <author-badge :dark-mode="darkMode" :author="item.author" />
                    </div>
                    <div v-for="displayedDate in displayedDates" class="flex gap-1 items-center">
                        <simple-icon :icon="displayedDate.icon" :tooltip="displayedDate.tooltip"/>
                        <p>{{ item[displayedDate.field] ?? displayedDate.default ?? '----' }}</p>
                    </div>
                </div>
                <div class="col-span-8" v-if="item.error_message">
                    <p class="font-normal" :class="[darkMode ? 'text-rose-400' : 'text-rose-600']">
                        {{item.error_message}}
                    </p>
                </div>
                <div v-if="showDetails[item.id]" class="col-span-8">
                    <InvoicePaymentChargesTable
                        :dark-mode="darkMode"
                        :payment-charges="item.charges"
                        class="pl-5"
                    >
                    </InvoicePaymentChargesTable>
                </div>
            </div>

        </div>
    </div>
</template>
<script>
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import InvoiceItem from "../components/CreateNewInvoice/InvoiceItem.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import InvoicePaymentStatusBadge from "./InvoicePaymentStatusBadge.vue";
import InvoicePaymentChargesTable from "./InvoicePaymentChargesTable.vue";
import PaymentMethodBadge from "./PaymentMethodBadge.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Badge from "../../Shared/components/Badge.vue";
import ApiService from "../../BillingManagement/services/invoice-payments.js";
import {useToastNotificationStore} from "../../../../stores/billing/tost-notification.store.js";
import {ROLES, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import AuthorBadge from "../components/AuthorBadge.vue";

export default {
    name: 'InvoicePaymentsTable',
    components: {
        AuthorBadge,
        LabeledValue,
        Badge,
        LoadingSpinner,
        PaymentMethodBadge,
        InvoicePaymentChargesTable,
        InvoicePaymentStatusBadge,
        InvoiceItem,
        SimpleIcon
    },
    props: {
        invoicePayments: {
            type: Array,
            required: true
        },
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            showDetails: {},
            api: ApiService.make(),
            toastNotificationStore: useToastNotificationStore(),
            loadingCancelingPayment: false,
            rolesPermissions: useRolesPermissions(),
        }
    },
    computed: {
        isFinanceOwner() {
            return this.rolesPermissions.hasRole(ROLES.FINANCE_OWNER)
        },
        simpleIcon() {
            return useSimpleIcon()
        },
        displayedDates() {
            return [
                {tooltip: 'Updated At', field: 'updated_at', icon: this.simpleIcon.icons.UPDATED_AT},
                {tooltip: 'Next Attempt At', field: 'next_attempt_at', icon: this.simpleIcon.icons.CALENDAR},
                {tooltip: 'Charged At', field: 'charged_at', icon: this.simpleIcon.icons.CHARGED_AT},
            ]
        }
    },
    methods: {
        useSimpleIcon,
        showCancelPaymentButton(invoicePayment) {
            return invoicePayment.status === 'rescheduled' && this.isFinanceOwner
        },
        async handlePaymentScheduleCancel(invoicePaymentId) {
            if (this.loadingCancelingPayment) {
                return
            }

            this.loadingCancelingPayment = true;

            try {
                await this.api.cancelInvoicePayment(invoicePaymentId)
                this.$emit('refresh')
            } catch (err) {
                console.error(err)
                this.toastNotificationStore.notifyError(
                    'Failed to cancel payment'
                )
            }

            this.loadingCancelingPayment = false;
        }
    },
}
</script>
