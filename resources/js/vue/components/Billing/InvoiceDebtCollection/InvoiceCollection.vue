<template>
    <div
        class="px-5 py-4 flex flex-col"
    >
        <div class="flex justify-between mb-2">
            <div class="text-lg font-bold ">
                <svg class="inline mr-1 fill-current text-primary-500 w-4" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M2.95312 20H14.9531C15.1311 20 15.3011 19.97 15.4651 19.926L11.7061 16.167C10.8906 16.7099 9.93284 16.9997 8.95312 17C6.19613 17 3.95312 14.757 3.95312 12C3.95312 9.243 6.19613 7 8.95312 7C11.7101 7 13.9531 9.243 13.9531 12C13.9535 12.9798 13.6636 13.9378 13.1201 14.753L16.8791 18.512C16.9231 18.348 16.9531 18.178 16.9531 18V6L10.9531 0H2.95312C2.42269 0 1.91398 0.210714 1.53891 0.585786C1.16384 0.960859 0.953125 1.46957 0.953125 2V18C0.953125 18.5304 1.16384 19.0391 1.53891 19.4142C1.91398 19.7893 2.42269 20 2.95312 20Z"/>
                    <path d="M8.95312 15C10.61 15 11.9531 13.6569 11.9531 12C11.9531 10.3431 10.61 9 8.95312 9C7.29627 9 5.95312 10.3431 5.95312 12C5.95312 13.6569 7.29627 15 8.95312 15Z"/>
                </svg>

                Debt Collection
            </div>
        </div>
        <div
            v-if="invoiceStore.invoiceCollections"
            class="text-xs mt-3"
        >
            <div
                class="grid border-b pb-1 font-semibold text-slate-500 uppercase grid-cols-6" :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
            >
                <div>Sent Date</div>
                <div>User</div>
                <div>Recovery Status</div>
                <div>Amount Collected</div>
                <div>Amount Recovered</div>
                <div class="text-right">Recovery Date</div>
            </div>
            <div class="grid font-medium grid-cols-6 mt-1 py-3 items-center">
                <div>{{invoiceStore.invoiceCollections.sent_date}}</div>
                <div>{{ invoiceStore.invoiceCollections.user_name }}</div>
                <div>
                    <custom-inline-select
                        :options="invoiceCollectionsStore.recoveryStatusOptions.map(e => ({name: e.name, value: e.id}))"
                        v-model="invoiceStore.invoiceCollections.recovery_status"
                        :disabled="!invoiceCollectionsStore.canUpdateInvoiceCollections"
                    >
                    </custom-inline-select>
                </div>
                <div>{{ $filters.centsToFormattedDollars(invoiceStore.invoiceCollections.amount_collected) }}</div>
                <div>
                    <custom-inline-input
                        v-model="invoiceStore.invoiceCollections.amount_recovered_in_dollars"
                        prefix="$"
                        :disabled="!invoiceCollectionsStore.canUpdateInvoiceCollections"
                        @update:modelValue="() => invoiceStore.invoiceCollections.amount_recovered = invoiceStore.invoiceCollections.amount_recovered_in_dollars * 100"
                    >
                    </custom-inline-input>
                </div>
                <div class="text-right">{{ invoiceStore.invoiceCollections.recovery_date }}</div>
            </div>
            <div class="flex justify-end">
                <custom-button v-if="invoiceCollectionsStore.canUpdateInvoiceCollections && !invoiceStore.hasPendingAction" @click="updateInvoiceCollectionsData" :disabled="invoiceCollectionsStore.saving">
                    Update
                </custom-button>
            </div>
        </div>
        <div v-else class="flex flex-col gap-2 mb-2 justify-between">
            <invoice-debt-collection-form
                :dark-mode="darkMode"
                :total-outstanding="invoiceStore?.invoiceTotals?.outstanding"
                :invoice-id="invoiceStore.invoiceId"
                @created="handleInvoiceCollectionsCreated"
            />
        </div>
    </div>
</template>
<script>
import CustomButton from "../../Shared/components/CustomButton.vue";
import {useInvoiceModalStore} from "../../../../stores/invoice/invoice-modal.store.js";
import CustomInlineInput from "../../Shared/components/CustomInlineInput.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import InvoiceItem from "../components/CreateNewInvoice/InvoiceItem.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import DatePicker from "@vuepic/vue-datepicker";
import InvoiceDebtCollectionForm from "./InvoiceDebtCollectionForm.vue";
import {useInvoiceCollectionsStore} from "../../../../stores/invoice/invoice-collections-store.js";
import CustomInlineSelect from "../../Shared/components/CustomInlineSelect.vue";
const simpleIcon = useSimpleIcon()

export default {
    name: "InvoiceCollection",
    components: {
        CustomInlineSelect,
        InvoiceDebtCollectionForm,
        DatePicker, Dropdown, CustomInput, InvoiceItem, SimpleIcon, CustomInlineInput, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            simpleIcon,
            invoiceStore: useInvoiceModalStore(),
            invoiceCollectionsStore: useInvoiceCollectionsStore(),
            refundReason: null,
        }
    },
    methods: {
        handleInvoiceCollectionsCreated(){
            this.invoiceStore.retrieveInvoiceData(this.invoiceStore.invoiceId)
        },

        updateInvoiceCollectionsData(){
            this.invoiceCollectionsStore.updateInvoiceCollectionsData(
                this.invoiceStore.invoiceId,
                {
                    amountRecovered: this.invoiceStore.invoiceCollections.amount_recovered,
                    recoveryStatus: this.invoiceStore.invoiceCollections.recovery_status,
                }
            )
        }
    }
}
</script>
