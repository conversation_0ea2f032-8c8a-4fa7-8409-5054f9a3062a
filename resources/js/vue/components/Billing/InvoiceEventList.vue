<template>
    <div class="flex flex-col p-5">
        <div @click="showEvents = !showEvents" class="group flex justify-between gap-2 items-center cursor-pointer">
            <div class="text-lg font-bold group-hover:text-primary-500">
                Activity Log
                <span class="text-slate-500 text-sm">(
                <loading-spinner v-if="loading" size="w-3 h-3" wrapper-style="inline-flex" margin="m-0"></loading-spinner>
                <span class="text-primary-500" v-else>{{filteredEvents.length}}</span>
                )</span>
            </div>
            <simple-icon
                :class="{showEvents}"
                :dark-mode="darkMode"
                :icon="simpleIcon.icons.CHEVRON_DOWN"
                :color="simpleIcon.colors.BLUE"
                :size="simpleIcon.sizes.MD"
                :style="showEvents ? 'transition duration-150 rotate-180' : 'transition duration-150'"
            />
        </div>
        <ul v-if="showEvents" role="list" class="space-y-1 mt-2 overflow-y-auto max-h-64 pr-6">
            <li v-if="filteredEvents.length > 0" v-for="(event, eventIdx) in filteredEvents" class="relative flex gap-x-4 items-center">
                <event :dark-mode="darkMode" :is-last="eventIdx === filteredEvents.length - 1">
                    <base-invoice-event
                        :dark-mode="darkMode"
                        :event-data="event"
                    >
                    </base-invoice-event>
                </event>
            </li>
        </ul>
    </div>
</template>
<script>
import Api from "./services/api";
import Event from "./components/EventList/Event.vue";
import BaseInvoiceEvent from "./components/EventList/BaseInvoiceEvent.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
const simpleIcon = useSimpleIcon()

export default {
    name: "InvoiceEventList",
    components: {LoadingSpinner, SimpleIcon, BaseInvoiceEvent, Event},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        invoiceId: {
            type: Number,
            required: true,
        }
    },
    data() {
        return {
            simpleIcon,
            api: Api.make(),
            loading: false,
            events: [],
            showEvents: true,
        }
    },
    created() {
        this.getInvoiceEvents()
    },
    computed: {
        filteredEvents() {
            // Filter out Leads from the list as requested from Billing Management team
            return this.events.filter(
                e => e?.payload?.item_details?.billableType !== "product"
            );
        }
    },
    methods: {
        async getInvoiceEvents() {
            this.loading = true;
            try {
                const response = await this.api.getInvoiceEvents(this.invoiceId)

                this.events = response.data.data

            } catch (e) {
                console.error(e)
            }
            this.loading = false;
        }
    }
}
</script>
