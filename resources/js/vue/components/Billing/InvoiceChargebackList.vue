<template>
    <div
        class="px-5 py-4 flex flex-col"
    >
        <div class="flex justify-between mb-2">
            <div class="text-lg font-bold ">

                <svg class="inline mr-1 w-4" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 13.574V2.42601C20 0.579013 18.4 -0.588987 17.097 0.308013L11 4.76801V3.12301C11 1.50701 9.53302 0.485013 8.33902 1.27001L0.920023 6.14701C-0.307977 6.95401 -0.307977 9.04601 0.920023 9.85301L8.33802 14.73C9.53202 15.515 10.999 14.493 10.999 12.877V11.232L17.096 15.692C18.398 16.589 20 15.422 20 13.574Z" fill="#0081FF"/>
                </svg>
                Chargebacks
            </div>
        </div>
        <div class="grid grid-cols-6 border-b pb-1 font-semibold text-xs text-slate-500 uppercase" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
            <div>Id</div>
            <div>Reason</div>
            <div>Status</div>
            <div>Amount</div>
            <div>Created at</div>
            <div>Updated at</div>
        </div>
        <div v-if="invoiceChargebackStore.invoiceChargebacks.length === 0"
             class="flex justify-center items-center py-2 text-xs font-semibold text-slate-500"
        >
            No chargebacks
        </div>
        <div v-else v-for="item in invoiceChargebackStore.invoiceChargebacks"
             class="grid grid-cols-6 text-xs font-semibold border-b items-center py-4" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
            <div>{{ item.id }}</div>
            <div>{{ item.reason }}</div>
            <div>{{ item.status }}</div>
            <div>{{ item.amount }}</div>
            <div>{{ item.created_at }}</div>
            <div>{{ item.updated_at }}</div>
        </div>
        <div class="flex uppercase text-sm font-semibold mt-2">
            <div class="mr-2 text-slate-500">
                Total Value:
            </div>
            <div>
                ${{ invoiceChargebackStore.invoiceChargebacksAggregate.total_chargeback / 100 }}
            </div>
        </div>
    </div>
</template>
<script>
import {useInvoiceChargebackStore} from "../../../stores/invoice/invoice-chargeback.js";
import CustomButton from "../Shared/components/CustomButton.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import InvoiceItem from "./components/CreateNewInvoice/InvoiceItem.vue";

export default {
    name: 'InvoiceChargebackList',
    components: {InvoiceItem, SimpleIcon, Dropdown, CustomInput, CustomButton},
    props: {
        invoiceId: {
            type: Number,
            required: true
        },
        darkMode: {
            type: Boolean,
            required: false
        }
    },
    data() {
        return {
            invoiceChargebackStore: useInvoiceChargebackStore()
        }
    },
    mounted() {
        this.invoiceChargebackStore.getInvoiceChargebacks(this.invoiceId)
    }
}
</script>
