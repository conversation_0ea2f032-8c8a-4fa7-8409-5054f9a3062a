<template>
    <div
        class="border-b p-5"
        :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
    >
        <div class="grid 2xl:grid-cols-2 gap-6">
            <div class="flex flex-col">
                <div class="grid gap-3 text-xs items-center">
                    <div v-if="disabledCompany || !invoiceStore.isEditable" class="text-xl font-bold">
                        <entity-hyperlink
                            type="company"
                            :dark-mode="darkMode"
                            :entity-id="invoiceStore.company"
                            :suffix="invoiceStore.companyName"
                            delimiter=" "
                            :prefix="invoiceStore.company"
                        />
                        <span class="text-sm font-semibold leading-tight">Invoice #{{invoiceStore.invoiceId}}</span>
                    </div>
                    <div v-else class="grid xl:grid-cols-2 gap-3 items-center">
                        <div>
                            <div class="text-slate-500 uppercase text-xs font-semibold mb-2">Company</div>
                            <autocomplete
                                :dark-mode="darkMode"
                                v-model="invoiceStore.company"
                                :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}"
                                :options="companies"
                                :placeholder="invoiceStore.companyName ?? 'Company name'"
                                create-user-input-option
                                @update:modelValue="handleCompanySelected"
                                @search="searchCompanies('companyname', $event)">
                            </autocomplete>
                        </div>

                    </div>
                    <div class="grid grid-cols-2 gap-6">
                        <div>
                            <div class="text-slate-500 uppercase text-xs font-semibold mb-2">Status</div>
                            <div class="flex gap-1 items-center">
                                <invoice-status-badge :dark-mode="darkMode" :status="invoiceStore.status?.id" />
                                <div v-if="futureInvoiceData?.status" class="font-medium text-sm">to</div>
                                <invoice-status-badge :dark-mode="darkMode" v-if="futureInvoiceData?.status" :status="futureInvoiceData.status"/>
                            </div>
                        </div>
                        <div>
                            <div v-if="invoiceStore?.billingProfile?.payment_method" class="text-slate-500 uppercase text-xs font-semibold mb-2">Payment Method</div>
                            <div v-if="invoiceStore?.billingProfile?.payment_method" class="flex gap-1 items-center">
                                <payment-method-badge :dark-mode="darkMode" :type="invoiceStore?.billingProfile?.payment_method" />
                            </div>
                        </div>
                        <div v-if="invoiceStore.invoiceTransactionStatuses?.length > 0">
                            <div class="text-slate-500 uppercase text-xs font-semibold mb-2">Traits</div>
                            <div class="flex gap-1 items-center flex-wrap" v-if="invoiceStore.invoiceTransactionStatuses?.length > 0">
                                <DisplayInvoiceTransactionStatuses :dark-mode="darkMode" :transaction-statuses="invoiceStore.invoiceTransactionStatuses"/>
                            </div>
                        </div>
                        <div v-if="defaultBillingProfile?.id">
                            <div class="uppercase text-slate-500">Billing Profile</div>
                            <payment-method-badge
                                v-if="defaultBillingProfile?.id"
                                :type="defaultBillingProfile.payment_method"
                                :reference="defaultBillingProfile.payment_method_number ? `${defaultBillingProfile.payment_method_number} ${defaultBillingProfile.payment_method_expiry_month}/${defaultBillingProfile.payment_method_expiry_year}` : null"
                            />
                        </div>
                    </div>
                    <div class="grid xl:grid-cols-2 gap-6 mt-4">
                        <div>
                            <div class="text-slate-500 uppercase text-xs font-semibold mb-2">Issue Date</div>
                            <Datepicker
                                :disabled="!invoiceStore.isEditable"
                                :model-value="invoiceStore.issueDate"
                                @update:model-value="(date) => {invoiceStore.issueDate = date}"
                                :dark="darkMode"
                                :format="(date) => $filters.dateFromTimestamp(date,'MMMM D, YYYY')"
                                timezone="America/Denver"
                            />
                        </div>
                        <div>
                            <div class="text-slate-500 uppercase text-xs font-semibold mb-2">Due Date</div>
                            <Datepicker
                                :disabled="!invoiceStore.isEditable"
                                :model-value="invoiceStore.dueDate"
                                @update:model-value="(date) => {invoiceStore.dueDate = date}"
                                :dark="darkMode"
                                :format="(date) => $filters.dateFromTimestamp(date,'MMMM D, YYYY')"
                                timezone="America/Denver"
                            />
                        </div>
                    </div>

                </div>
            </div>

            <div class="flex flex-col text-xs">
                <div class="flex flex-col">
<!--                    <div class="flex gap-2">-->
<!--                        <div class="flex flex-col">-->
<!--                            <div class="text-slate-500 mb-4">Process Automatically</div>-->
<!--                            <toggle-switch :disabled="!invoiceStore.isEditable" @click="invoiceStore.processAuto = !invoiceStore.processAuto" :model-value="invoiceStore.processAuto" class="flex justify-end" :dark-mode="darkMode"></toggle-switch>-->
<!--                        </div>-->
<!--                        <div class="flex flex-col">-->
<!--                            <div class=" text-slate-500 mb-4">Prepayment</div>-->
<!--                            <toggle-switch :disabled="!invoiceStore.isEditable" @click="invoiceStore.prepayment = !invoiceStore.prepayment" :model-value="invoiceStore.prepayment" class="flex justify-end" :dark-mode="darkMode"></toggle-switch>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="grid gap-4">
                        <div v-if="invoiceStore.invoiceTotals">
                            <p class="font-semibold text-lg mb-2">Balance</p>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div class="flex gap-1" v-for="(value, key) in invoiceStore.nonEmptyInvoiceTotals">
                                    <badge :dark-mode="darkMode" class="w-full" :color="invoiceBalanceColor[key]">
                                        <div class="flex flex-col gap-1 p-2 text-base font-semibold">
                                            <p class="capitalize">{{ toTitleCase(key) }}</p>
                                            <p>{{ $filters.centsToFormattedDollars(value) }}</p>
                                        </div>
                                    </badge>
                                </div>
                            </div>
                        </div>
                        <div v-if="creditStore.balances?.length > 0">
                            <p class="font-semibold text-lg mb-2">Credits</p>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div class="flex gap-1" v-for="creditType in creditStore.balances">
                                    <badge class="w-full" color="green">
                                        <div class="flex flex-col gap-1 p-2 text-base">
                                            <p class="text-slate-500 capitalize">{{ toTitleCase(creditType.name) }}</p>
                                            <p>{{ $filters.centsToFormattedDollars(creditType.balance) }}</p>
                                        </div>
                                    </badge>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>

import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import {useInvoiceModalStore} from "../../../../stores/invoice/invoice-modal.store.js";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import SharedApiService from "../../Shared/services/api.js";
import Badge from "../../Shared/components/Badge.vue";
import {useCompanyCreditManagementStore} from "../../../../stores/credit/company-credit-management.store.js";
import useInvoiceHelper from "../../../../composables/useInvoiceHelper.js";
import InvoiceStatusBadge from "../../BillingManagement/components/InvoiceStatusBadge.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import DisplayInvoiceTransactionStatuses from "./components/DisplayInvoiceTransactionStatuses.vue";
import PaymentMethodBadge from "../InvoicePaymentsModule/PaymentMethodBadge.vue";
import EntityHyperlink from "../../BillingManagement/components/EntityHyperlink.vue";
import {toTitleCase} from "../../../../modules/helpers.js";
import {DateTime} from "luxon";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
const invoiceHelper = useInvoiceHelper()

export default {
    name: "CreateInvoiceModalContentCompanyData",
    components: {
        LoadingSpinner,
        EntityHyperlink,
        PaymentMethodBadge,
        DisplayInvoiceTransactionStatuses,
        SimpleIcon, InvoiceStatusBadge, Badge, Autocomplete, Dropdown, ToggleSwitch},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: [Number, String],
            default: null,
        },
        companyName: {
            type: String,
            default: null,
        },
        futureInvoiceData: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            simpleIcon: useSimpleIcon(),
            invoiceHelper,
            sharedApi: SharedApiService.make(),
            invoiceStore: useInvoiceModalStore(),
            creditStore: useCompanyCreditManagementStore(),
            companies: [],
            disabledCompany: false,
            defaultBillingProfile: {}
        }
    },
    mounted() {
        if (this.companyId && this.companyName) {
            this.invoiceStore.setCompanyDetails(this.companyId, this.companyName)
            this.disabledCompany = true;
        }
    },
    beforeUnmount() {
        this.creditStore.$reset()
        this.invoiceStore.$reset()
    },
    methods: {
        async loadCompanyPaymentData(companyId){
            await this.getCompanyDefaultBillingProfile(companyId)
            await this.creditStore.getBalances(companyId, this.defaultBillingProfile.id);
        },
        toTitleCase,
        async searchCompanies(nameType, query) {
            this.sharedApi.searchCompanyNamesAndId(query).then(res => {
                if (res.data.data.status === true) {
                    this.companies = res.data.data.companies;
                }
            })
        },
        async getCompanyDefaultBillingProfile(companyId) {
            if (!this.invoiceStore.invoiceId) {
                const response = await this.sharedApi.getCompanyDefaultBillingProfile(companyId)
                this.defaultBillingProfile = response.data.data.default_billing_profile

                this.invoiceStore.issueDate = DateTime.now().setZone('America/Denver').plus({days: 0}).toJSDate()
                this.invoiceStore.dueDate = DateTime.now().setZone('America/Denver').plus({days: this.defaultBillingProfile.due_in_days}).toJSDate()
            }
        },

        async handleCompanySelected(companyId) {
            if (typeof companyId === 'number') {
                this.loading = true;

                this.invoiceStore.companyChanged();

                await Promise.all([
                    this.invoiceStore.getUninvoicedProducts(),
                    this.loadCompanyPaymentData(companyId)
                ])

                this.loading = false;
            }
        },
    },
    watch: {
        async 'invoiceStore.companyName'() {
            if (this.invoiceStore.companyName !== "") {
                this.companies = [{
                    id: this.invoiceStore.company,
                    name: `${this.invoiceStore.company}: ${this.invoiceStore.companyName}`
                }];
                await this.loadCompanyPaymentData(this.invoiceStore.company);
                if (this.invoiceStore.isEditable) {
                    await this.invoiceStore.getUninvoicedProducts()
                }
            }
        }
    },
    computed: {
        invoiceBalanceColor(){
            return {
                'paid': 'green',
                'outstanding': 'amber',
                'credits_applied': 'purple'
            }
        }
    }
}
</script>

<style scoped>
.dp__theme_dark {
    --dp-background-color: rgb(13, 26, 38);
    --dp-hover-color: #484848;
    --dp-hover-text-color: #4D87C1;
    --dp-hover-icon-color: #959595;
    --dp-primary-color: #005cb2;
    --dp-primary-text-color: #4D87C1;
    --dp-secondary-color: #a9a9a9;
    --dp-border-color: rgb(0, 55, 95);
    --dp-menu-border-color: #2d2d2d;
    --dp-border-color-hover: #418FDB;
    --dp-disabled-color: rgb(20 34 47);
    --dp-scroll-bar-background: #212121;
    --dp-scroll-bar-color: #484848;
    --dp-success-color: #00701a;
    --dp-success-color-disabled: #428f59;
    --dp-icon-color: rgb(0, 129, 255);
    --dp-danger-color: #e53935;
}



.dp__theme_light {
    --dp-background-color: rgb(249, 249, 249);
    --dp-hover-color: #ECECEC;
    --dp-hover-text-color: #212121;
    --dp-hover-icon-color: #1976d2;
    --dp-border-color: rgb(202, 207, 211);
    --dp-icon-color: rgb(0, 129, 255);
}
</style>
