<template>
    <div
        class="p-5 flex flex-col"
    >
        <div @click="show = !show" class="group flex justify-between gap-2 items-center cursor-pointer">
            <div class="text-lg font-bold group-hover:text-primary-500">
                Transactions
            </div>
            <simple-icon v-if="hasItems" :icon="showItemsIcon"  clickable color="blue" size="md"/>
        </div>
        <div v-if="show" class="grid grid-cols-3 border-b pb-1 font-semibold text-xs text-slate-500 uppercase mt-5" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
            <div>Type</div>
            <div>Amount</div>
            <div>Created at</div>
        </div>
        <loading-spinner v-if="invoiceTransactionStore.loading" />
        <div v-else-if="!hasItems"
             class="flex justify-center items-center py-2 text-xs font-semibold text-slate-500"
        >
            No Items Added
        </div>
        <div v-else-if="show"
             v-for="item in invoiceTransactionStore.transactions"
             class="grid grid-cols-3 text-xs font-semibold border-b items-center py-4 ease-in" :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
        >
            <div>{{item.transaction_type.name}}</div>
            <div>{{ $filters.centsToFormattedDollars(item.amount) }}</div>
            <div>{{ item.transaction_date }}</div>
        </div>
    </div>
</template>
<script>
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import {useInvoiceTransactionStore} from "../../../stores/invoice/invoice-transaction.js";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";

export default {
    name: 'InvoiceTransactionList',
    components: {LoadingSpinner, SimpleIcon},
    props: {
        invoiceId: {
            type: Number,
            required: true
        },
        darkMode: {
            type: Boolean,
            required: false
        }
    },
    data() {
        return {
            invoiceTransactionStore: useInvoiceTransactionStore(),
            simpleIcon: useSimpleIcon(),
            show: false
        }
    },
    computed: {
        showItemsIcon(){
            return this.show ? this.simpleIcon.icons.CHEVRON_UP : this.simpleIcon.icons.CHEVRON_DOWN;
        },

        hasItems(){
            return this.invoiceTransactionStore.transactions.length > 0
        }
    },
    mounted() {
        this.invoiceTransactionStore.getInvoiceTransactions(this.invoiceId)
    }
}
</script>
