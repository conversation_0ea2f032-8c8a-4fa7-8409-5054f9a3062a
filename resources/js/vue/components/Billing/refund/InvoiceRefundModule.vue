<template>
    <div
        class="px-5 py-4 flex flex-col"
    >
        <simple-alert
            v-if="showAlert"
            variant="light-red"
            :dark-mode="darkMode"
            :content="errorHandler?.message"
            dismissible
            @dismiss="showAlert = false"
        />
        <div class="flex mb-2 gap-2">
            <div class="text-lg font-bold">
                <svg class="inline mr-1 w-4" viewBox="0 0 11 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M2.9873 0.136841C2.39057 0.136841 1.81827 0.373894 1.39631 0.795851C0.974358 1.21781 0.737305 1.7901 0.737305 2.38684V11.3868C0.737191 11.5244 0.774907 11.6593 0.846327 11.7769C0.917747 11.8944 1.02012 11.9901 1.14225 12.0534C1.26438 12.1167 1.40156 12.1451 1.53878 12.1357C1.67601 12.1262 1.80799 12.0793 1.92031 11.9998L3.6123 10.8048L5.30431 11.9998C5.43094 12.0894 5.58221 12.1375 5.73731 12.1375C5.8924 12.1375 6.04367 12.0894 6.1703 11.9998L7.86231 10.8048L9.5553 11.9998C9.66761 12.079 9.7995 12.1257 9.93657 12.135C10.0736 12.1443 10.2106 12.1158 10.3326 12.0525C10.4546 11.9893 10.5568 11.8938 10.6282 11.7764C10.6995 11.659 10.7373 11.5242 10.7373 11.3868V2.38684C10.7373 1.7901 10.5003 1.21781 10.0783 0.795851C9.65634 0.373894 9.08404 0.136841 8.48731 0.136841H2.9873ZM6.0173 3.41684C6.14978 3.27467 6.22191 3.08662 6.21848 2.89232C6.21505 2.69802 6.13634 2.51263 5.99893 2.37522C5.86151 2.23781 5.67613 2.15909 5.48183 2.15567C5.28753 2.15224 5.09948 2.22436 4.9573 2.35684L2.7073 4.60684C2.56685 4.74747 2.48796 4.93809 2.48796 5.13684C2.48796 5.33559 2.56685 5.52622 2.7073 5.66684L4.9573 7.91684C5.09948 8.04932 5.28753 8.12144 5.48183 8.11802C5.67613 8.11459 5.86151 8.03588 5.99893 7.89846C6.13634 7.76105 6.21505 7.57566 6.21848 7.38136C6.22191 7.18706 6.14978 6.99902 6.0173 6.85684L5.04731 5.88684H6.36231C7.12231 5.88684 7.73731 6.50284 7.73731 7.26184C7.73731 7.46075 7.81632 7.65152 7.95698 7.79217C8.09763 7.93282 8.28839 8.01184 8.48731 8.01184C8.68622 8.01184 8.87698 7.93282 9.01763 7.79217C9.15829 7.65152 9.2373 7.46075 9.2373 7.26184C9.2373 6.49934 8.9344 5.76808 8.39524 5.22891C7.85607 4.68974 7.1248 4.38684 6.36231 4.38684H5.04831L6.0173 3.41684Z" fill="#0081FF"/>
                </svg>
                Refunds
            </div>
            <custom-button
                class="ml-auto"
                v-show="canCreateNewRefund"
                @click="toggleRefund"
                :dark-mode="darkMode"
            >
                {{ creatingRefund ? 'Cancel Refund' : 'Create Refund'}}
            </custom-button>
        </div>
        <div v-if="creatingRefund">
            <div class="font-semibold mb-2"> New Refund </div>
            <div
                class="grid border-b pb-1 font-semibold text-xs text-slate-500 uppercase grid-cols-9"
            >
                <div class="col-span-4">
                    Description
                </div>
                <div>
                    Quantity
                </div>
                <div>
                    Item Price
                </div>
                <div>
                    Added By
                </div>
                <div>
                    Amount
                </div>
            </div>
            <div class="flex flex-col gap-1 mb-2" v-for="(item) in invoiceRefundStore.refundableItems">
                <invoice-item
                    :dark-mode="darkMode"
                    :model-value="item"
                    disabled
                    actionable
                >
                    <template v-slot:actions>
                        <div class="flex justify-end items-center">
                            <simple-icon
                                :dark-mode="darkMode"
                                :icon="getInvoiceItemRefundActionIcon(item)"
                                clickable
                                @click="toggleRefundItem(item)"
                            />
                        </div>
                    </template>
                </invoice-item>
            </div>
            <div class="flex gap-2 mb-2 justify-between">
                <custom-input
                    placeholder="Enter a custom amount."
                    type="number"
                    v-model="invoiceRefundStore.newRefundPayload.custom_amount_refunded_in_dollars"
                    @update:modelValue="() => invoiceRefundStore.newRefundPayload.custom_amount_refunded = invoiceRefundStore.newRefundPayload.custom_amount_refunded_in_dollars * 100"
                >
                </custom-input>
                <div class="flex">
                    <dropdown
                        :dark-mode="darkMode"
                        placeholder="Select Reason"
                        :options="REFUND_REASON_OPTIONS"
                        v-model="invoiceRefundStore.newRefundPayload.refund_reason"
                    />
                </div>
                <div class="flex uppercase text-sm font-semibold">
                    <div class="mr-2">
                        Refund Value:
                    </div>
                    <div class="text-red-500">
                        {{$filters.centsToFormattedDollars(refundValue)}} / {{$filters.centsToFormattedDollars(invoiceRefundStore.invoiceAggregates.paid)}}
                    </div>
                </div>
                <custom-button
                    @click="handleToggleAll"
                    :dark-mode="darkMode"
                >
                    Toggle All
                </custom-button>
                <custom-button
                    @click="handleExecuteRefund"
                    :disabled="!refundEligible"
                    :dark-mode="darkMode"
                >
                    Execute Refund
                </custom-button>
            </div>
        </div>
        <div v-for="refund in invoiceRefundStore.invoiceRefunds">
            <div class="font-semibold mb-2 flex items-center gap-2">
                <span>{{refund.refunded_at}} - Refund #{{refund.id}} ({{refund.reason}})</span>
                <a
                    v-if="refund.related_lead_refund_id"
                    :href="`/lead-refunds?lead_refund_id=${refund.related_lead_refund_id}`"
                    target="_blank"
                    class="text-primary-500 hover:text-primary-400"
                >
                    View lead refund
                </a>
            </div>
            <div
                v-if="refund.items.length > 0"
                class="grid border-b pb-1 font-semibold text-xs text-slate-500 uppercase grid-cols-8"
            >
                <div class="col-span-4">
                    Description
                </div>
                <div>
                    Quantity
                </div>
                <div>
                    Item Price
                </div>
                <div>
                    Added By
                </div>
                <div class="flex justify-end">
                    Amount
                </div>
            </div>
            <div class="flex flex-col gap-1 mb-2" v-for="(item) in refund.items">
                <invoice-item
                    :dark-mode="darkMode"
                    :model-value="item"
                    disabled
                />
            </div>
            <div v-if="refund.custom_amount > 0" class="flex gap-2 mb-2 justify-between">
                <labeled-value
                    label="Custom Refund Amount"
                    orientation="horizontal"
                >
                    {{$filters.centsToFormattedDollars(refund.custom_amount)}}
                </labeled-value>
            </div>
        </div>
    </div>
</template>
<script>
import {useInvoiceRefundStore} from "../../../../stores/invoice/invoice-refund.store.js";
import CustomButton from "../../Shared/components/CustomButton.vue";
import InvoiceItem from "../components/CreateNewInvoice/InvoiceItem.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import useErrorHandler from "../../../../composables/useErrorHandler.js";
import SimpleAlert from "../../Shared/components/SimpleAlert.vue";
const simpleIcon = useSimpleIcon()
const REFUND_REASON_OPTIONS = [
    {name: "Duplicate", id: 'duplicate'},
    {name: "Fraudulent", id: 'fraudulent'},
    {name: "Customer Request", id: 'requested_by_customer'},
    {name: "Other", id: 'other'},
]

export default {
    name: "InvoiceRefundModule",
    components: {SimpleAlert, LabeledValue, CustomInput, Dropdown, SimpleIcon, InvoiceItem, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        invoiceId: {
            type: Number,
            required: true,
        },
        editable: {
            type: Boolean,
            default: true,
        }
    },
    data(){
        return {
            invoiceRefundStore: useInvoiceRefundStore(),
            creatingRefund: false,
            simpleIcon,
            errorHandler: useErrorHandler(),
            showAlert: false,
            REFUND_REASON_OPTIONS,
            loading: false,
        }
    },
    created() {
        this.getRefunds();
    },
    unmounted() {
        this.invoiceRefundStore.$reset();
    },
    computed: {
        canCreateNewRefund() {
            return this.editable && this.invoiceRefundStore.invoiceAggregates.paid > 0 // TODO - Permissions
        },
        refundValue() {
            const customAmountRefunded = Number(this.invoiceRefundStore.newRefundPayload.custom_amount_refunded);

            const totalItemsValue = this.invoiceRefundStore.newRefundPayload.items.reduce((previousValue, currentValue) => {
                return previousValue + (currentValue.unit_price * currentValue.quantity);
            }, 0);

            const totalRefund = customAmountRefunded + totalItemsValue;

            return totalRefund.toFixed(2);
        },
        refundEligible() {
            const hasReason = this.invoiceRefundStore.newRefundPayload.refund_reason !== null;

            const hasCustomAmountOrLead = this.invoiceRefundStore.newRefundPayload.items.length > 0
                || this.invoiceRefundStore.newRefundPayload.custom_amount_refunded > 0;

            const refundValueIsLessThanOrEqualTotalPaid = this.refundValue <= this.invoiceRefundStore.invoiceAggregates.paid

            return hasReason && hasCustomAmountOrLead && refundValueIsLessThanOrEqualTotalPaid
        }
    },
    methods: {
        async getRefunds() {
            this.loading = true;
            await this.invoiceRefundStore.getRefunds(this.invoiceId)
            this.loading = false;
        },
        toggleRefund() {
            this.creatingRefund = !this.creatingRefund
        },
        toggleRefundItem(item) {
            this.invoiceRefundStore.toggleItemRefunded(item)
        },
        getInvoiceItemRefundActionIcon(item){
           return this.invoiceRefundStore.isSelectedForRefund(item.invoice_item_id) ? simpleIcon.icons.X_MARK : simpleIcon.icons.CHECK
        },
        handleToggleAll(){
            const hasItems = this.invoiceRefundStore.newRefundPayload.items.length > 0;

            this.invoiceRefundStore.newRefundPayload.items = hasItems ? [] : [
                ...this.invoiceRefundStore.refundableItems
            ]
        },
        async handleExecuteRefund() {
            this.loading = true;
            try {
                const resp = await this.invoiceRefundStore.issueRefund(this.invoiceId);
                if (resp.data.data.status === true) {
                    this.invoiceRefundStore.$reset();
                    this.creatingRefund = false;
                    await this.getRefunds(this.invoiceId);
                }
            } catch (err) {
                this.errorHandler.handleError(err, 'Validation error')

                console.error(err)
                this.showAlert = true;
                setTimeout(() => {
                    this.showAlert = false;
                }, 4000)
            }
            this.loading = false;
        }
    }
}
</script>
