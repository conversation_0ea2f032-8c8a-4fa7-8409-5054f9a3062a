<script setup>
import BaseTable from "../../Shared/components/BaseTable.vue";
import {onMounted, ref} from "vue";
import DeleteCompanyButton from "../../Companies/components/Delete/DeleteCompanyButton.vue";

const props = defineProps({
    company: {},
    companyId: null,
    darkMode: false,
    loading: false,
    margin: {
        type: String,
        default: "mb-6",
    }
})

const scopedLoading = ref(false);
const scopedCompany = ref(null);

onMounted(() => {
    if(props.companyId){
        fetchCompany(props.companyId)
    }else{
        scopedCompany.value = props.company;
    }
})

const fetchCompany = (id) => {
    scopedLoading.value = true;
    axios.get('/internal-api/v1/companies/' + id + '/check-for-duplicates').then(response => {
        scopedCompany.value = response.data.data.company;
    }).finally(() => scopedLoading.value = false)
}

</script>

<template>
    <div>
        <div class="rounded-lg border" :class="[margin, darkMode ? 'bg-dark-module border-dark-border text-slate-50' : 'bg-light-module border-light-border text-slate-900']">
            <h3 class="font-bold text-sm uppercase text-primary-500 p-5">
                Possible Duplicates
            </h3>
            <BaseTable
                :dark-mode="darkMode"
                :loading="loading || scopedLoading"
                body-height="h-64">
                <template #head>
                    <tr>
                        <th>
                            Company
                        </th>
                    </tr>
                </template>
                <template #body>
                    <tr v-if="scopedCompany?.duplicate_companies" v-for="match in scopedCompany?.duplicate_companies" :key="match.company_name">
                        <td>
                            <div class="grid grid-cols-6">
                                <div class="col-span-5 flex flex-col">
                                    <a :href="match.profile_link" target="_blank" class="text-sm cursor-pointer inline-flex items-center hover:text-primary-500 font-medium truncate">
                                        {{match.company_name}}
                                        <svg class="w-3.5 ml-2" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 0.833344L13.293 4.12634L6.293 11.1263L7.707 12.5403L14.707 5.54034L18 8.83334V0.833344H10Z" fill="#0081FF"/><path d="M16 16.8333H2V2.83334H9L7 0.833344H2C0.897 0.833344 0 1.73034 0 2.83334V16.8333C0 17.9363 0.897 18.8333 2 18.8333H16C17.103 18.8333 18 17.9363 18 16.8333V11.8333L16 9.83334V16.8333Z" fill="#0081FF"/></svg>
                                    </a>
                                    <small>{{match.website}}</small>
                                </div>
                                <delete-company-button :dark-mode="darkMode" :company-id="match.company_id"
                                                       :deletable="match.deletable" :queued="match.queued" @finished="fetchCompany(props.companyId)"/>
                            </div>
                        </td>
                    </tr>
                    <span class="flex items-center h-full justify-center text-slate-500" v-else>No Duplicates Detected</span>
                </template>
            </BaseTable>
        </div>
    </div>
</template>

<style scoped>

</style>
