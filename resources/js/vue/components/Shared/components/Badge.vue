<template>
    <div class="inline-flex items-center" :class="[
                color === 'blue' ? (darkMode ? 'bg-dark-border/50 text-blue-400' : 'bg-blue-100/75 text-blue-900') : '',
                color === 'cyan' ? (darkMode ? 'bg-dark-border/50 text-cyan-400' : 'bg-cyan-200/75 text-cyan-900') : '',
                color === 'green' ? (darkMode ? 'bg-dark-border/50 text-emerald-400' : 'bg-emerald-100/75 text-emerald-900') : '',
                color === 'red' ? (darkMode ? 'bg-dark-border/50 text-rose-400' : 'bg-rose-100/75 text-rose-900') : '',
                color === 'orange' ? (darkMode ? 'bg-dark-border/50 text-orange-400' : 'bg-orange-100/75 text-orange-900') : '',
                color === 'amber' ? (darkMode ? 'bg-dark-border/50 text-amber-400' : 'bg-amber-100/75 text-amber-900') : '',
                color === 'purple' ? (darkMode ? 'bg-dark-border/50 text-purple-400' : 'bg-purple-100/75 text-purple-900') : '',
                color === 'indigo' ? (darkMode ? 'bg-dark-border/50 text-indigo-400' : 'bg-indigo-100/75 text-indigo-900') : '',
                color === 'gray' ? (darkMode ? 'bg-dark-border/50 text-gray-300' : 'bg-gray-100/75 text-gray-900') : '',
                color === 'primary' ? (darkMode ? 'bg-dark-border/50 text-primary-500' : 'bg-primary-100/75 text-primary-900') : '',
                badgeStyle,
                ]">
        <slot></slot>
    </div>
</template>

<script>
export default {
    name: "Badge",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        color: {
            type: String,
            default: 'blue'
        },
        wrapStyle: {
            type: String,
            default: 'default'
        }
    },
    computed: {
        badgeStyle() {
            const styles = {
                default: 'py-1 px-2 rounded-md inline-flex font-bold text-xs',
                rounded: 'px-4 text-xs inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap'
            }

            return styles[this.wrapStyle] ?? styles.default
        }
    }
}
</script>
