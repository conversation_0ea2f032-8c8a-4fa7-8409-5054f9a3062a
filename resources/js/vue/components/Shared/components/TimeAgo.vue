<template>
  <span
      class="relative border-b border-dotted border-gray-400 hover:text-blue-600 transition-colors duration-200"
      :class="[
          darkMode ? 'text-white' : 'text-black'
      ]"
      @mouseover="showTooltip = true"
      @mouseleave="showTooltip = false"
  >
    {{ timeAgoFormatted }}
    <div v-if="showTooltip"
         class="absolute left-full transform -translate-x-1/2 bg-gray-700 text-white px-3 py-2 rounded text-sm whitespace-nowrap z-50 mb-1 shadow-lg"
         :class="positionClass"
    >
      {{ fullDateFormatted }}
    </div>
  </span>
</template>

<script>
import {DateTime} from 'luxon'

export default {
    name: 'TimeAgo',
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        verticalAlign: {
            type: String,
            default: 'top'
        },
        date: {
            type: String,
            required: true,
            validator(value) {
                return DateTime.fromISO(value).isValid
            }
        },
        timezone: {
            type: String,
            default: 'America/Denver'
        },
        updateInterval: {
            type: Number,
            default: 60000
        }
    },
    data() {
        return {
            showTooltip: false,
            now: DateTime.now(),
            intervalId: null
        }
    },
    computed: {
        positionClass() {
            const options = {
                top: 'bottom-full',
                bottom: 'top-full'
            }

            return options[this.verticalAlign] ?? options.top
        },
        parsedDate() {
            return DateTime.fromISO(this.date).setZone(this.timezone)
        },
        timeAgoFormatted() {
            const diff = this.now.diff(this.parsedDate)
            const duration = diff.shiftTo('years', 'months', 'days', 'hours', 'minutes', 'seconds')

            if (duration.years >= 1) {
                const years = Math.floor(duration.years)
                return `${years} year${years !== 1 ? 's' : ''} ago`
            }

            if (duration.months >= 1) {
                const months = Math.floor(duration.months)
                return `${months} month${months !== 1 ? 's' : ''} ago`
            }

            if (duration.days >= 1) {
                const days = Math.floor(duration.days)
                return `${days} day${days !== 1 ? 's' : ''} ago`
            }

            if (duration.hours >= 1) {
                const hours = Math.floor(duration.hours)
                return `${hours} hour${hours !== 1 ? 's' : ''} ago`
            }

            if (duration.minutes >= 1) {
                const minutes = Math.floor(duration.minutes)
                return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`
            }

            return 'just now'
        },
        fullDateFormatted() {
            return this.parsedDate.toFormat('EEEE, MMMM dd, yyyy \'at\' h:mm:ss a ZZZZ')
        }
    },
    mounted() {
        // Update the current time periodically to keep relative time accurate
        this.intervalId = setInterval(() => {
            this.now = DateTime.now()
        }, this.updateInterval)
    },
    beforeDestroy() {
        if (this.intervalId) {
            clearInterval(this.intervalId)
        }
    },
    watch: {
        timezone() {
            // Force reactivity when timezone changes
            this.now = DateTime.now()
        }
    }
}
</script>
