<template>
    <div
        v-click-outside="handleClickOutside"
        class="flex p-1 gap-1 items-center relative flex-wrap rounded text-sm font-medium w-full cursor-text"
        :class="[
            !clean ? darkMode ? 'text-slate-100 bg-dark-background border-dark-border' : 'text-slate-900 bg-light-background border-light-border' : '',
            !clean ? 'border focus:outline-none outline-none focus:ring-0 focus:border-primary-500 focus:shadow-lg focus:shadow-primary-500/10' : ''
        ]"
        @click="handleWrapClick"
    >
        <div
            v-for="(value, index) in modelValue"
            :key="index"
            class="inline-flex items-center px-2 py-1 text-sm font-medium rounded-lg whitespace-nowrap h-8" :class="[darkMode ? 'bg-dark-border text-slate-100' : 'bg-light-border text-slate-900']"
        >
            <!--                dark:bg-blue-900 dark:text-blue-300-->
            <div class="flex gap-1">
                <slot v-if="$slots['selected-tag']" name="selected-tag" :value="value"></slot>
                <p v-else>{{searchableFields.reduce((prev, f) => prev + ' ' + value[f], '')}}</p>
            </div>
            <button
                v-if="!disabled"
                type="button"
                @click="removeTag(index)"
                class="inline-flex items-center p-1 ml-2 text-sm text-blue-400 bg-transparent rounded-sm hover:bg-blue-200 hover:text-blue-900"
                data-dismiss-target="#badge-dismiss-default"
                aria-label="Remove"
            >
                <svg class="w-2 h-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                </svg>
                <span class="sr-only">Remove</span>
            </button>
        </div>
        <div class="flex-1 relative">
            <input
                ref="input"
                class="w-full h-8 bg-transparent appearance-none border-none outline-none focus:outline-none focus:ring-0 focus:border-none p-2"
                type="text"
                v-model="searchText"
                autocomplete="off"
                :placeholder="placeholder"
                @input="handleInput"
                @keydown="handleShortcodeDown"
                :disabled="disabled"
            />
            <div class="absolute z-50 top-7 w-full" v-if="showList">
                <div
                    :class="{'border-blue-700 bg-dark-background text-white': darkMode, 'bg-light-module border-gray-300 text-gray-800': !darkMode}"
                    class="overflow-auto rounded-lg shadow-md z-10 py-2 border text-xs absolute top-2">
                    <ul class="list-reset overflow-y-scroll max-h-56 relative">
                        <li v-if="loading">
                            <span class="block px-4 py-1 text-gray-500 text-nowrap whitespace-nowrap">Loading...</span>
                        </li>
                        <li v-else-if="filteredResults.length === 0 && !allowNew">
                            <span class="block px-4 py-1 text-gray-500 text-nowrap whitespace-nowrap">No results</span>
                        </li>
                        <li
                            v-if="allowNew && searchText?.length > 0"
                            ref="op-new-user"
                            class="px-4 py-1 flex no-underline hover:no-underline text-nowrap whitespace-nowrap hover:bg-gray-100 text-black cursor-pointer"
                            @click.prevent="handleAddNew()"
                        >
                            <div class="flex gap-1">
                                <p class="font-semibold">
                                    Click to add: {{ searchText }}
                                </p>
                            </div>
                        </li>
                        <li
                            v-for="(value, index) in filteredResults"
                            :ref="`op-${index}`"
                            class="px-4 py-1 flex no-underline hover:no-underline text-nowrap whitespace-nowrap hover:bg-primary-500 cursor-pointer"
                            :class="selectIndex === index ? 'hover:text-white bg-primary-500 text-white' : 'hover:bg-gray-100 text-black'"
                            @click.prevent="handleSelect(value)"
                        >
                            <div class="flex gap-1">
                                <slot v-if="$slots['filtered-result-option']" name="filtered-result-option" :value="value"></slot>
                                <p v-else>{{searchableFields.reduce((prev, f) => prev + ' ' + value[f], '')}}</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</template>
<script>

import useArrayHelper from "../../../../composables/useArrayHelper";

const arrayHelper = useArrayHelper();

const KEYS = {
    ESC: 27,
    BACKSPACE: 8,
    ENTER: 13,
    ARROW_UP: 38,
    ARROW_DOWN: 40,
    TAB: 9,
};

export default {
    name: 'TagInput',

    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        modelValue: {
            type: Array,
            required: true
        },
        clean: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: 'Enter name, email, or ID'
        },
        allowNew: {
            type: Boolean,
            default: false
        },
        options: {
            type: Array,
            required: false,
            default: []
        },
        searchableFields: {
            type: Array,
            default: []
        },
        optionFactoryCallback: {
            type: Function,
            required: false
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },

    data(){
        return {
            localOptions: [],
            selectIndex: 0,
            searchText: "",
            showList: false,
            loading: false,
        }
    },

    created(){
        this.assignOptions(this.options)
    },

    computed: {
        filteredResults(){
            let rx;

            try {
                const cleanSearchString = this.searchText.trim()
                rx = new RegExp(`^.*${cleanSearchString}.*$`, 'gi')
            } catch {}

            return this.localOptions.filter(option => {
                const isSelected = this.modelValue.find(id => typeof option === 'object' ? id === option.id : id === option)
                if (isSelected) return false

                if (!rx) return true

                if (this.searchableFields.length > 0 && typeof option === 'object') {
                    return this.searchableFields.some(prop => option[prop] && option[prop].toString().toLowerCase().match(rx))
                }

                if (typeof option === 'string') {
                    return option.toLowerCase().match(rx)
                }

                return false
            });
        }
    },

    emits: ['update:modelValue', 'input'],

    watch: {
        options(options){
            if (options?.length > 0) {
                this.assignOptions(options)
            }
        }
    },

    methods: {
        assignOptions(options){
            this.localOptions = arrayHelper.removeDuplicatesByFields([
                ...(this.modelValue ?? []),
                ...(options ?? [])
            ], this.searchableFields)
        },
        handleClickOutside(){
          this.resetSelect()
        },

        resetSelect(){
            this.searchText = ''
            this.showList = false;
            this.selectIndex = 0;
        },

        handleSelect(value){
            if (!arrayHelper.checkElementExistsInArrayByFields(this.modelValue, value, this.searchableFields)) {
                this.$emit('update:modelValue', [...this.modelValue, value]);
            }

            this.handleWrapClick();
            this.resetSelect();
        },

        handleShortcodeDown(key){
             const keyCode = key.keyCode;

            if (!Object.values(KEYS).includes(keyCode)) {
                this.showList = true;
                this.selectIndex = 0;
                this.scrollIntoView()
            } else {
                if (keyCode === KEYS.BACKSPACE) {
                    return this.handleBackspace();
                } else if (keyCode === KEYS.ESC) {
                    return this.handleEsc();
                }

                if (!this.showList) return;

                key.preventDefault();
                this.handleArrowKeys(keyCode);
            }
        },

        handleBackspace() {
            if (this.searchText.length === 0) {
                this.removeTag();
                if (this.searchText.length <= 0) {
                    this.resetSelect();
                }
            }
        },

        handleEsc() {
            this.showList = false;
        },

        handleArrowKeys(keyCode) {
            if (keyCode === KEYS.ARROW_UP && this.selectIndex - 1 >= 0) {
                this.selectIndex--;
                this.scrollIntoView();
            } else if (keyCode === KEYS.ARROW_DOWN && this.selectIndex + 1 < this.filteredResults.length) {
                this.selectIndex++;
                this.scrollIntoView();
            } else if ([KEYS.ENTER].includes(keyCode) && this.filteredResults.length > 0) {
                this.handleSelect(this.filteredResults[this.selectIndex]);
                this.selectIndex = 0;
                this.searchText = '';
            } else if ([KEYS.ENTER].includes(keyCode) && this.filteredResults.length === 0 && this.searchText.length >0) {
                this.handleAddNew()
                this.selectIndex = 0;
            }
        },

        scrollIntoView() {
            if (!this.$refs[`op-${this.selectIndex}`] || this.filteredResults.length === 0) return;

            const [ref] = this.$refs[`op-${this.selectIndex}`]

            if (!ref) return;

            ref.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        },

        handleWrapClick(){
            this.$refs.input.focus();
        },
        removeTag(itemIndex = null){
            if (itemIndex === null) {
                this.modelValue.pop()
            } else {
                this.$emit('update:modelValue', this.modelValue.filter((item, idx) => idx !== itemIndex));
            }

            this.resetSelect();
        },

        handleAddNew(){
            if (this.searchText.length === 0) return;

            const newOption = this.optionFactoryCallback
                ? this.optionFactoryCallback(this.searchText)
                : this.formatNewOption();

            if (!arrayHelper.checkElementExistsInArrayByFields(this.localOptions, newOption, this.searchableFields)) {
                this.localOptions = [
                    newOption,
                    ...this.options
                ]
            }

            this.handleSelect(newOption);
        },
        formatNewOption() {
            const [firstOption] = this.options

            if (typeof firstOption === 'string' || !firstOption) {
                return this.searchText
            }

            return Object.keys(firstOption).reduce((prev, curr) => Object.assign({[curr]: this.searchText}, prev), {})
        },

        handleInput(e){
            this.$emit('input', e.target.value)
            this.showList = true
        }
    },
};
</script>
<style scoped>
</style>
