<template>
    <div class="hidden">
        <label for="tabs" class="sr-only">Select a tab</label>
        <select id="tabs" name="tabs"
                class="block w-full focus:ring-indigo-500 focus:border-indigo-500 border-gray-300 rounded-lg">
            <option v-for="tab in tabs" :key="tab.name" :selected="tab.current">{{ tab.name }}</option>
        </select>
    </div>
    <div class="flex flex-nowrap border-b overflow-x-auto"
         :class="[darkMode ? 'border-dark-border bg-dark-module' : `border-gray-200 ${colorStyle}`, wrapperClasses]">
        <nav v-if="tabType === 'Badges'" class="relative z-0 flex cursor-pointer" :class="tabsClasses"
             aria-label="Tabs">
            <a v-for="(tab, tabIdx) in tabs" :key="tab.name" @click="tabSelected(tab)"
               v-if="!(tab?.hide === true)"
               :class="[!darkMode ? (tab.current ? 'text-blue-550' : 'text-gray-500 hover:text-gray-700')  : (tab.current ? 'text-blue-550' : 'text-gray-400 hover:text-gray-300'), !darkMode ? 'bg-light-module' : 'bg-dark-module', 'group flex-1 relative min-w-0 overflow-hidden py-4 px-4 text-sm font-medium text-center focus:z-10']"
               :aria-current="tab.current ? 'page' : undefined">
                <span>{{ tab.name }}</span>
                <span v-if="!loading && showTotal"
                      :class="[!darkMode ? (tab.current ? 'bg-cyan-100 text-blue-550' : 'bg-gray-100 text-gray-900') : (tab.current ? 'bg-dark-175 text-blue-550' : 'bg-dark-background text-grey-400'), 'hidden ml-3 py-0.5 px-2.5 rounded-full text-xs font-medium md:inline-block']">{{
                        safeGetTotal(tab.name)
                    }}</span>
                <span v-if="loading && showTotal"
                      :class="[!darkMode ? (tab.current ? 'bg-cyan-100 text-cyan-100' : 'bg-gray-100 text-gray-100') : (tab.current ? 'bg-dark-175 text-dark-175' : 'bg-dark-background text-dark-40'), 'hidden ml-3 py-0.5 px-2.5 rounded-full text-xs font-medium md:inline-block']">0</span>
                <span aria-hidden="true"
                      :class="[tab.current ? 'bg-primary-500' : 'bg-transparent', 'absolute inset-x-0 transform transition-all duration-300 bottom-0 h-0.5']"/>
            </a>
        </nav>
        <nav v-else-if="tabType === 'Normal'" class="relative z-0 flex cursor-pointer" :class="tabsClasses"
             aria-label="Tabs">
            <a v-for="(tab, tabIdx) in tabs" :key="tab.name" @click="tabSelected(tab)" class="block whitespace-nowrap"
               :class="[!darkMode ? (tab.current ? 'text-blue-550' : 'text-gray-500 hover:text-gray-700')  : (tab.current ? 'text-blue-550' : 'text-gray-400 hover:text-gray-300'), !darkMode ? 'bg-light-module' : 'bg-dark-module', innerTabStyle]"
               :aria-current="tab.current ? 'page' : undefined">
                <span :class="tab.current ? 'font-semibold' : 'font-medium'">{{ tab.name }}</span>
                <span aria-hidden="true"
                      :class="[tab.current ? 'bg-primary-500' : 'bg-transparent', 'absolute inset-x-0 transform transition-all duration-300 bottom-0 h-0.5']"/>
            </a>
        </nav>
        <nav v-else-if="tabType === 'Advanced Badges'" class="relative z-0 flex cursor-pointer" :class="tabsClasses"
             aria-label="Tabs">
            <a v-for="(tab, tabIdx) in tabs" :key="tab.name" @click="tabSelected(tab)"
               :class="[!darkMode ? (tab.current ? 'text-blue-550' : 'text-gray-500 hover:text-gray-700')  : (tab.current ? 'text-blue-550' : 'text-gray-400 hover:text-gray-300'), !darkMode ? 'bg-light-module' : 'bg-dark-module', 'flex-1 relative whitespace-nowrap pb-4 pt-7 px-4 text-sm font-medium text-center focus:z-10']"
               :aria-current="tab.current ? 'page' : undefined">
                <span>{{ tab.name }}</span>
                <span class="absolute top-2 inset-x-0" v-if="tab.redCount || tab.greenCount || tab.yellowCount">
                    <span v-if="!loading">
                        <span v-if="tab.redCount" :class="[darkMode ? 'text-rose-400 bg-dark-background' : 'text-rose-700 bg-rose-100', 'group leading-4 hidden ml-1 px-2 rounded-full text-[11px] font-bold md:inline-block relative']">
                            {{tab.redCount}}
<!--                            TODO: Uncomment these when we want to add hover state. -->
<!--                            <span :class="[darkMode ? 'text-rose-400 bg-dark-background' : 'text-rose-700 bg-rose-100']" class="absolute top-0 left-0 group-hover:visible invisible leading-4 hidden px-2 rounded-full text-[11px] font-bold md:inline-block z-30">-->
<!--                                {{tab.redCount}} Overdue-->
<!--                            </span>-->
                        </span>
                        <span v-if="tab.greenCount" :class="[darkMode ? 'text-emerald-400 bg-dark-background' : 'text-emerald-700 bg-emerald-100', 'group leading-4 hidden ml-1 px-2 rounded-full text-[11px] font-bold md:inline-block relative']">
                            {{tab.greenCount}}
<!--                            TODO: Uncomment these when we want to add hover state. -->
<!--                            <span :class="[darkMode ? 'text-emerald-400 bg-dark-background' : 'text-emerald-700 bg-emerald-100']" class="absolute top-0 left-0 group-hover:visible invisible leading-4 hidden px-2 rounded-full text-[11px] font-bold md:inline-block z-30">-->
<!--                                {{tab.greenCount}} Overdue-->
<!--                            </span>-->
                        </span>
                        <span v-if="tab.yellowCount" :class="[darkMode ? 'text-amber-400 bg-dark-background' : 'text-amber-700 bg-amber-100', 'group leading-4 hidden ml-1 px-2 rounded-full text-[11px] font-bold md:inline-block relative']">
                            {{tab.yellowCount}}
<!--                            TODO: Uncomment these when we want to add hover state. -->
<!--                            <span :class="[darkMode ? 'text-amber-400 bg-dark-background' : 'text-amber-700 bg-amber-100']" class="absolute top-0 left-0 group-hover:visible invisible leading-4 hidden px-2 rounded-full text-[11px] font-bold md:inline-block z-30">-->
<!--                                {{tab.yellowCount}} Overdue-->
<!--                            </span>-->
                        </span>
                    </span>
                    <span v-else :class="[!darkMode ? (tab.current ? 'bg-cyan-100 text-cyan-100' : 'bg-gray-100 text-gray-100') : (tab.current ? 'bg-dark-175 text-dark-175' : 'bg-dark-background text-dark-40'), 'hidden ml-3 py-0.5 px-2.5 rounded-full text-xs font-medium md:inline-block']">0</span>
                </span>
                <span aria-hidden="true" :class="[tab.current ? 'bg-primary-500' : 'bg-transparent', 'absolute inset-x-0 transform transition-all duration-300 bottom-0 h-0.5']"/>
            </a>
        </nav>
        <slot name="extra-tab"></slot>
    </div>
</template>

<script>/**
 * @typedef tab
 * @property {string} name
 * @property {boolean} current
 */

import {slugify} from "../services/strings.js";

export default {
    name: "Tab",
    props: {
        tabs: {
            default: null,
            validator: (o) => {
                if (!_.isArray(o)) {
                    return false;
                }

                o.forEach(function (i) {
                    if (!i.hasOwnProperty('current')) {
                        return false;
                    }

                    if (!i.hasOwnProperty('name')) {
                        return false;
                    }

                    if (!i.hasOwnProperty('redCount')) {
                        return false;
                    }

                    if (!i.hasOwnProperty('greenCount')) {
                        return false;
                    }

                    if (!i.hasOwnProperty('yellowCount')) {
                        return false;
                    }
                })

                return true;
            }
        },
        tabType: {
            type: String,
            default: 'Badges',
        },
        darkMode: {
            type: Boolean,
            default: false
        },
        total: {
            type: Object,
            default: ({})
        },
        showTotal: {
            type: Boolean,
            default: true
        },
        loading: {
            type: Boolean,
            default: false
        },
        tabsClasses: {
            type: String,
            default: ''
        },
        bgClasses: {
            type: String,
            default: ''
        },
        defaultTabIndex: {
            type: Number,
            default: 0
        },
        backgroundColor: {
            type: String,
            default: 'gray'
        },
        tabStyle: {
            type: String,
            default: 'default'
        },
        wrapperClasses: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            lastSelectedTab: this.tabs[this.defaultTabIndex]
        }
    },
    emits: ['selected'],
    methods: {
        tabSelected(tab) {

            if (!tab.hasOwnProperty('current')) {
                throw new Error(`tab missing "current" property.`)
            }

            if (!_.isBoolean(tab.current)) {
                throw new Error(`"tab.current" must be a boolean value. ${tab.current} passed.`)
            }

            if (!tab.hasOwnProperty('name')) {
                throw new Error(`tab missing "name" property.`)
            }

            this.lastSelectedTab.current = false;
            tab.current = !tab.current;
            this.lastSelectedTab = tab;
            this.$emit('selected', tab.name);
        },

        safeGetTotal(property){
            if (!this.total)
                return

            const lowercaseTotals = Object.entries(this.total).reduce((prev, [key, val]) => Object.assign({
                [slugify(key, '_')]: val,
                [key]: val,
            }, prev))

            return lowercaseTotals[slugify(property, '_')] ?? 0
        }
    },

    computed: {
        colorStyle(){
            return {
                'gray': 'bg-gray-50',
                'light': 'bg-light-module',
            }[this.backgroundColor] ?? this.backgroundColor
        },
        innerTabStyle() {
            return {
                'default': 'group flex-1 relative min-w-0 overflow-hidden py-4 px-4 text-sm font-medium text-center focus:z-10',
                'fit': 'group relative min-w-0 overflow-hidden py-4 px-4 text-sm font-medium text-center focus:z-10',
            }[this.tabStyle] ?? this.tabStyle
        }
    }
}
</script>

<style scoped>

</style>
