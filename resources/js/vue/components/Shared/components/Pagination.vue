<template>
    <div v-if="showPagination"
         :class="darkMode ? 'bg-dark-module border-dark-border' : 'border-light-border'"
         class="flex items-center justify-between">
        <div class="flex-1 flex justify-between sm:hidden">
            <div v-if="showPreviousButton" @click="handleChangePage(paginationData.prev_page_url)"
                 :class="darkMode ? 'text-grey-200 bg-dark-background hover:bg-primary-800 border-slate-700' : 'text-gray-700 bg-light-module hover:bg-gray-50 border-gray-300'"
                 class="relative inline-flex items-center px-4 py-2 border-y text-sm font-medium rounded-lg">
                Previous
            </div>
            <div v-if="showNextButton" @click="handleChangePage(paginationData.next_page_url)"
                 :class="darkMode ? 'text-grey-200 bg-dark-background hover:bg-primary-800 border-slate-700' : 'text-gray-700 bg-light-module hover:bg-gray-50 border-gray-300'"
                 class="ml-3 relative inline-flex items-center px-4 py-2 border-y text-sm font-medium rounded-lg">
                Next
            </div>
        </div>

        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p v-if="showTotalRecordsDetail" :class="darkMode ? 'text-grey-200' : 'text-slate-500'" class="text-sm mr-3">
                    Showing
                    <span class="font-medium">{{ paginationData.from }}</span>
                    to
                    <span class="font-medium">{{ paginationData.to }}</span>
                    of
                    <span class="font-medium">{{ paginationData.total }}</span>
                    results
                </p>
                <p v-else :class="darkMode ? 'text-grey-200' : 'text-grey-800'" class="text-sm">
                    Total: <span class="font-medium">{{ paginationData.total }}</span>
                </p>
            </div>

            <div class="ml-auto flex items-center">
                <div v-if="showResultsPerPage" class="sm:flex items-center space-x-2">
                    <span :class="darkMode ? 'text-grey-200' : 'text-slate-500'" class="text-sm whitespace-nowrap">{{ perPageLabel }}</span>
                    <Dropdown class="p-2 dropdown-top z-10" v-model="perPage"
                              v-on:update:modelValue="handleChangePage(paginationData.path + '?page=1')"
                              :options="perPageOptions"
                              :dark-mode="darkMode"
                              :selected="paginationData.per_page"/>
                </div>

                <div v-if="showPageSelector && pageOptions.length" class="sm:flex items-center space-x-2">
                    <span :class="darkMode ? 'text-grey-200' : 'text-slate-500'" class="ml-2 text-sm whitespace-nowrap">Go to Page</span>
                    <Dropdown class="p-2 dropdown-top z-10" v-model="paginationData.current_page"
                              v-on:update:modelValue="handleChangePage(paginationData.path + '?page=' + paginationData.current_page)"
                              :options="pageOptions"
                              :dark-mode="darkMode"
                              :selected="paginationData.current_page"/>
                </div>
            </div>

            <div v-if="!truncated">
                <nav class="relative z-0 inline-flex divide-x rounded border overflow-hidden cursor-pointer"
                     :class="darkMode ? 'border-dark-border divide-dark-border' : 'border-light-border divide-light-border'"
                     aria-label="Pagination">
                    <div v-for="(link, index) in paginationData.links"
                         @click="link.url !== null ? handleChangePage(link.url) : null"
                         :class="darkMode ? 'bg-dark-background text-grey-200 hover:bg-primary-800' : 'bg-light-background text-slate-800 hover:bg-primary-100'"
                         class="relative items-center px-4 py-2 text-sm font-medium inline-flex">
                        <p v-html="link.label"
                           :class="{ 'text-primary-500': link.active, 'text-slate-400': link.url === null }"
                           class="pb-0 elect-none"></p>
                    </div>
                </nav>
            </div>

            <div v-else>
                <nav class="relative z-0 cursor-pointer inline-flex divide-x rounded border overflow-hidden" :class="[darkMode ? 'border-dark-border divide-dark-border' : 'border-light-border divide-light-border']" aria-label="Pagination">
                    <div v-if="paginationData.links[0]"
                         @click="paginationData.links[0].url !== null ? handleChangePage(paginationData.links[0].url) : null"
                         class="relative items-center px-4 py-2 text-sm font-medium inline-flex"
                         :class="[paginationData.links[0].url !== null ? (darkMode ? 'hover:bg-primary-800' : 'hover:bg-primary-100') : '',darkMode ? 'bg-dark-background text-grey-200' : 'bg-light-background text-slate-800']">
                        <p class="pb-0 elect-none" :class="{ 'text-primary-500': paginationData.links[0].active, 'text-slate-400': paginationData.links[0].url === null }">Prev</p>
                    </div>
                    <div v-if="paginationData.current_page !== 1"
                         @click="paginationData.links[1].url !== null ? handleChangePage(paginationData.links[1].url) : null"
                         class="relative items-center px-4 py-2 text-sm font-medium inline-flex"
                         :class="[paginationData.links[1].url !== null ? (darkMode ? 'hover:bg-primary-800' : 'hover:bg-primary-100') : '',darkMode ? 'bg-dark-background text-grey-200' : 'bg-light-background text-slate-800']">
                        <p class="pb-0 elect-none">1</p>
                    </div>
                    <div v-if="paginationData.links.length > 3 && (paginationData.current_page !== 1)"
                         class="relative items-center px-2 py-2 text-sm font-medium inline-flex pointer-events-none"
                         :class="[darkMode ? 'bg-dark-background text-grey-200' : 'bg-light-background text-slate-800']">
                        <p class="pb-0 elect-none text-slate-500">...</p>
                    </div>
                    <div v-for="(link, index) in paginationData.links"
                         @click="link.url !== null ? handleChangePage(link.url) : null"
                         class="relative items-center px-4 py-2 text-sm font-medium"
                         :class="[link.active ? 'z-10 text-primary-500' : 'hidden' , link.url !== null ? (darkMode ? 'hover:bg-primary-800' : 'hover:bg-primary-100') : '',darkMode ? 'bg-dark-background text-grey-200' : 'bg-light-background text-slate-800']">
                        <p v-html="link.label" class="pb-0 elect-none" :class="{ 'text-primary-500': link.active, 'text-slate-400': link.url === null }"></p>
                    </div>
                    <div v-if="paginationData.links.length > 3 && (paginationData.links.length - 2 !== paginationData.current_page)"
                         class="relative items-center px-2 py-2 text-sm font-medium inline-flex pointer-events-none"
                         :class="[darkMode ? 'bg-dark-background text-grey-200' : 'bg-light-background text-slate-800']">
                        <p class="pb-0 elect-none text-slate-500">...</p>
                    </div>
                    <div v-if="paginationData.links[paginationData.links.length -2].label !== paginationData.current_page.toString()"
                         @click="paginationData.links[paginationData.links.length -2].url !== null ? handleChangePage(paginationData.links[paginationData.links.length -2].url) : null"
                         class="relative items-center px-4 py-2 text-sm font-medium inline-flex"
                         :class="[paginationData.links[paginationData.links.length -2].url !== null ? (darkMode ? 'hover:bg-primary-800' : 'hover:bg-primary-100') : '',darkMode ? 'bg-dark-background text-grey-200' : 'bg-light-background text-slate-800']">
                        <p v-html="paginationData.links[paginationData.links.length -2].label" class="pb-0 elect-none" :class="{ 'text-primary-500': paginationData.links[paginationData.links.length -2].active, 'text-slate-400': paginationData.links[paginationData.links.length -2].url === null }"></p>
                    </div>
                    <div v-if="paginationData.links[paginationData.links.length -1]"
                         @click="paginationData.links[paginationData.links.length -1].url !== null ? handleChangePage(paginationData.links[paginationData.links.length -1].url) : null"
                         class="relative items-center px-4 py-2 text-sm font-medium inline-flex"
                         :class="[paginationData.links[paginationData.links.length -1].url !== null ? (darkMode ? 'hover:bg-primary-800' : 'hover:bg-primary-100') : '',darkMode ? 'bg-dark-background text-grey-200' : 'bg-light-background text-slate-800']">
                        <p class="pb-0 elect-none" :class="{ 'text-primary-500': paginationData.links[paginationData.links.length -1].active, 'text-slate-400': paginationData.links[paginationData.links.length -1].url === null }">Next</p>
                    </div>
                </nav>
            </div>
        </div>
    </div>
</template>

<script>
import Dropdown from "./Dropdown.vue";
export default {
    components: {Dropdown},
    props: {
        paginationData: {
            type: Object,
            default: {
                current_page: {
                    type: Number,
                    default: null
                },
                from: {
                    type: Number,
                    default: null
                },
                last_page: {
                    type: Number,
                    default: null
                },
                last_page_url: {
                    type: String,
                    default: null
                },
                links: {
                    url: {
                        type: String,
                        default: null
                    },
                    label: {
                        type: String,
                        default: null
                    },
                    active: {
                        type: Boolean,
                        default: null
                    }
                },
                next_page_url: {
                    type: String,
                    default: null
                },
                prev_page_url: {
                    type: String,
                    default: null
                },
                per_page: {
                    type: Number,
                    default: null
                },
                to: {
                    type: Number,
                    default: null
                },
                total: {
                    type: Number,
                    default: null
                }
            }
        },
        showPagination: {
            type: Boolean,
            default: true
        },
        showResultsPerPage: {
            type: Boolean,
            default: false
        },
        darkMode: {
            type: Boolean,
            default: false,
        },
        showTotalRecordsDetail: {
            type: Boolean,
            default: true
        },
        truncated: {
            type: Boolean,
            default: false
        },
        showPageSelector: {
            type: Boolean,
            default: false
        },
        perPageLabel: {
            type: String,
            default: 'Show Rows:'
        },
    },
    data() {
        return {
            perPageOptions: [
                { id: 5, name: 5 },
                { id: 10, name: 10 },
                { id: 25, name: 25 },
                { id: 50, name: 50 },
                { id: 100, name: 100 },
                { id: this.paginationData.total, name: 'All'}
            ],
            perPage: this.paginationData.per_page,
        }
    },
    computed: {
        showPreviousButton() {
            return this.paginationData.current_page !== 1;
        },
        showNextButton() {
            return this.paginationData.current_page !== this.paginationData.last_page;
        },
        pageOptions() {
            if (!this.paginationData.last_page || this.paginationData.last_page < 10) {
                return [];
            }

            return Array.from({length: this.paginationData.last_page}, (_, index) => ({id: index + 1, name: index + 1}));
        }
    },
    methods: {
        handleChangePage: function(linkUrl) {
            if(!linkUrl) {
                return;
            }

            linkUrl += '&perPage=' + this.perPage

            const newPage = Number( (new URLSearchParams(linkUrl.split('?')[1])).get('page') );

            this.$emit('change-page', {
                link: linkUrl,
                perPage: this.perPage,
                newPage: newPage
            });
        },
    }
};
</script>

<style scoped>
.bg-dark-module ::-webkit-scrollbar {
    width: 8px;
}

.bg-dark-module ::-webkit-scrollbar-track {
    background: #2d3748;
}

.bg-dark-module ::-webkit-scrollbar-thumb {
    background-color: #4a5568;
    border-radius: 4px;
    border: 2px solid #2d3748;
}

.bg-dark-module ::-webkit-scrollbar-thumb:hover {
    background-color: #718096;
}
.bg-dark-module * {
    scrollbar-width: thin;
    scrollbar-color: #4a5568 #2d3748; /* Thumb color, track color */
}
</style>
