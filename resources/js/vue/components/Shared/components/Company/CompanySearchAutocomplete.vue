<template>
    <autocomplete
        :model-value="modelValue"
        :value="modelValue"
        :dark-mode="darkMode"
        placeholder="Enter Company Name or ID"
        @search="searchCompany"
        :options="options"
        @update:modelValue="handleModelUpdate"
        :search-icon="searchIcon"
        @input="$emit('input', $event)"
        :loading="loading && enableLoading"
        @clear-search="$emit('clear-search')"
    />
</template>
<script>
import Autocomplete from "../Autocomplete.vue";
import SharedApiService from "../../services/api.js";

export default {
    name: "CompanySearchAutocomplete",
    components: {Autocomplete},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: [Number, String],
            default: 0
        },
        emitValue: {
            type: String,
            default: 'company-id' // 'company-id' or 'company-payload'
        },
        searchIcon: {
            type: Boolean,
            default: false
        },
        enableLoading: {
            type: Boolean,
            default: false
        }
    },
    emits: ['update:modelValue', 'input', 'clear-search'],
    data() {
        return {
            sharedApi: SharedApiService.make(),
            options: [],
            loading: false,
        }
    },
    methods: {
        handleModelUpdate(value){
            if(this.emitValue === 'company-payload'){
                value = this.options.find((option) => option.id === value)
            }
            this.$emit('update:modelValue', value)
        },
        searchCompany(query) {
            this.loading = true;
            this.sharedApi.getAdminCompanies(query, {})
                .then(resp => this.options = resp.data.data.companies)
                .finally(() => this.loading = false);
        },
    }
}
</script>
