<template>
    <div class="flex flex-col border-t rounded-b-lg" :class="{'bg-light-module border-light-border':!darkMode, 'bg-dark-module border-dark-border':darkMode}">
        <div v-if="loading" class="col-span-4 p-4" :class="{'border-light-border':!darkMode, 'border-dark-border':darkMode}">
            <loading-spinner  :dark-mode="darkMode" label="Retrieving credit balance. . ."></loading-spinner>
        </div>
        <div v-else>
            <div class="col-span-4 p-4" :class="{'border-light-border':!darkMode, 'border-dark-border':darkMode}">
                <div class="flex gap-4">
                    <div>
                        <div class="flex gap-2">
                            <div>Total Available Credit</div>
                            <VersionBadge :dark-mode="darkMode"/>
                        </div>
                        <p v-if="errorHandler.message" class="text-red-500">{{errorHandler.message}}</p>
                        <p class="text-lg font-semibold text-green-500">{{ $filters.centsToFormattedDollars(totalBalance) }}</p>
                    </div>
                    <simple-icon
                        v-if="canViewCompanyCredits"
                        :dark-mode="darkMode"
                        :icon="simpleIcon.icons.PENCIL_SQUARE"
                        :color="simpleIcon.colors.BLUE"
                        clickable
                        @click="showCompanyCreditsManagementModal = true"
                    />
                </div>
            </div>
            <div v-if="balances.length > 0" class="flex border-t gap-2 flex-wrap p-2">
                <badge v-for="credit in balances" color="green" :dark-mode="darkMode">
                    <div class="flex">
                        {{credit.name}}: {{ $filters.centsToFormattedDollars(credit.balance) }}
                    </div>
                </badge>
            </div>
        </div>
        <manage-company-credits-modal
            v-if="showCompanyCreditsManagementModal"
            @close="showCompanyCreditsManagementModal = false"
            :dark-mode="darkMode"
            :company-id="companyId"
        />
    </div>
</template>
<script>
import ManageCompanyCreditsModal from "./ManageCompanyCreditsModal.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store";
import Badge from "../../components/Badge.vue";
import LoadingSpinner from "../../components/LoadingSpinner.vue";
import VersionBadge from "../../../Billing/VersionBadge.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import Api from "../../services/company-credit-api.js";
import useErrorHandler from "../../../../../composables/useErrorHandler.js";
const simpleIcon = useSimpleIcon()
export default {
    name: "CompanyCreditsCard",
    components: {SimpleIcon, VersionBadge, LoadingSpinner, Badge, ManageCompanyCreditsModal},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            required: true
        }
    },
    data() {
        return {
            permissionStore: useRolesPermissions(),
            loading: false,
            showCompanyCreditsManagementModal: false,
            simpleIcon,
            companyCreditManagement: Api.make(),
            errorHandler: useErrorHandler(),
            balances: []
        }
    },
    mounted() {
        this.getBalances(this.companyId)
    },
    computed: {
        totalBalance() {
            return this.balances?.reduce((prev, curr) => prev + curr.balance, 0) ?? 0;
        },
        canViewCompanyCredits() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_BILLING_CREDITS_COMPANY_VIEW);
        }
    },
    methods: {
        async getBalances(companyId) {
            this.loading = true;
            try{
                const response = await this.companyCreditManagement.getCreditBalances(companyId);
                if (response.data.data.status) {
                    this.balances = response.data.data.balances;
                }
            } catch (error) {
                this.errorHandler.handleError(error)
            }
            this.loading = false;
        }
    }

}
</script>
