<script setup>

import ActivityMinimalisticModule from "./ActivityMinimalisticModule.vue";
import Activity from "./Activity.vue";
import {ref} from "vue";
import Tab from "../components/Tab.vue";

const props = defineProps({
    darkMode: false,
    companyId: {
        type: Number,
        default: null
    },
    companyName: {
        type: String,
        default: null
    },
    actionCategories: {
        type: Array,
        default: () => []
    }
})

const tabs = ref([
    {name: 'Timeline', current: true},
    {name: 'Notes & Actions', current: false}
])
const selectedTab = ref('Timeline')
const tabIndex = ref(0)

function selectTab(selected) {
    selectedTab.value = selected;
}
</script>

<template>
    <div class="rounded-lg border overflow-hidden" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="flex justify-between px-5 pt-5" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
            <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">
                Activity</h5>
        </div>
        <Tab
            :dark-mode="darkMode"
            :tabs="tabs"
            @selected="selectTab"
            :default-tab-index="tabIndex"
            tab-style="fit"
            background-color="light"
            :tab-type="'Normal'"
        ></Tab>
        <ActivityMinimalisticModule
            v-if="selectedTab === 'Timeline'"
            :company-id="companyId"
            :dark-mode="darkMode">
        </ActivityMinimalisticModule>
        <Activity
            v-if="selectedTab === 'Notes & Actions'"
            :dark-mode="darkMode"
            :company-id="companyId"
            :company-name="companyName"
            show-search
            show-category-filter
            :category-options="actionCategories"
            hide-title
            wrapper-classes=""
        >
        </Activity>
    </div>
</template>

<style scoped>

</style>
