<script setup>
import ActivityTab from "../../Companies/components/activity/ActivityTab.vue";
const props = defineProps({
    darkMode: false,
    companyId: {
        type: Number,
        default: null
    },
    companyName: {
        type: String,
        default: ''
    }
})
</script>

<template>
    <div class="overflow-hidden"
         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">

        <activity-tab
            key="all"
            :dark-mode="darkMode"
            :company-id="companyId"
            :activity-type="null"
            :hide-filters="true"
            :truncated-pagination="true"
        >
            <template v-slot:extra-filters>
                <slot name="extra-filters"></slot>
            </template>
        </activity-tab>
    </div>
</template>

<style scoped>

</style>
