<template>
    <div class="min-w-full" :class="{'text-white': darkMode}">
        <simple-table
            v-if="!invoiceStore.showInvoiceModal"
            :dark-mode="darkMode"
            :title="customTitle"
            :data="data"
            :headers="headers"
            :loading="loading"
            :current-per-page="tableFilter.per_page"
            v-model="tableFilter"
            @update:modelValue="getInvoices"
            :table-filters="tableFilters"
            :pagination-data="paginationData"
            @search="handleSearch"
            @reset="handleReset"
            has-row-click
            @click-row="handleRowClick"
            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
        >
            <template v-if="!companyId" v-slot:visible-filters>
                <company-search-autocomplete v-model="tableFilter.companyId" :dark-mode="darkMode"/>
            </template>
            <template v-if="closeable" v-slot:title-actions>
                <simple-icon
                    clickable
                    @click="$emit('close')"
                    :dark-mode="darkMode"
                    :icon="simpleIcon.icons.X_MARK"
                    :size="simpleIcon.sizes.MD"
                />
            </template>
            <template v-slot:custom-buttons>
                <CustomButton v-if="canCreateInvoices" :dark-mode="darkMode" @click="openInvoiceModal">
                    + Invoice
                </CustomButton>
                <CustomButton
                    v-if="canImportLeads"
                    :dark-mode="darkMode"
                    @click="toggleImportLeads(true)"
                >
                    Import Leads
                </CustomButton>
            </template>
            <template v-slot:row.col.company_id="{item, value}">
                <entity-hyperlink
                    type="company"
                    :entity-id="item.company_id"
                    :suffix="item.company_id"
                    :dark-mode="darkMode"
                />
            </template>
            <template v-slot:row.col.company_name="{item}">
                <entity-hyperlink
                    type="company"
                    :entity-id="item.company_id"
                    :suffix="item.company_name"
                    :dark-mode="darkMode"
                />
            </template>
            <template v-slot:row.col.billing_profile="{value}">
                <payment-method-badge :type="value.payment_method"/>
            </template>
            <template v-slot:row.col.payment_charges="{item}">
                <div class="flex flex-col gap-1">
                    <hover-tooltip
                        v-if="item?.credits_applied > 0"
                        left
                        large
                        :dark-mode="darkMode"
                    >
                        <template #title>
                            <badge>
                                Credits applied
                            </badge>
                        </template>
                        <template #default>
                            <div class="flex flex-wrap gap-1">
                                {{ $filters.centsToFormattedDollars(item?.credits_applied) }}
                            </div>
                        </template>
                    </hover-tooltip>
                    <hover-tooltip
                        v-for="charge in item.payment_charges"
                        left
                        large
                        :dark-mode="darkMode"
                    >
                        <template #title>
                            <payment-method-badge
                                :type="charge.type"
                                :date="$filters.dateFromTimestamp(charge.date, 'us', 'America/Denver')"
                                only-icon
                            />
                        </template>
                        <template #default>
                            <div class="flex flex-wrap gap-1">
                                {{ charge.total }}
                            </div>
                        </template>
                    </hover-tooltip>
                </div>
            </template>
            <template v-slot:row.col.issue_at="{item,value}">
                <div>{{ $filters.dateFromTimestamp(value, 'MMMM D, YYYY') }}</div>
            </template>
            <template v-slot:row.col.due_at="{item,value}">
                <div>{{ $filters.dateFromTimestamp(value, 'MMMM D, YYYY') }}</div>
            </template>
            <template v-slot:row.col.status_data="{item, value}">
                <div class="flex flex-wrap gap-2">
                    <badge v-if="item.has_pending_action" color="orange">Pending Action</badge>
                    <badge class="w-fit" :dark-mode="darkMode" :color="invoiceHelper.getStatusStyle([value.id]).color">
                        {{ value.title }}
                    </badge>
                    <DisplayInvoiceTransactionStatuses :dark-mode="darkMode"
                                                       :transaction-statuses="item.transaction_statuses"/>
                    <badge v-if="shouldShowFailedAttemptsCount(item)" :dark-mode="darkMode" color="red">
                        Failed {{ item.payment_fail_count }}
                    </badge>
                    <badge v-if="item.pdf_failed" :dark-mode="darkMode" color="red">PDF Failed</badge>
                </div>
            </template>
            <template v-slot:row.col.invoice_value="{item}">
                <div class="relative">
                    {{ $filters.centsToFormattedDollars(item?.items_total) }}
                </div>
            </template>
            <template v-slot:row.col.outstanding_balance="{item}">
                <div class="relative">
                    {{ $filters.centsToFormattedDollars(item?.totals?.outstanding) }}
                </div>
            </template>
            <template v-slot:row.col.pdf="{item,value}">
                <div class="relative">
                    <simple-icon
                        v-if="item.has_pdf"
                        clickable
                        :tooltip="invoiceStore.loadingInvoicePdf ? 'Loading Pdf...' : 'View PDF Invoice'"
                        @click.stop="openInvoicePdf(item)"
                        :icon="simpleIcon.icons.DOCUMENT_TEXT"
                        :color="simpleIcon.colors.BLUE"
                        :dark-mode="darkMode"
                    />
                </div>
            </template>
            <template v-slot:row.col.tags="{item,value}">
                <limited-list
                    :list-items="value"
                    :dark-mode="darkMode"
                    empty-message="No Tags"
                    dynamic-show
                />
            </template>
        </simple-table>
        <view-create-invoice-modal
            :company-name="companyName"
            :company-id="companyId"
            v-if="invoiceStore.showInvoiceModal"
            @close="closeInvoiceModal"
            @invoice-created-updated="invoiceCreatedUpdated"
            :dark-mode="darkMode"
            :invoice-id="viewingInvoiceId"
        />
        <AlertsContainer
            v-if="alertActive"
            :alert-type="alertType"
            :text="alertText"
            :dark-mode="darkMode"
        />
        <ImportLeadsInvoiceModal
            v-if="showImportLeadsModal"
            :dark-mode="darkMode"
            @close="toggleImportLeads(false)"
        />
    </div>
</template>
<script>
import SimpleTable from "../../components/SimpleTable/SimpleTable.vue";
import CustomButton from "../../components/CustomButton.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store";
import AlertsContainer from "../../components/AlertsContainer.vue";
import AlertsMixin from "../../../../mixins/alerts-mixin";
import {SimpleTableFilterTypesEnum} from "../../components/SimpleTable/enum/simpleTableFilterLocationsEnum";
import apiService from "../../../../components/Billing/services/api";
import Badge from "../../components/Badge.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon";
import ViewCreateInvoiceModal from "../../../Billing/ViewCreateInvoiceModal.vue";
import {useInvoiceModalStore} from "../../../../../stores/invoice/invoice-modal.store";
import {SimpleTableHiddenFilterTypesEnum} from "../../components/SimpleTable/enum/simpleTableFilterHiddenTypes";
import useInvoiceHelper from "../../../../../composables/useInvoiceHelper";
import TagApiService from "../../../BillingManagement/services/tags-api";
import DisplayInvoiceTransactionStatuses
    from "../../../Billing/CreateInvoiceModalContentCompanyData/components/DisplayInvoiceTransactionStatuses.vue";
import useQueryParams from "../../../../../composables/useQueryParams.js";
import LimitedList from "../../components/Simple/LimitedList.vue";
import PaymentMethodBadge from "../../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";
import CompanySearchAutocomplete from "../../components/Company/CompanySearchAutocomplete.vue";
import HoverTooltip from "../../components/HoverTooltip.vue";
import EntityHyperlink from "../../../BillingManagement/components/EntityHyperlink.vue";
import ImportLeadsInvoiceModal from "../../../Billing/ImportLeadsInvoiceModal.vue";
import {DateTime} from "luxon";

const queryParamsHelper = useQueryParams();

const simpleIcon = useSimpleIcon()
const invoiceHelper = useInvoiceHelper()

export default {
    name: "InvoiceManagement",
    components: {
        ImportLeadsInvoiceModal,
        EntityHyperlink,
        HoverTooltip,
        CompanySearchAutocomplete,
        PaymentMethodBadge,
        LimitedList,
        DisplayInvoiceTransactionStatuses,
        ViewCreateInvoiceModal,
        SimpleIcon,
        Badge,
        AlertsContainer,
        CustomButton,
        SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            required: false,
            default: null,
        },
        companyName: {
            type: String,
            default: null,
        },
        closeable: {
            type: Boolean,
            default: false,
        },
        customTitle: {
            type: String,
            default: "Invoice Management"
        }
    },
    inject: ['baseFilters'],
    mixins: [AlertsMixin],
    data() {
        return {
            simpleIcon,
            invoiceHelper,
            tagsApiService: TagApiService.make(),
            apiService: apiService.make(),
            permissionStore: useRolesPermissions(),
            invoiceStore: useInvoiceModalStore(),
            showInvoiceModal: false,
            loading: false,
            headers: [
                {title: "ID", field: "id", sortable: true},
                {title: "Company Id", field: "company_id", show: true, sortable: true},
                {title: "Company Name", field: "company_name", show: true, cols: 2, sortable: true},
                {title: "Date Issued", field: "issue_at"},
                {title: "Due Date", field: "due_at"},
                {title: "Payment Method", field: "billing_profile"},
                {title: "Payment Received", field: "payment_charges", cols: 2},
                {title: "Status", field: "status_data", cols: 2},
                {title: "Outstanding Balance", field: "outstanding_balance"},
                {title: "Invoice Value", field: "invoice_value"},
                {title: "PDF", field: "pdf"},
                {title: "Tags", field: "tags", cols: 2},
            ],
            data: [],
            paginationData: {},
            tableFilter: {},
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'invoiceId',
                    title: 'Invoice ID'
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'issued_date',
                    title: 'Issued date',
                    type: SimpleTableHiddenFilterTypesEnum.DATE_RANGE,
                    timezone: 'America/Denver'
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                    field: 'status',
                    title: 'Invoice Status',
                    options: [],
                },
            ],
            showImportLeadsModal: false,
            isCreatingInvoice: true,
            viewingInvoiceId: null,
        }
    },
    computed: {
        canCreateInvoices() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_BILLING_CREATE_INVOICE);
        },
        canImportLeads() {
            return !this.companyId
        }
    },
    created() {
        const {invoice_id} = queryParamsHelper.getCurrentParams()

        this.tableFilter = {
            companyId: this.companyId,
            page: 1,
            perPage: 25,
            per_page: 25,
            invoice_id,
            sort_by: [
                'id:desc'
            ]
        }
        if (this.companyId) {
            this.headers.find(header => header.field === 'company_id').show = false;
        }

        if (invoice_id) {
            this.viewingInvoiceId = invoice_id
            this.openInvoiceModal()
        }

        this.getInvoices();
        this.getFilterOptions();
        this.getTagFilterOptions();
    },
    methods: {
        async getTagFilterOptions() {
            const res = await this.tagsApiService.list();

            if (res.data.data) {
                this.tableFilters.push({
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                    field: 'tags',
                    title: 'Tags',
                    options: res.data.data,
                })
            }
        },
        async getInvoices() {
            this.loading = true

            const from = this.tableFilter?.issued_date?.from;
            const to = this.tableFilter?.issued_date?.to;

            const startDate = from
                ? DateTime.fromISO(from).setZone('America/Denver').toISO()
                : undefined;

            const endDate = to
                ? DateTime.fromISO(to).setZone('America/Denver').toISO()
                : undefined;

            const response = await this.apiService.getInvoices(
                {
                    ...this.tableFilter,
                    startDate,
                    endDate,
                    ...this.baseFilters
                }
            )
            const {data, links, meta} = response.data
            this.data = data
            this.paginationData = {links, ...meta}
            this.loading = false
        },
        async getFilterOptions() {
            const statusFilterIndex = this.tableFilters.findIndex(e => e.field === 'status');

            const response = await this.apiService.getFilterOptions();

            this.tableFilters[statusFilterIndex].options = [
                ...response.data.data.statuses
            ]
        },
        async openInvoicePdf(invoice) {
            if (this.invoiceStore.loadingInvoicePdf) {
                return
            }

            const url = await this.invoiceStore.generatePdfSignedUrl(invoice.id)

            if (url) {
                window.open(url, '__blank')
            }
        },
        invoiceCreatedUpdated(invoiceUuid, message) {
            this.showAlert('success', message + invoiceUuid);
            this.closeInvoiceModal();
            this.getInvoices();
        },
        async handleSearch() {
            await this.getInvoices()
        },
        async handleReset() {
            this.tableFilter = {
                ...this.tableFilter,
                page: 1,
                perPage: 10,
                companyId: this.companyId,
                sort_by: [
                    'id:desc'
                ]
            }
            await this.getInvoices()
        },
        handleRowClick(invoice) {
            // this.viewingInvoiceId = invoice.id
            // this.openInvoiceModal()
            this.isCreatingInvoice = false;
            window.open(`/billing-management?tab=Invoices&invoice_id=${invoice.id}`, '_blank')
        },
        openInvoiceModal() {
            this.invoiceStore.setShowInvoiceModal(true)
        },
        toggleImportLeads(show) {
            this.showImportLeadsModal = show
        },
        closeInvoiceModal() {
            this.viewingInvoiceId = null;
            this.isCreatingInvoice = true;
            this.invoiceStore.setShowInvoiceModal(false)
        },
        shouldShowFailedAttemptsCount(item) {
            return ['issued', 'failed'].includes(item.status_data.id) && item.payment_fail_count
        }
    }
}
</script>
