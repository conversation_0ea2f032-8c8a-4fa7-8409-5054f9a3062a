<template>
    <div class="border rounded-lg"
         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="pb-5">
            <div class="flex items-center justify-between p-5">
                <h5 class="text-sm uppercase text-primary-500 font-bold leading-tight">Upcoming Tasks</h5>
            </div>
            <div class="grid grid-cols-12 gap-x-3 px-5 pb-3 items-center">
                <p class="uppercase text-xs font-bold text-slate-500 col-span-2">Task Name</p>
                <p class="uppercase text-xs font-bold text-slate-500">Assigned User</p>
                <p class="uppercase text-xs font-bold text-slate-500 col-span-2">Category</p>
                <p class="uppercase text-xs font-bold text-slate-500">Timezone</p>
                <p class="uppercase text-xs font-bold text-slate-500 col-span-2">Company</p>
                <p class="uppercase text-xs font-bold text-slate-500">State/City</p>
                <p class="uppercase text-xs font-bold text-slate-500">Source</p>
                <p class="uppercase text-xs font-bold text-slate-500">Priority</p>
                <p class="uppercase text-xs font-bold text-slate-500">Due</p>
            </div>
            <div v-if="loading"
                 class="h-80 flex justify-center items-center border-y"
                 :class="[darkMode ? 'border-dark-border bg-dark-background' : 'border-light-border bg-light-background']">
                <loading-spinner></loading-spinner>
            </div>
            <div
                v-else
                class="border-y divide-y h-80 overflow-y-auto"
                :class="[darkMode ? 'border-dark-border divide-dark-border bg-dark-background text-slate-100' : 'border-light-border divide-light-border bg-light-background text-slate-900']">
                <div v-if="tasks?.length > 0"
                     v-for="(task, index) in tasks"
                    :class="[darkMode ? 'bg-dark-background hover:bg-dark-module' : 'bg-light-background hover:bg-light-module']"
                >
                    <Task
                        :key="task?.id ?? index"
                        :task="task"
                        :dark-mode="darkMode"
                        :read-only="true"
                        :filter-tab="null"
                        :quick-view="true"
                    >
                    </Task>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import LoadingSpinner from "../../components/LoadingSpinner.vue";
import {onMounted, ref} from "vue";
import ApiService from "../../../Tasks/services/api.js";
import Task from "./Task.vue";

const props = defineProps({
    darkMode: {
        type: Boolean,
        default: false,
    },
    companyId: {
        type: Number,
        required: true,
    }
})

const loading = ref(false);
const tasks = ref([]);

onMounted(() => {
    getUpcomingTasks();
})

const getUpcomingTasks = async () => {
    loading.value = true;
    ApiService.make().getUpcomingTasks({
        'all_user': true,
        'sort_col': 'priority',
        'sort_dir': 'desc',
        'company_id': props.companyId,
        'timezone': 'any',
        'perPage': 25,
    }).then(resp => {
        tasks.value = resp.data.data.tasks.data;
    }).catch(e => console.error(e)).finally(() => loading.value = false);
}
</script>

<style scoped>

</style>
