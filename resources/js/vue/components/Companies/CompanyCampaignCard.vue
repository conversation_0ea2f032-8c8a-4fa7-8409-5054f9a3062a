<template>
    <div class="border rounded p-4 relative"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="absolute right-0 top-0 mt-2 mr-2">
            <slot name="action-handle">
            </slot>
        </div>
        <h4 class="text-lg font-semibold mb-3 truncate" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">{{ campaign.name }}</h4>
        <div class="flex justify-between items-center mb-3">
            <div class="flex items-center gap-x-4">
                <div class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap"
                     :class="[
                                                 darkMode
                                                 ? (campaign.active ? 'text-green-550 border border-green-550' : 'text-red-350 border border-red-350')
                                                 : (campaign.active ? 'text-green-550 bg-green-150' : 'text-red-350 bg-red-75')]">
                    <p v-if="campaign.active" class="text-xs">Active</p>
                    <p v-else class="text-xs">Inactive</p>
                </div>
                <badge
                    v-if="!campaign.active"
                    class="ml-1"
                    wrap-style="rounded"
                    :dark-mode="darkMode"
                    :color="campaign.reactivate_at ? 'amber' :'red'"
                >
                    {{campaign.reactivate_at ? "Paused Temporarily" : "Paused Permanently"}}
                </badge>
            </div>
            <div class="flex items-center gap-x-1"
                 :class="[darkMode ? 'text-grey-300' : 'text-grey-500']"
            >
                <Tooltip v-if="zipCodeTargeted" :dark-mode="darkMode">
                    <template v-slot:icon>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z" />
                        </svg>
                    </template>
                    <template v-slot:default>
                        {{ campaign.zip_code_targeted ? 'This is a zip code targeted campaign' : 'This company has unrestricted zip code targeting' }}
                    </template>
                </Tooltip>
                <Tooltip v-if="campaign.uses_custom_floor_prices" :dark-mode="darkMode">
                    <template v-slot:icon>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                        </svg>
                    </template>
                    <template v-slot:default>
                        This campaign has custom floor pricing enabled
                    </template>
                </Tooltip>
                <Tooltip v-if="campaign.has_filters && campaign.campaign_filter_enabled" :dark-mode="darkMode">
                    <template v-slot:icon>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z" />
                        </svg>
                    </template>
                    <template v-slot:default>
                        This campaign has active filters
                    </template>
                </Tooltip>
            </div>
        </div>
        <div v-if="campaign.bidding_disabled">
            <div class="inline-flex items-center gap-1 px-2 py-1 mb-2 mt-1 text-xs font-medium text-gray-500 bg-gray-100 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="h-4 w-4 text-gray-500">
                    <path fill-rule="evenodd" d="m6.72 5.66 11.62 11.62A8.25 8.25 0 0 0 6.72 5.66Zm10.56 12.68L5.66 6.72a8.25 8.25 0 0 0 11.62 11.62ZM5.105 5.106c3.807-3.808 9.98-3.808 13.788 0 3.808 3.807 3.808 9.98 0 13.788-3.807 3.808-9.98 3.808-13.788 0-3.808-3.807-3.808-9.98 0-13.788Z" clip-rule="evenodd" />
                </svg>
                Bidding Disabled
            </div>
        </div>
        <div class="mb-3" v-if="campaign.industry">
            <p class="font-semibold" :class="{'text-grey-800': !darkMode, 'text-grey-200': darkMode}">
                Industry & Service
            </p>
            <div :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}"
                 class="ml-2"
            >
                <p class="text-sm">{{ campaign.industry }}</p>
                <p class="text-sm">{{ campaign.service }}</p>
            </div>
        </div>
        <div class="mb-3">
            <p class="font-semibold" :class="{'text-grey-800': !darkMode, 'text-grey-200': darkMode}">
                Region & Utilities
            </p>
            <div :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}">
                <div class="flex items-center gap-x-3">
                    <p class="text-sm" v-if="zipCodeTargeted">
                        {{ campaign.zip_code_count }} zip codes in {{  campaign.county_count }} {{ campaign.county_count > 1 ? 'counties' : 'county' }}
                    </p>
                    <p class="text-sm" v-else>
                        <button v-if="campaign.county_count || campaign.state_count" class="text-blue-500" @click="openCountiesInStateModal">{{ campaign.county_count }} {{ campaign.county_count > 1 ? 'counties' : 'county' }} in {{ campaign.state_count }} {{ campaign.state_count > 1 ? 'states' : 'state' }}</button>
                        <div v-else>{{ campaign.county_count }} {{ campaign.county_count > 1 ? 'counties' : 'county' }} in {{ campaign.state_count }} {{ campaign.state_count > 1 ? 'states' : 'state' }}</div>
                        <Modal v-if="countiesInStateModal" @close="closeCountiesInStateModal" no-buttons restrict-width small :dark-mode="darkMode">
                            <template v-slot:header>
                                <h1 class="text-lg font-semibold">{{ campaign.county_count }} {{ campaign.county_count > 1 ? 'counties' : 'county' }} in {{ campaign.state_count }} {{ campaign.state_count > 1 ? 'states' : 'state' }}</h1>
                            </template>

                            <template v-slot:content>
                                <div class="p-4 space-y-4">
                                    <div v-if="campaign.state_count">
                                        <h1 class="text-lg">States</h1>
                                        <ul>
                                            <li :key="location.state_key" v-for="location in campaign.states">
                                                {{ location.state }}
                                                <h2 class="mt-2 ml-2">Counties</h2>
                                                <ul class="ml-4">
                                                    <li :key="county.county_key" v-for="county in location.counties">{{ county.county }}</li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </template>
                        </Modal>
                    </p>
                </div>
            </div>
        </div>

        <div class="mb-3" v-if="campaign.budgets.length">
            <p class="font-semibold" :class="{'text-grey-800': !darkMode, 'text-grey-200': darkMode}">
                {{ campaign.product }}s Budgets
            </p>
            <div v-for="budget in campaign.budgets" class="ml-2">
                <div v-if="budget.status"
                    :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}"
                >
                    <p class="text-sm">{{ budget.display_name || $filters.toProperCase(budget.key) }} Leads:
                        {{ budget.type_display }}
                        <span v-if="budget.type > 0">
                            - {{ budget.type === 2 ? '$' : '' }}{{ budget.value }}
                        </span>
                    </p>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <p class="font-semibold" :class="{'text-grey-800': !darkMode, 'text-grey-200': darkMode}">
                Property Types
            </p>
            <div class="text-sm list-disc ml-2"
                 :class="{'text-grey-500': !darkMode, 'text-grey-300': darkMode}"
                 v-for="type in campaign.property_types"
            >
                {{ type }}
            </div>
        </div>
        <div class="mb-3">
            <p class="font-semibold" :class="{'text-grey-800': !darkMode, 'text-grey-200': darkMode}">
                Delivery Methods
            </p>
            <div v-if="campaign.delivery_methods" class="flex gap-2">
                <badge v-if="campaign.delivery_methods.crm" color="primary">CRM</badge>
                <badge v-if="campaign.delivery_methods.phone" color="primary">Phone</badge>
                <badge v-if="campaign.delivery_methods.email" color="primary">Email</badge>
                <div v-if="!campaign.delivery_methods.crm && !campaign.delivery_methods.phone && !campaign.delivery_methods.email" class="text-sm ml-2" :class="{'text-red-500': !darkMode, 'text-red-300': darkMode}">None</div>
            </div>
        </div>
        <div class="flex flex-col text-xs text-slate-500">
            <div v-if="campaign.reactivate_at">Reactivates: {{campaign.reactivate_at}}</div>
            <div>Last Lead Sold: {{campaign.lead_last_sold_at}}</div>
            <div>Avg Lead Cost: {{campaign.average_lead_cost}}</div>
            <div>
                Verified Budget Usage: {{ this.getVerifiedBudgetUsage(campaign) }}
            </div>
        </div>
    </div>
</template>
<script>
import Badge from "../Shared/components/Badge.vue";
import Tooltip from "../Shared/components/Tooltip.vue";
import Modal from "../Shared/components/Modal.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import SharedApiService from "../Shared/services/api.js";

export default {
    name: "CompanyCampaignCard",
    components: {CustomButton, Dropdown, Tooltip, Badge, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        unrestrictedZipCodeTargeting: {
            type: Boolean,
            default: false,
        },
        campaign: {
            type: Object,
            required: true
        },
        optInNames: {
            type: Array,
            required: true
        },
        companyId: {
            type: Number,
            required: true
        }
    },
    emits:['refresh-campaign'],
    data() {
        return {
            countiesInStateModal: false,
            updateActive: false,
            selectedActiveOptInName: null,
            saving: false,
            api: SharedApiService.make()
        }
    },
    computed: {
        zipCodeTargeted() {
            return this.unrestrictedZipCodeTargeting || this.campaign.zip_code_targeted;
        }
    },
    methods: {
        openCountiesInStateModal() {
            this.countiesInStateModal = true;
        },
        closeCountiesInStateModal() {
            this.countiesInStateModal = false;
        },
        deleteActiveOptInName() {
            this.saving = true;
            this.api.deleteCampaignActiveOptInName(this.companyId, this.campaign.id)
                .then(resp => this.$emit('refresh-campaign'))
                .catch(e => console.error(e))
                .finally(() => this.saving = false);
        },
        setActiveOptInName() {
            if (!this.selectedActiveOptInName) {
                return;
            }

            this.saving = true;
            this.api.setCampaignActiveOptInName(this.companyId, this.campaign.id, this.selectedActiveOptInName)
                .then(resp => this.$emit('refresh-campaign'))
                .catch(e => console.error(e))
                .finally(() => this.saving = false);
        },
        getVerifiedBudgetUsage(campaign) {
            if (!campaign.verified_budget_usage) {
                return 'N/A'
            }

            if (campaign.verified_budget_usage.type === 0) {
                return 'No Limit';
            }

            return campaign.verified_budget_usage.usage?.toFixed(2) + '%';
        }
    }
}
</script>
