<template>
    <div class="border rounded-lg overflow-hidden mb-10" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="flex justify-between items-center px-5 py-3 gap-5 border-b" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Company Profile</h5>
            <div class="flex-grow max-w-screen-sm">
                <CustomInput :dark-mode="darkMode" v-model="searchString" search-icon :placeholder="'Search ' + profileTabs[selectedProfileTab].name" v-if="!searchIsHidden"/>
            </div>
            <toggle-switch :dark-mode="darkMode" :disabled="!allowToggleDisplayProfile" v-on:click="toggleDisplayProfile" label="Display Profile" :value="displayProfile"></toggle-switch>
        </div>
        <div class="flex">
            <div class="h-[32rem] flex-shrink-0 overflow-y-auto divide-y border-r w-72" :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border text-slate-200' : 'bg-light-background  border-light-border divide-light-border text-slate-900']">
                <div @click="selectProfileTab(profileTab.id)"
                     v-for="profileTab in profileTabs"
                     :key="profileTab"
                     class="cursor-pointer grid grid-cols-1 gap-x-3 py-3 px-5 group relative transition duration-100 items-center"
                     :class="[selectedProfileTab === profileTab.id ? (darkMode ? 'bg-slate-800 bg-opacity-50 text-primary-500 font-medium' : 'bg-primary-50 text-primary-500 font-medium') : (darkMode ? 'hover:bg-dark-module' : 'hover:bg-light-module')]">
                    <div class="absolute left-0 h-full w-1"
                         :class="[selectedProfileTab === profileTab.id ? (darkMode ? 'bg-primary-500' : 'bg-primary-500') : (darkMode ? 'bg-slate-600 invisible group-hover:visible' : 'bg-slate-400 invisible group-hover:visible') ]">
                    </div>
                    <p class="inline-flex items-center gap-2">
                        <CompanyProfileIcons :tab="profileTab.id" :selected="selectedProfileTab" :dark-mode="darkMode"/>
                        {{profileTab.name}}
                    </p>
                    <div class="absolute right-5">
                        <svg class="fill-current" :class="[selectedProfileTab === profileTab.id ? (darkMode ? 'text-primary-500' : 'text-primary-500') : 'text-transparent']"
                             width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L6.70711 5.29289C7.09763 5.68342 7.09763 6.31658 6.70711 6.70711L1.70711 11.7071C1.31658 12.0976 0.683417 12.0976 0.292893 11.7071C-0.0976311 11.3166 -0.0976311 10.6834 0.292893 10.2929L4.58579 6L0.292893 1.70711C-0.0976311 1.31658 -0.0976311 0.683417 0.292893 0.292893Z"/>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="flex-grow h-[32rem] overflow-y-auto">
                <div class="h-full flex items-center justify-center" v-if="loading">
                    <loading-spinner></loading-spinner>
                </div>
                <div v-else :class="[darkMode ? 'text-slate-200' : 'text-slate-800']">
                    <!-- Basic Info -->
                    <div v-if="selectedProfileTab === 0">
                        <BasicInfo :search-string="searchString" :dark-mode="darkMode" :has-edit-rights="hasEditRights" :company-id="companyId" :legacy-company-id="legacyCompanyId" @activate-alert="handleActivateAlert"></BasicInfo>
                    </div>

                    <!-- Addresses -->
                    <div v-if="selectedProfileTab === 1">
                        <Addresses :dark-mode="darkMode" :has-edit-rights="hasEditRights" :company-id="companyId"></Addresses>
                    </div>

                    <!-- todo each tab should be its own component  -->
                    <!-- Activity -->
                    <div v-if="selectedProfileTab === 2" class="grid md:grid-cols-2 lg:grid-cols-4 gap-4 p-5 relative">
                        <div>
                            <p class="font-semibold pb-1" v-html="highlight('Last Revised')"></p>
                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{$filters.dateFromTimestamp(company.lastRevised) || placeholder.generic}}</p>
                        </div>
                        <div>
                            <p class="font-semibold pb-1" v-html="highlight('Last Changed')"></p>
                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{$filters.dateFromTimestamp(company.lastChanged) || placeholder.generic}}</p>
                        </div>
                        <div>
                            <p class="font-semibold pb-1" v-html="highlight('Last Login')"></p>
                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{$filters.dateFromTimestamp(company.lastLogin) || placeholder.generic}}</p>
                        </div>
                        <div>
                            <p class="font-semibold pb-1" v-html="highlight('Last Quote')"></p>
                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{$filters.dateFromTimestamp(company.lastQuote) || placeholder.generic}}</p>
                        </div>
                        <div>
                            <p class="font-semibold pb-1" v-html="highlight('Last Review')"></p>
                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{$filters.dateFromTimestamp(company.lastReview) || placeholder.generic}}</p>
                        </div>
                    </div>

                    <!-- Contact & Social Links -->
                    <div v-if="selectedProfileTab === 3" class="grid md:grid-cols-2 lg:grid-cols-4 gap-4 p-5 relative">
                        <div>
                            <p class="font-semibold pb-1" v-html="highlight('Link to Profile')"></p>
                            <a target="_blank" class="text-primary-500" :href="company.profilePageLink">Click to open</a>
                        </div>
                        <div>
                            <p class="font-semibold pb-1" v-html="highlight('Website Link')"></p>
                            <p  v-if="!editingSocialInfo" :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{company.website || placeholder.generic}}</p>
                            <input v-else type="text" :name="company.website" v-model="company.website" class="rounded border font-medium text-sm h-9 w-full" :class="[darkMode ? 'bg-dark-background border-dark-border text-slate-200' : 'bg-light-background border-light-border text-slate-900']" />
                        </div>
                        <div>
                            <p class="font-semibold pb-1" v-html="highlight('Social')"></p>
                            <div v-if="!editingSocialInfo" class="inline-flex items-center gap-x-6 mt-1">
                                <div v-for="link in company.socialMediaLinks">
                                    <a v-if="link.linktype === 'twitter'" :href="link.linkvalue" target="_blank">
                                        <svg class="cursor-pointer w-4 h-4 flex-shrink-0" viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M10.7671 2.24295C10.7751 2.34162 10.7751 2.43973 10.7751 2.53784C10.7751 5.54028 8.30022 9 3.77733 9C2.38388 9 1.08935 8.6273 0 7.98002C0.197842 8.00088 0.388357 8.00821 0.594138 8.00821C1.7012 8.01068 2.77691 7.66885 3.64787 7.03784C3.13459 7.02926 2.63703 6.87289 2.22465 6.59057C1.81227 6.30824 1.50567 5.91405 1.34765 5.46304C1.49969 5.4839 1.65235 5.498 1.81233 5.498C2.03277 5.498 2.25443 5.4698 2.46021 5.42075C1.90317 5.3169 1.40228 5.0381 1.04271 4.63176C0.683145 4.22542 0.487091 3.71663 0.487889 3.19189V3.1637C0.815795 3.33229 1.19621 3.43773 1.59923 3.45182C1.26161 3.24466 0.984767 2.96355 0.793409 2.63357C0.602051 2.3036 0.502125 1.93501 0.502544 1.56071C0.502544 1.13896 0.624059 0.752162 0.837167 0.414986C1.4552 1.11698 2.22603 1.69127 3.09974 2.10067C3.97344 2.51007 4.93053 2.74545 5.90902 2.79157C5.87116 2.62242 5.84795 2.44706 5.84795 2.27114C5.84779 1.97285 5.9113 1.67745 6.03485 1.40184C6.1584 1.12622 6.33957 0.875797 6.568 0.664872C6.79642 0.453948 7.06763 0.286662 7.36611 0.172579C7.6646 0.0584958 7.98451 -0.00014794 8.30755 2.80264e-07C9.01588 2.80264e-07 9.6552 0.274026 10.1046 0.717203C10.6553 0.61888 11.1833 0.433267 11.6654 0.168588C11.4818 0.693419 11.0973 1.13845 10.584 1.42031C11.0723 1.36888 11.5496 1.25029 12 1.06848C11.6636 1.52129 11.2468 1.91838 10.7671 2.24295V2.24295Z" fill="#0081FF"/>
                                        </svg>
                                    </a>
                                    <a v-else-if="link.linktype === 'linkedin'" :href="link.linkvalue" target="_blank">
                                        <svg class="cursor-pointer w-4 h-4 flex-shrink-0" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1.32205 2.64411C2.05221 2.64411 2.64411 2.05221 2.64411 1.32205C2.64411 0.591904 2.05221 0 1.32205 0C0.591904 0 0 0.591904 0 1.32205C0 2.05221 0.591904 2.64411 1.32205 2.64411Z" fill="#0081FF"/>
                                            <path d="M3.89233 3.64593V10.9807H6.16967V7.35348C6.16967 6.39638 6.34973 5.4695 7.53644 5.4695C8.70684 5.4695 8.72134 6.56376 8.72134 7.41391V10.9813H10.9999V6.95892C10.9999 4.98309 10.5745 3.46466 8.26514 3.46466C7.15638 3.46466 6.41318 4.07312 6.10925 4.64895H6.07844V3.64593H3.89233V3.64593ZM0.181152 3.64593H2.46212V10.9807H0.181152V3.64593Z" fill="#0081FF"/>
                                        </svg>
                                    </a>
                                    <a v-else-if="link.linktype === 'facebook'" :href="link.linkvalue" target="_blank">
                                        <svg class="cursor-pointer w-4 h-4 flex-shrink-0" viewBox="0 0 5 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M3.24818 10V5.44595H4.72901L4.94912 3.66289H3.24818V2.52716C3.24818 2.01263 3.38635 1.66036 4.09812 1.66036H5V0.0706607C4.56118 0.0218702 4.1201 -0.00168805 3.67877 9.3996e-05C2.36986 9.3996e-05 1.47119 0.829113 1.47119 2.35102V3.65956H0V5.44262H1.4744V10H3.24818Z" fill="#0081FF"/>
                                        </svg>
                                    </a>
                                    <a v-else-if="link.linktype === 'youtube'" :href="link.linkvalue" target="_blank">
                                        <svg class="cursor-pointer w-4 h-4 flex-shrink-0" viewBox="0 0 29 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M28.0882 16.8181C27.7563 18.0417 26.7987 18.9999 25.5713 19.3272C23.9225 19.9617 7.9524 20.2727 3.31969 19.309C2.09221 18.9781 1.13101 18.0236 0.80271 16.7999C0.0585591 13.5454 0.00201823 6.50902 0.820949 3.18174C1.1529 1.95811 2.11045 0.999924 3.33793 0.672651C6.60271 -0.0764397 22.1241 -0.181894 25.5895 0.672651C26.817 1.00356 27.7782 1.95811 28.1065 3.18174C28.8999 6.7272 28.9564 13.3272 28.0882 16.8181Z" fill="#0081FF"/>
                                            <path d="M19.151 9.99985L11.7095 14.2544V5.7453L19.151 9.99985Z" fill="white"/>
                                        </svg>
                                    </a>
                                    <a v-else :title="link.linktype" :href="link.linkvalue" target="_blank">
                                        <svg class="cursor-pointer w-4 h-4 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" >
                                            <path fill="#0081FF" fill-rule="evenodd" d="M19.902 4.098a3.75 3.75 0 00-5.304 0l-4.5 4.5a3.75 3.75 0 001.035 *********** 0 01-.646 1.353 5.25 5.25 0 01-1.449-8.45l4.5-4.5a5.25 5.25 0 117.424 7.424l-1.757 1.757a.75.75 0 11-1.06-1.06l1.757-1.757a3.75 3.75 0 000-5.304zm-7.389 4.267a.75.75 0 011-.353 5.25 5.25 0 011.449 8.45l-4.5 4.5a5.25 5.25 0 11-7.424-7.424l1.757-1.757a.75.75 0 111.06 1.06l-1.757 1.757a3.75 3.75 0 105.304 5.304l4.5-4.5a3.75 3.75 0 00-1.035-*********** 0 01-.354-1z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                            <div v-else>
                                <div class="mb-2">
                                    <label class="block mb-1" v-html="highlight('Twitter')"></label>
                                    <input type="text" :name="company.twitter" v-model="company.twitter" class="rounded border font-medium text-sm h-9 w-full" :class="[darkMode ? 'bg-dark-background border-dark-border text-slate-200' : 'bg-light-background border-light-border text-slate-900']" />
                                </div>
                                <div class="mb-2">
                                    <label class="block mb-1" v-html="highlight('LinkedIn')"></label>
                                    <input type="text" :name="company.linkedIn" v-model="company.linkedIn" class="rounded border font-medium text-sm h-9 w-full" :class="[darkMode ? 'bg-dark-background border-dark-border text-slate-200' : 'bg-light-background border-light-border text-slate-900']" />
                                </div>
                                <div class="mb-2">
                                    <label class="block mb-1" v-html="highlight('Facebook')"></label>
                                    <input type="text" :name="company.facebook" v-model="company.facebook" class="rounded border font-medium text-sm h-9 w-full" :class="[darkMode ? 'bg-dark-background border-dark-border text-slate-200' : 'bg-light-background border-light-border text-slate-900']" />
                                </div>
                                <div class="mb-2">
                                    <label class="block mb-1" v-html="highlight('Youtube')"></label>
                                    <input type="text" :name="company.youtube" v-model="company.youtube" class="rounded border font-medium text-sm h-9 w-full" :class="[darkMode ? 'bg-dark-background border-dark-border text-slate-200' : 'bg-light-background border-light-border text-slate-900']" />
                                </div>
                            </div>
                        </div>
                        <!-- Disabling the edit option until the backend logic to sync this data with Legacy is finalized -->
                        <div v-if="false">
                            <div class="flex items-center justify-between p-5">
                                <div>
                                    <div v-if="!editingSocialInfo" @click="editSocialInfo" class="w-4 p-1 cursor-pointer mr-5" >
                                        <svg class="w-4"  viewBox="0 0 11 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z" fill="#0081FF"/>
                                        </svg>
                                    </div>
                                    <div v-else class="flex items-center">
                                        <button v-if="editingSocialInfo" @click="editSocialInfo" class="mr-3 text-white h-9 bg-emerald-500  hover:bg-emerald-600 mt-5 sm:mt-0 px-5 py-2 font-medium text-sm rounded-md inline-flex items-center justify-center">
                                            Save
                                        </button>
                                        <button v-if="editingSocialInfo" @click="editSocialInfo" class="text-white h-9 bg-slate-400 hover:bg-slate-500 mt-5 sm:mt-0 px-5 py-2 font-medium text-sm rounded-md inline-flex items-center justify-center">
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Billing & Plan Details -->
                    <div v-if="selectedProfileTab === 4" >
                        <div  class="flex flex-col">
                            <div class="flex gap-2 text-md font-semibold ml-4 my-4" v-if="companyBilling.version === companyBilling.VERSIONS.V2">
                                Billing 2.0
                                <version-badge :dark-mode="darkMode"/>
                                <simple-icon
                                    :icon="showArchived ? simpleIcon.icons.EYE_SLASH : simpleIcon.icons.EYE"
                                    :tooltip="showArchived ? 'Hide archived' : 'Show Archived'"
                                    clickable
                                    @click="toggleArchived()"
                                />
                            </div>
                            <billing-details
                                v-if="companyBilling.version === companyBilling.VERSIONS.V2"
                                :dark-mode="darkMode"
                                :company-id="companyId"
                                :show-archived="showArchived"
                            />
                            <div class="flex gap-2 text-md font-semibold ml-4 mt-4">
                                Legacy Billing
                                <version-badge v-if="companyBilling.version === companyBilling.VERSIONS.V2" :dark-mode="darkMode" version="v1"/>
                            </div>
                            <div class="gap-4 p-5 grid grid-cols-2">
                                <simple-card  :dark-mode="darkMode">
                                    <div class="p-5  grid grid-cols-4 gap-2">
                                        <labeled-value label="Paying By">
                                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">
                                                {{ company.payingBy ? `Paying By ${company.payingBy}` : placeholder.notSet }}
                                            </p>
                                            <a :href="legacyHref('generateInvoice')" v-if="company.payingBy === 'Invoice'" class="text-primary-500 text-sm" target="_blank">
                                                Generate Invoice
                                            </a>
                                        </labeled-value>
                                        <labeled-value label="Payment Source">
                                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{company.paymentSource || placeholder.notSet }}</p>
                                        </labeled-value>
                                        <labeled-value label="Date of next Charge">
                                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">
                                                {{ company.dateOfNextCharge ? $filters.dateFromTimestamp(company.dateOfNextCharge) : 'Not set' }}
                                            </p>
                                        </labeled-value>
                                        <labeled-value label="Active Payment Gateway">
                                            <p :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{company.activePaymentGateway}}</p>
                                        </labeled-value>
                                        <labeled-value class="col-span-full" label="Payment Methods">
                                            <a :href="legacyHref('paymentMethods')" class="text-primary-500 text-sm" target="_blank">
                                                View Payment Methods
                                            </a>
                                        </labeled-value>
                                    </div>
                                </simple-card>
                            </div>
                        </div>
                    </div>

                    <!-- Media & Attachments -->
                    <div v-if="selectedProfileTab === 5"
                         class="grid grid-cols-1 min-w-full max-w-sm gap-4 mt-5"
                    >
                        <div class="mb-6">
                            <company-logo
                                :company-id="companyId"
                                :dark-mode="darkMode"
                                :has-edit-rights="hasEditRights"
                            />
                        </div>
                        <div class="mb-6">
                            <company-media-assets
                                :company-id="companyId"
                                :dark-mode="darkMode"
                                :has-edit-rights="hasEditRights"
                            />
                        </div>
                    </div>

                    <!-- Configurable Fields -->
                    <div v-if="selectedProfileTab === 6" v-for="(scope, scopeName) in company.companyFields" class="p-5">
                        <div v-for="(scopeCategory, scopeCategoryName) in scope">
                            <div v-for="(fields, categoryName) in scopeCategory" class="columns-2 xl:columns-4">
                                <div v-for="field in fields" class="inline-block w-full mb-4 mr-4">
                                    <div class="flex items-center">
                                        <p class="font-semibold pb-1" v-html="highlight(field.name)"></p>
                                        <div @click="editField(field.key)" class="w-5 h-5 rounded cursor-pointer ml-2 inline-flex items-center justify-center group" :class="[darkMode ? 'hover:bg-slate-700' : 'hover:bg-primary-100']">
                                            <svg class="fill-current text-slate-500 group-hover:text-primary-500" width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M9.05781 0.522434C9.21567 0.358989 9.4045 0.22862 9.61329 0.138933C9.82207 0.0492468 10.0466 0.00203912 10.2738 6.46124e-05C10.5011 -0.00190989 10.7264 0.0413885 10.9367 0.127433C11.147 0.213478 11.3381 0.340546 11.4988 0.501223C11.6595 0.6619 11.7865 0.852967 11.8726 1.06328C11.9586 1.27359 12.0019 1.49893 11.9999 1.72615C11.998 1.95337 11.9508 2.17793 11.8611 2.38671C11.7714 2.5955 11.641 2.78433 11.4776 2.94219L10.799 3.62071L8.37929 1.20096L9.05781 0.522434ZM7.16941 2.41083L0 9.58025V12H2.41975L9.59002 4.83059L7.16941 2.41083Z"/>
                                            </svg>
                                        </div>
                                    </div>
                                    <edit-field
                                        v-if="editingField === field.key"
                                        :dark-mode="darkMode"
                                        @update-field="updateField"
                                        @cancel-edit="cancelEdit"
                                        :value="field.value"
                                        :field-name="field.key"
                                        :type="getFieldType(field.type)"
                                        :options="booleanOptions"
                                    />
                                    <div v-else>
                                        <p v-if="field.type === 'Boolean'" :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{ formatBooleanValue(field.value) }}</p>
                                        <p v-else :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">{{ field.value }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Expert Review -->
                    <div v-if="selectedProfileTab === 7">
                        <expert-reviews
                            :dark-mode="darkMode"
                            :companyId="companyId"
                        />
                    </div>

                    <!-- Business Hours -->
                    <div v-if="selectedProfileTab === 8">
                        <BusinessHours :dark-mode="darkMode" :has-edit-rights="hasEditRights" :company-id="companyId"></BusinessHours>
                    </div>

                    <!-- Brands Sold -->
                    <div v-if="selectedProfileTab === 9">
                        <BrandsSold :dark-mode="darkMode" :has-edit-rights="hasEditRights" :company-id="companyId"></BrandsSold>
                    </div>

                    <!-- Credentials -->
                    <div v-if="selectedProfileTab === 10">
                        <Credentials :dark-mode="darkMode" :has-edit-rights="hasEditRights" :company-id="companyId"></Credentials>
                    </div>

                    <!-- Content -->
                    <div v-if="selectedProfileTab === 11">
                        <Content :dark-mode="darkMode" :has-edit-rights="hasEditRights" :company-id="companyId"></Content>
                    </div>

                    <!-- Company Relationships -->
                    <div v-if="selectedProfileTab === 12">
                        <CompanyRelationships :dark-mode="darkMode" :has-edit-rights="hasEditRights" :company-id="companyId"></CompanyRelationships>
                    </div>

                </div>
            </div>
        </div>
        <div class="p-3 border-t" :class="[darkMode ? 'border-dark-border' : 'border-light-border']"></div>
    </div>
</template>

<script>
import Dropdown from "../../Shared/components/Dropdown.vue";
import Addresses from "../../Shared/modules/Addresses.vue";
import ApiService from "../services/api";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import EditField from "../../Shared/components/EditField.vue";
import AlertsMixin from "../../../mixins/alerts-mixin";
import Alert from "../../Shared/components/Alert.vue";
import MultiSelect from "../../Shared/components/MultiSelect.vue";
import SharedApiService from "../../Shared/services/api";
import CustomButton from "../../Shared/components/CustomButton.vue";
import { useRolesPermissions } from "../../../../stores/roles-permissions.store.js";
import CustomInput from "../../Shared/components/CustomInput.vue";
import CompanyProfileIcons from "./CompanyProfileIcons.vue";
import CompanyLogo from "../../Shared/modules/CompanyLogo.vue";
import CompanyMediaAssets from "../../Shared/modules/CompanyMediaAssets.vue";
import ExpertReviews from "./CompanyProfileComponents/ExpertReviews.vue";
import BasicInfo from "./CompanyProfileComponents/BasicInfo.vue";
import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
import BusinessHours from "./CompanyProfileComponents/BusinessHours.vue";
import BrandsSold from "./CompanyProfileComponents/BrandsSold.vue";
import Credentials from "./CompanyProfileComponents/Credentials.vue";
import Content from "./CompanyProfileComponents/Content.vue";
import BillingDetails from "./CompanyProfileComponents/BillingDetails.vue";
import VersionBadge from "../../Billing/VersionBadge.vue";
import {useCompanyBillingStore} from "../../../../stores/billing/company-billing.js";
import SimpleCard from "../../MarketingCampaign/SimpleCard.vue";
import PaymentMethodBadge from "../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import DisplayPendingActions from "../../Shared/components/DisplayPendingActions/DisplayPendingApprovals.vue";
import CompanyRelationships from "./CompanyRelationships.vue";

const activeInactiveOptions = [
    'Active', 'Inactive'
];

const positiveNegativeOptions = [
    'Yes', 'No'
];

export default {
    name: "CompanyProfile",
    components: {
        CompanyRelationships,
        DisplayPendingActions,
        SimpleIcon, LabeledValue, PaymentMethodBadge,
        SimpleCard,
        VersionBadge,
        BillingDetails,
        Content,
        Credentials,
        BrandsSold,
        ToggleSwitch,
        BasicInfo,
        ExpertReviews,
        CompanyProfileIcons,
        CustomInput,
        CustomButton,
        Addresses,
        Dropdown,
        LoadingSpinner,
        EditField,
        Alert,
        MultiSelect,
        CompanyLogo,
        CompanyMediaAssets,
        BusinessHours,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null
        },
        legacyCompanyId: {
            type: Number,
            default: null
        }
    },
    mixins: [
        AlertsMixin
    ],
    data() {
        return {
            api: ApiService.make(),
            sharedApiService: SharedApiService.make(),
            company: {},
            editingSocialInfo: false,
            adminBaseUrl: '',
            loading: false,
            booleanOptions: [
                {id: true, value: "Yes"},
                {id: false, value: "No" }
            ],
            editingField: null,
            messages: {
                updateFailed: 'Oops, the server error occurred while updating data.',
                updateSuccess: 'Hurrah, the update went through successfully.',
            },
            alertTimeOutLimit: 3000,
            placeholder: {
                generic: '-',
                notSet: 'Not set',
            },
            userIsAdmin: false,
            profileTabs: [
                {id: 0, name: 'Basic Info'},
                {id: 1, name: 'Addresses'},
                {id: 2, name: 'Activity'},
                {id: 3, name: 'Contact & Social Links'},
                {id: 4, name: 'Billing & Plan Details'},
                {id: 5, name: 'Media & Attachments'},
                {id: 6, name: 'Configurable Fields'},
                {id: 7, name: 'Expert Review'},
                {id: 8, name: 'Business Hours'},
                {id: 9, name: 'Brands & Services'},
                {id: 10, name: 'Credentials'},
                {id: 11, name: 'Content'},
                {id: 12, name: 'Company Relationships'},

            ],
            selectedProfileTab: 0,
            searchString: '',
            allowToggleDisplayProfile: true,
            displayProfile: null,
            companyBilling: useCompanyBillingStore(),
            showArchived: false,
        }
    },
    created() {
        if (this.companyId) {
            this.getCompanyProfileData();
        }
        this.userIsAdmin = useRolesPermissions().hasRole('admin');
        this.companyBilling.getBillingVersion(this.companyId)
    },
    computed: {
        simpleIcon(){
            return useSimpleIcon()
        },
        searchIsHidden() {
            return [12].find(tabIndex => tabIndex === this.selectedProfileTab);
        }
    },
    methods: {
        toggleArchived(){
            this.showArchived = !this.showArchived
        },
        highlight(string) {
            if(!this.searchString) {
                return string;
            }
            return string.replace(new RegExp(this.searchString, "gi"), match => {
                if(this.darkMode) {
                    return '<span class="bg-yellow-800">' + match + '</span>';
                }
                else {
                    return '<span class="bg-yellow-500">' + match + '</span>';
                }
            });
        },
        legacyHref(section) {
            switch (section) {
                case 'reviews': return `${this.adminBaseUrl}/review/review-search?company_name=${encodeURIComponent(this.company.tradingName)}&review_status=any&email_validation=any&limit=10`;
                case 'tickets': return `${this.adminBaseUrl}/tickets.php?ticketcompanyid=${this.legacyCompanyId}`;
                case 'expertReviews': return `${this.adminBaseUrl}/companies/${this.legacyCompanyId}/expert-reviews`;
                case 'generateInvoice': return `${this.adminBaseUrl}/company.php?getcompanyid=${this.legacyCompanyId}&action=generateinvoice`;
                case 'attachments': return `${this.adminBaseUrl}/companies/${this.legacyCompanyId}/attachments`;
                case 'media': return `${this.adminBaseUrl}/companymedia.php?getcompanyid=${this.legacyCompanyId}`;
                case 'logoRectangle': return `${this.adminBaseUrl}/companies/${this.legacyCompanyId}/logo-rectangle`;
                case 'paymentMethods': return `${this.adminBaseUrl}/company.php?getcompanyid=${this.legacyCompanyId}`;
                case 'logoSquare': return `${this.adminBaseUrl}/company.php?getcompanyid=${this.legacyCompanyId}&action=logo`;
                case 'profileMedia': return `${this.adminBaseUrl}/companymedia.php?getcompanyid=${this.legacyCompanyId}`;
                case 'companyProfile': return `${this.adminBaseUrl}/company.php?getcompanyid=${this.legacyCompanyId}`;
                case 'leads': return `${this.adminBaseUrl}/quotes.php?action=Search&delivery=&fromdate=&getcompanyid=${this.legacyCompanyId}&&hours=24&itemsperpage=10&lead_category=&lead_industries%5B%5D=multi&lead_industries%5B%5D=roofing&lead_industries%5B%5D=solar&searchstatus=all`
                default: return ``;
            }
        },
        editSocialInfo() {
            this.editingSocialInfo = !this.editingSocialInfo;
        },
        getCompanyProfileData() {
            this.loading = true;
            this.api.getCompanyProfileData(this.companyId).then(resp => {
                this.company = resp.data.data.company;
                this.adminBaseUrl = resp.data.data.adminUrl;
                this.displayProfile = resp.data.data.display_profile;
            }).finally(() => {
                this.loading = false;
            });
        },
        formatBooleanValue(value) {
            if (value === null) return null;

            return value ? 'Yes' : 'No';
        },
        getFieldType(type) {
            return type === 'Boolean'
                ? 'select'
                : 'text';
        },
        editField(field) {
            this.editingField = field;
        },
        updateField(field, value) {
            this.api.updateCompanyConfigurableFields(this.companyId, {[field]: value}).then(resp => {
                if (resp.data.data.status === true) {
                    this.showAlert('success', this.messages.updateSuccess, this.alertTimeOutLimit);
                    this.company['companyFields'] = resp.data.data.updatedFields;
                    return;
                }
                this.showAlert('error', this.messages.updateFailed, this.alertTimeOutLimit);
            })
            .catch((e) => this.showAlert('error', e.response.data.message, this.alertTimeOutLimit))
            .finally(() => {
                this.editingField = null;
                this.getCompanyProfileData();
            });
        },
        selectProfileTab(profileTabId) {
            this.selectedProfileTab = profileTabId
        },
        handleActivateAlert(alertData) {
            this.$emit('activate-alert', alertData);
        },
        toggleDisplayProfile() {
            if(this.allowToggleDisplayProfile){
                this.allowToggleDisplayProfile = false
                this.api.toggleDisplayProfile(this.companyId)
                    .then(resp => this.displayProfile = resp.data.data.display_profile)
                    .finally(() => this.allowToggleDisplayProfile = true)
            }
        }
    }
}
</script>

<style scoped>

</style>
