<template>
    <div class="m-6">
        <div class="flex gap-2 items-center">
            <div class="w-72">
                <CompanySearchAutocomplete
                    :dark-mode="darkMode"
                    v-model="selectedCompany"
                    search-icon
                    enable-loading
                    @clear-search="selectedCompany = null"
                />
            </div>
            <div class="w-64">
                <Dropdown
                    :dark-mode="darkMode"
                    :options="companyStore.relationshipOptions"
                    v-model="selectedRelationship"
                />
            </div>
            <CustomButton dark-mode="dark-mode" class="ml-5" @click="creatRelationship" :disabled="loading || saving">
                Create
            </CustomButton>
        </div>
        <div class="mt-6">
            <div class="h-96 flex items-center" v-if="loading">
                <LoadingSpinner :dark-mode="darkMode" />
            </div>
            <div v-else class="text-sm">
                <div
                     class="grid grid-cols-3 font-semibold uppercase py-1 px-3 text-slate-400 border-b"
                     :class="[{'border-light-border': !darkMode, 'border-dark-border': darkMode}]"
                >
                    <p>Company</p>
                    <p>Relationship</p>
                </div>
                <div
                    v-for="relationship in relationships"
                    class="grid grid-cols-3 p-3 border-b"
                    :class="[{'border-light-border bg-light-background text-slate-900 hover:bg-light-module': !darkMode, 'border-dark-border bg-dark-background text-slate-200 hover:bg-dark-module': darkMode}]"
                >
                    <div class="flex">
                        <a :href="`/companies/${relationship.target_company.id}`" class="text-primary-500" target="_blank">
                            {{ relationship.target_company.name }}
                        </a>
                    </div>
                    <p>{{ relationship.relationship.label }}</p>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5 text-red-450 cursor-pointer"
                         :class="{'pointer-events-none text-red-200': saving}"
                         @click="deleteRelationship(relationship.id)">
                        <path stroke-linecap="round" stroke-linejoin="round"
                              d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"/>
                    </svg>
                </div>
            </div>
        </div>
        <AlertsContainer
            v-if="alertActive"
            :dark-mode="darkMode"
            :alert-type="alertType"
            :text="alertText"
        />
    </div>
</template>

<script>
import {useCompanyStore} from "../../../../stores/company/company.store.js";
import Dropdown from "../../Shared/components/Dropdown.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import CompanySearchAutocomplete from "../../Shared/components/Company/CompanySearchAutocomplete.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../../mixins/alerts-mixin.js";

export default {
    name: "CompanyRelationships",
    components: {AlertsContainer, LoadingSpinner, CompanySearchAutocomplete, CustomButton, Dropdown},
    mixins:[AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        hasEditRights: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            required: true
        }
    },
    data() {
        return {
            companyStore: useCompanyStore(),
            selectedRelationship: 1,
            selectedCompany: null,
            loading: false,
            saving: false,
            relationships: [],
        }
    },
    beforeMount() {
        this.getRelationships();
    },
    methods: {
        getRelationships() {
            this.loading = true

            this.companyStore.sharedApi.getCompanyRelationships(this.companyId)
                .then(resp => this.relationships = resp.data.data.relationships)
                .catch(e => console.error(e)).finally(() => this.loading = false)
        },
        creatRelationship() {
            if (!this.selectedCompany || !this.selectedRelationship) {
                this.showAlert('error', 'Please select both a company and a relationship type.');
                return;
            }

            if (this.selectedCompany === this.companyId) {
                this.showAlert('error', 'A company cannot have a relationship with itself.');
                return;
            }

            if (this.relationships.find(relationship => relationship.target_company.id === this.selectedCompany && relationship.relationship.value === this.selectedRelationship)) {
                this.showAlert('error', 'A relationship with the selected company already exists.')
                return;
            }

            this.saving = true;

            this.companyStore.sharedApi.createCompanyRelationships(this.companyId, {
                target_company_id: this.selectedCompany,
                relationship: this.selectedRelationship
            }).then(() => this.getRelationships())
                .catch(e => console.error(e))
                .finally(() => this.saving = false);
        },
        deleteRelationship(id) {
            this.saving = true;

            this.companyStore.sharedApi.deleteCompanyRelationships(this.companyId, id)
                .then(() => this.getRelationships())
                .catch(e => console.error(e))
                .finally(() => this.saving = false);
        }
    }
}
</script>
