<template>
    <div>
        <svg v-if="tab === 0" class="fill-current" :class="[tab === selected ? 'text-primary-500' : (darkMode ? 'text-slate-200' : 'text-slate-800')]" id="basic-info" width="16" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14.4 0H1.6C0.7176 0 0 0.697667 0 1.55556V12.4444C0 13.3023 0.7176 14 1.6 14H14.4C15.2824 14 16 13.3023 16 12.4444V1.55556C16 0.697667 15.2824 0 14.4 0ZM13.6 3.11111V4.66667H9.6V3.11111H13.6ZM9.6 6.22222H13.6V7.77778H9.6V6.22222ZM1.6 12.4444V1.55556H7.2V12.4444H1.6Z" />
        </svg>
        <svg v-if="tab === 1" class="fill-current" :class="[tab === selected ? 'text-primary-500' : (darkMode ? 'text-slate-200' : 'text-slate-800')]" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.00005 12.3333L8.80005 10.7333V6.61965C10.1768 6.26205 11.2 5.01965 11.2 3.53325C11.2 1.76845 9.76485 0.333252 8.00005 0.333252C6.23525 0.333252 4.80005 1.76845 4.80005 3.53325C4.80005 5.01965 5.82325 6.26205 7.20005 6.61965V10.7333L8.00005 12.3333Z" />
            <path d="M11.4136 7.18366L10.9872 8.72606C13.06 9.29886 14.4 10.4005 14.4 11.5333C14.4 13.0469 11.772 14.7333 8 14.7333C4.228 14.7333 1.6 13.0469 1.6 11.5333C1.6 10.4005 2.94 9.29886 5.0136 8.72526L4.5872 7.18286C1.7576 7.96526 0 9.63166 0 11.5333C0 14.2245 3.5144 16.3333 8 16.3333C12.4856 16.3333 16 14.2245 16 11.5333C16 9.63166 14.2424 7.96526 11.4136 7.18366Z" />
        </svg>
        <svg v-if="tab === 2" class="fill-current" :class="[tab === selected ? 'text-primary-500' : (darkMode ? 'text-slate-200' : 'text-slate-800')]" width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.11121 4.44434V8.88878H10.6668V7.111H8.53343V4.44434H7.11121Z" />
            <path d="M15.4336 4.34311C15.0727 3.4977 14.5489 2.72965 13.8912 2.08173C12.8956 1.09845 11.6302 0.424888 10.2512 0.144308C9.29407 -0.0481027 8.30751 -0.0481027 7.35039 0.144308C5.97013 0.423096 4.70357 1.09721 3.7088 2.08252C3.05271 2.73113 2.52906 3.49834 2.1664 4.34232C1.79128 5.21828 1.59865 6.15988 1.6 7.11097L1.6008 7.13072H0L2.4 10.2715L4.8 7.13072H3.2008L3.2 7.11097C3.19769 6.00902 3.53077 4.93182 4.156 4.01915C4.55929 3.43025 5.07276 2.92284 5.6688 2.52421C6.27515 2.12076 6.95529 1.83789 7.67119 1.6914C9.12602 1.39597 10.6401 1.68321 11.8806 2.48997C13.121 3.29674 13.9864 4.55699 14.2864 5.99371C14.4373 6.73003 14.4373 7.48875 14.2864 8.22506C14.14 8.93274 13.8535 9.60486 13.4432 10.2028C13.2432 10.4959 13.0136 10.7717 12.76 11.0214C12.2489 11.5255 11.6447 11.9281 10.98 12.2074C10.6415 12.3487 10.2896 12.4567 9.92959 12.5297C9.18434 12.6787 8.41644 12.6787 7.67119 12.5297C6.95543 12.3848 6.27557 12.1021 5.67039 11.6977C5.37276 11.4991 5.09497 11.2729 4.8408 11.0222L3.7096 12.1394C4.3777 12.8002 5.17122 13.3243 6.04471 13.6817C6.91821 14.0391 7.85451 14.2228 8.79999 14.2222C9.76243 14.2218 10.7151 14.0319 11.6024 13.6636C12.8885 13.1262 13.9888 12.2313 14.7696 11.0877C15.5732 9.91351 16.0017 8.52826 16 7.11097C16.002 6.16007 15.8093 5.2186 15.4336 4.34311Z" />
        </svg>
        <svg v-if="tab === 3" class="fill-current" :class="[tab === selected ? 'text-primary-500' : (darkMode ? 'text-slate-200' : 'text-slate-800')]" width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13.3 0H2.8C2.4287 0 2.0726 0.136964 1.81005 0.380761C1.5475 0.624558 1.4 0.955218 1.4 1.3V3.25H0V4.55H1.4V5.85H0V7.15H1.4V8.45H0V9.75H1.4V11.7C1.4 12.0448 1.5475 12.3754 1.81005 12.6192C2.0726 12.863 2.4287 13 2.8 13H13.3C13.4857 13 13.6637 12.9315 13.795 12.8096C13.9263 12.6877 14 12.5224 14 12.35V0.65C14 0.477609 13.9263 0.312279 13.795 0.190381C13.6637 0.0684819 13.4857 0 13.3 0ZM7.7 1.94935C8.8536 1.94935 9.8 2.8275 9.8 3.89935C9.79797 4.416 9.57609 4.91097 9.18273 5.27636C8.78936 5.64175 8.25639 5.84794 7.7 5.85C6.5471 5.85 5.6 4.97055 5.6 3.89935C5.6 2.8275 6.5471 1.94935 7.7 1.94935ZM11.9 10.4H3.5V9.9125C3.5 8.47015 5.3935 6.9875 7.7 6.9875C10.0065 6.9875 11.9 8.47015 11.9 9.9125V10.4Z" />
        </svg>
        <svg v-if="tab === 4" class="fill-current" :class="[tab === selected ? 'text-primary-500' : (darkMode ? 'text-slate-200' : 'text-slate-800')]" width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13.5 0H1.5C0.67275 0 0 0.67275 0 1.5V10.5C0 11.3273 0.67275 12 1.5 12H13.5C14.3273 12 15 11.3273 15 10.5V1.5C15 0.67275 14.3273 0 13.5 0ZM7.875 9C7.37772 9 6.90081 8.80246 6.54917 8.45082C6.19754 8.09919 6 7.62228 6 7.125C6 6.62772 6.19754 6.15081 6.54917 5.79917C6.90081 5.44754 7.37772 5.25 7.875 5.25C8.282 5.25094 8.67737 5.38589 9 5.634C8.547 5.976 8.25 6.51375 8.25 7.125C8.25 7.73625 8.547 8.274 9 8.616C8.67737 8.86411 8.282 8.99906 7.875 9ZM10.875 9C10.3777 9 9.90081 8.80246 9.54918 8.45082C9.19754 8.09919 9 7.62228 9 7.125C9 6.62772 9.19754 6.15081 9.54918 5.79917C9.90081 5.44754 10.3777 5.25 10.875 5.25C11.3723 5.25 11.8492 5.44754 12.2008 5.79917C12.5525 6.15081 12.75 6.62772 12.75 7.125C12.75 7.62228 12.5525 8.09919 12.2008 8.45082C11.8492 8.80246 11.3723 9 10.875 9Z"/>
        </svg>
        <svg v-if="tab === 5" class="fill-current" :class="[tab === selected ? 'text-primary-500' : (darkMode ? 'text-slate-200' : 'text-slate-800')]" width="16" height="13" viewBox="0 0 16 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M0 3C0 1.34315 1.34315 0 3 0H13C14.6569 0 16 1.34315 16 3V10C16 11.6569 14.6569 13 13 13H3C1.34315 13 0 11.6569 0 10V3ZM6.688 7.8288L5.77778 6.6L4 9H12L8.88889 5L6.688 7.8288ZM7 4.5C7 5.32843 6.32843 6 5.5 6C4.67157 6 4 5.32843 4 4.5C4 3.67157 4.67157 3 5.5 3C6.32843 3 7 3.67157 7 4.5Z"/>
        </svg>
        <svg v-if="tab === 6" class="fill-current" :class="[tab === selected ? 'text-primary-500' : (darkMode ? 'text-slate-200' : 'text-slate-800')]" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0.102529 9.95325L1.63466 12.5483C1.73637 12.7204 1.90375 12.8459 2.09999 12.8972C2.29622 12.9486 2.50524 12.9215 2.6811 12.822L3.75053 12.2175C4.19484 12.5603 4.68589 12.8415 5.20222 13.0515V14.25C5.20222 14.4489 5.28293 14.6397 5.42659 14.7803C5.57026 14.921 5.76511 15 5.96828 15H9.03254C9.23571 15 9.43056 14.921 9.57423 14.7803C9.71789 14.6397 9.7986 14.4489 9.7986 14.25V13.0515C10.319 12.8394 10.8074 12.5588 11.2503 12.2175L12.3197 12.822C12.6851 13.0283 13.1555 12.9045 13.3662 12.5483L14.8983 9.95325C14.9991 9.7809 15.0263 9.57653 14.9739 9.38464C14.9216 9.19275 14.7939 9.0289 14.6187 8.92875L13.5676 8.334C13.6498 7.78087 13.6492 7.21898 13.5661 6.666L14.6171 6.07125C14.9818 5.865 15.1082 5.40375 14.8968 5.04675L13.3646 2.45175C13.2629 2.27962 13.0955 2.15409 12.8993 2.10276C12.7031 2.05142 12.494 2.07849 12.3182 2.178L11.2488 2.7825C10.8064 2.44078 10.3182 2.16016 9.79784 1.9485V0.75C9.79784 0.551088 9.71713 0.360322 9.57346 0.21967C9.4298 0.0790176 9.23494 0 9.03177 0H5.96752C5.76434 0 5.56949 0.0790176 5.42583 0.21967C5.28216 0.360322 5.20145 0.551088 5.20145 0.75V1.9485C4.68108 2.16061 4.19269 2.44119 3.74976 2.7825L2.6811 2.178C2.59405 2.12863 2.49791 2.09654 2.39818 2.08358C2.29846 2.07061 2.1971 2.07703 2.0999 2.10246C2.00271 2.12788 1.91159 2.17182 1.83176 2.23175C1.75193 2.29169 1.68495 2.36645 1.63466 2.45175L0.102529 5.04675C0.00170565 5.2191 -0.0254808 5.42347 0.0268899 5.61536C0.0792607 5.80725 0.206947 5.9711 0.382142 6.07125L1.43318 6.666C1.35055 7.21906 1.35055 7.78094 1.43318 8.334L0.382142 8.92875C0.0174959 9.135 -0.108905 9.59625 0.102529 9.95325V9.95325ZM7.49964 4.5C9.18958 4.5 10.5639 5.8455 10.5639 7.5C10.5639 9.1545 9.18958 10.5 7.49964 10.5C5.80971 10.5 4.43539 9.1545 4.43539 7.5C4.43539 5.8455 5.80971 4.5 7.49964 4.5Z" />
        </svg>
        <svg v-if="tab === 7" class="fill-current" :class="[tab === selected ? 'text-primary-500' : (darkMode ? 'text-slate-200' : 'text-slate-800')]" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z"/>
        </svg>

<!--    todo: replace with appropriate icons   -->
        <svg v-if="tab === 8" class="fill-current" :class="[tab === selected ? 'text-primary-500' : (darkMode ? 'text-slate-200' : 'text-slate-800')]" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 16C10.1217 16 12.1566 15.1571 13.6569 13.6569C15.1571 12.1566 16 10.1217 16 8C16 5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842855 10.1217 0 8 0C5.87827 0 3.84344 0.842855 2.34315 2.34315C0.842855 3.84344 0 5.87827 0 8C0 10.1217 0.842855 12.1566 2.34315 13.6569C3.84344 15.1571 5.87827 16 8 16ZM9 4C9 3.73478 8.89464 3.48043 8.70711 3.29289C8.51957 3.10536 8.26522 3 8 3C7.73478 3 7.48043 3.10536 7.29289 3.29289C7.10536 3.48043 7 3.73478 7 4V8C7.00006 8.26519 7.10545 8.51951 7.293 8.707L10.121 11.536C10.2139 11.6289 10.3242 11.7026 10.4456 11.7529C10.567 11.8032 10.6971 11.8291 10.8285 11.8291C10.9599 11.8291 11.09 11.8032 11.2114 11.7529C11.3328 11.7026 11.4431 11.6289 11.536 11.536C11.6289 11.4431 11.7026 11.3328 11.7529 11.2114C11.8032 11.09 11.8291 10.9599 11.8291 10.8285C11.8291 10.6971 11.8032 10.567 11.7529 10.4456C11.7026 10.3242 11.6289 10.2139 11.536 10.121L9 7.586V4Z"/>
        </svg>
        <svg v-if="tab === 9" class="fill-current" :class="[tab === selected ? 'text-primary-500' : (darkMode ? 'text-slate-200' : 'text-slate-800')]" width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M16.208 7.65946C16.3954 7.84698 16.5008 8.10129 16.5008 8.36646C16.5008 8.63162 16.3954 8.88593 16.208 9.07345L9.20798 16.0735C9.02045 16.2609 8.76614 16.3662 8.50098 16.3662C8.23581 16.3662 7.9815 16.2609 7.79398 16.0735L0.793977 9.07345C0.700979 8.98072 0.62722 8.87052 0.576938 8.74919C0.526656 8.62786 0.500841 8.49779 0.500977 8.36646V3.36646C0.500977 2.57081 0.817048 1.80774 1.37966 1.24513C1.94227 0.682526 2.70533 0.366455 3.50098 0.366455H8.50098C8.75698 0.366455 9.01298 0.464455 9.20798 0.659455L16.208 7.65946ZM3.50098 4.36646C3.76619 4.36646 4.02055 4.2611 4.20808 4.07356C4.39562 3.88603 4.50098 3.63167 4.50098 3.36646C4.50098 3.10124 4.39562 2.84688 4.20808 2.65935C4.02055 2.47181 3.76619 2.36646 3.50098 2.36646C3.23576 2.36646 2.98141 2.47181 2.79387 2.65935C2.60633 2.84688 2.50098 3.10124 2.50098 3.36646C2.50098 3.63167 2.60633 3.88603 2.79387 4.07356C2.98141 4.2611 3.23576 4.36646 3.50098 4.36646Z"/>
        </svg>
        <svg v-if="tab === 10" class="fill-current" :class="[tab === selected ? 'text-primary-500' : (darkMode ? 'text-slate-200' : 'text-slate-800')]" width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M4.99673 0.746858C5.47493 0.268653 6.12352 0 6.7998 0H10.1997C10.876 0 11.5246 0.268653 12.0028 0.746858C12.481 1.22506 12.7496 1.87365 12.7496 2.54993V3.39991H14.4496C15.1259 3.39991 15.7745 3.66856 16.2527 4.14677C16.7309 4.62497 16.9995 5.27356 16.9995 5.94984V10.3967C16.9998 10.4097 16.9998 10.4227 16.9995 10.4357V14.4496C16.9995 15.1259 16.7309 15.7745 16.2527 16.2527C15.7745 16.7309 15.1259 16.9995 14.4496 16.9995H2.54991C1.87363 16.9995 1.22504 16.7309 0.746839 16.2527C0.268634 15.7745 -1.91223e-05 15.1259 -1.91223e-05 14.4496V10.4357C-0.000318437 10.4227 -0.000319856 10.4097 -1.91223e-05 10.3967V5.94984C-1.91223e-05 5.27356 0.268634 4.62497 0.746839 4.14677C1.22504 3.66856 1.87363 3.39991 2.54991 3.39991H4.24987V2.54993C4.24987 1.87365 4.51852 1.22506 4.99673 0.746858ZM2.54991 5.09986C2.32449 5.09986 2.10829 5.18942 1.94889 5.34882C1.78949 5.50822 1.69994 5.72441 1.69994 5.94984V9.83451C3.8172 10.62 6.10704 11.0497 8.49976 11.0497L8.50079 11.0497C10.8228 11.0525 13.1252 10.6404 15.2996 9.83434V5.94984C15.2996 5.72441 15.21 5.50822 15.0506 5.34882C14.8912 5.18942 14.675 5.09986 14.4496 5.09986H2.54991ZM11.0497 3.39991H5.94982V2.54993C5.94982 2.3245 6.03937 2.10831 6.19878 1.94891C6.35818 1.78951 6.57437 1.69995 6.7998 1.69995H10.1997C10.4251 1.69995 10.6413 1.78951 10.8007 1.94891C10.9601 2.10831 11.0497 2.3245 11.0497 2.54993V3.39991ZM15.2996 11.6375C13.1113 12.3758 10.8143 12.7524 8.49924 12.7497C6.12216 12.7496 3.83572 12.3587 1.69994 11.6377V14.4496C1.69994 14.675 1.78949 14.8912 1.94889 15.0506C2.10829 15.21 2.32449 15.2996 2.54991 15.2996H14.4496C14.675 15.2996 14.8912 15.21 15.0506 15.0506C15.21 14.8912 15.2996 14.675 15.2996 14.4496V11.6375ZM7.64978 9.34975C7.64978 8.88032 8.03033 8.49977 8.49976 8.49977H8.50826C8.97769 8.49977 9.35823 8.88032 9.35823 9.34975C9.35823 9.81918 8.97769 10.1997 8.50826 10.1997H8.49976C8.03033 10.1997 7.64978 9.81918 7.64978 9.34975Z"/>
        </svg>
        <svg v-if="tab === 11" class="fill-current" :class="[tab === selected ? 'text-primary-500' : (darkMode ? 'text-slate-200' : 'text-slate-800')]" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 1C0 0.734784 0.105357 0.48043 0.292893 0.292893C0.48043 0.105357 0.734784 0 1 0H13C13.2652 0 13.5196 0.105357 13.7071 0.292893C13.8946 0.48043 14 0.734784 14 1V3C14 3.26522 13.8946 3.51957 13.7071 3.70711C13.5196 3.89464 13.2652 4 13 4H1C0.734784 4 0.48043 3.89464 0.292893 3.70711C0.105357 3.51957 0 3.26522 0 3V1ZM0 7C0 6.73478 0.105357 6.48043 0.292893 6.29289C0.48043 6.10536 0.734784 6 1 6H7C7.26522 6 7.51957 6.10536 7.70711 6.29289C7.89464 6.48043 8 6.73478 8 7V13C8 13.2652 7.89464 13.5196 7.70711 13.7071C7.51957 13.8946 7.26522 14 7 14H1C0.734784 14 0.48043 13.8946 0.292893 13.7071C0.105357 13.5196 0 13.2652 0 13V7ZM11 6C10.7348 6 10.4804 6.10536 10.2929 6.29289C10.1054 6.48043 10 6.73478 10 7V13C10 13.2652 10.1054 13.5196 10.2929 13.7071C10.4804 13.8946 10.7348 14 11 14H13C13.2652 14 13.5196 13.8946 13.7071 13.7071C13.8946 13.5196 14 13.2652 14 13V7C14 6.73478 13.8946 6.48043 13.7071 6.29289C13.5196 6.10536 13.2652 6 13 6H11Z"/>
        </svg>
        <svg v-if="tab === 12" class="fill-current h-5 w-5" :class="[tab === selected ? 'text-primary-500' : (darkMode ? 'text-slate-200' : 'text-slate-800')]"
             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 21 3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5"/>
        </svg>

    </div>
</template>

<script>
export default {
    name: "CompanyProfileIcons",
    props: {
        tab: {
            type: Number,
            default: 0
        },
        selected: {
            type: Number,
            default: 0
        },
        darkMode: {
            type: Boolean,
            default: false
        }
    }
}
</script>

<style scoped>

</style>
