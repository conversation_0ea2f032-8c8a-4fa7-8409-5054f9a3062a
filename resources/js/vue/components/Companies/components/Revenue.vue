<template>
    <div class="border rounded-lg overflow-hidden"
         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
    >
        <div class="p-2 flex items-center gap-1">
            <labeled-value :dark-mode="darkMode" class="flex-1">
                <template #label>
                    <div class="flex items-center gap-1">
                        <p :class="[darkMode ? 'text-white' : 'text-black']"
                        >
                            View by
                        </p>
                        <p
                            v-if="revenueOverview.requested_at"
                            class="text-xs"
                            :class="[darkMode ? 'text-white' : 'text-black']"
                        >
                            (Last update: <time-ago :date="revenueOverview.requested_at" :dark-mode="darkMode" vertical-align="bottom"/>)
                        </p>
                    </div>
                </template>
                <template #default>
                    <Dropdown
                        v-model="viewBy"
                        :dark-mode="darkMode"
                        :options="viewByOptions"
                    />
                </template>
            </labeled-value>
        </div>
        <div class="flex">
            <div
                class="flex-1 border-l border-b grid grid-cols-4 divide-x"
                :class="[darkMode ? 'divide-dark-border border-dark-border bg-dark-background text-slate-100' : 'text-slate-900 divide-light-border border-light-border bg-light-background']"
            >
                <div
                    v-for="(card, index) in revenueCards"
                    :key="index"
                    class="p-5"
                >
                    <p class="text-sm font-semibold pb-0">
                        {{ card.title }}
                    </p>
                    <p v-if="!loadingSummary" class="text-xs pb-0">
                        {{ card.subtitle }}
                    </p>
                    <p v-if="card.dateRangeKey && !loadingSummary" class="text-xs pb-2">
                        {{ revenueOverview?.[card.dateRangeKey] ?? card.dateRangeKey }} - Now
                    </p>
                    <div v-if="loadingSummary" class="mt-2">
                        <div class="h-6 w-24 rounded animate-pulse" :class="[
                            darkMode ? 'bg-dark-module' : 'bg-gray-200'
                        ]"></div>
                    </div>
                    <div
                        v-else
                        class="flex items-center gap-1"
                    >
                        <p :class="card.colorClass" class="text-lg font-semibold">
                            {{ $filters.currency(revenueOverview[card.valueKey]) }}
                        </p>
                        <p class="text-xs">
                            ({{ revenueOverview[card.countKey] }})
                        </p>
                    </div>

                </div>
            </div>
        </div>
        <div class="p-8">
            <div class="flex justify-between">
                <div class="flex gap-x-5">
                    <p
                        v-for="(option, i) in options"
                        :key="i"
                        class="uppercase text-sm tracking-wide font-bold"
                        :class="{
                                'text-primary-500':
                                  option.period === 'all-time'
                                    ? period === 'all-time'
                                    : period === option.period && duration === option.duration,
                                'text-slate-500':
                                  option.period === 'all-time'
                                    ? period !== 'all-time'
                                    : period !== option.period || duration !== option.duration,
                                'cursor-pointer': !loading
                           }"
                        @click="setPeriod(option.period, option.duration)"
                    >
                        {{ option.label }}
                    </p>
                </div>
                <div>
                    <dropdown
                        v-model="industryId"
                        :options="industryOptions"
                        placeholder="Industry"
                        @update:modelValue="handleIndustryChange"
                    />
                </div>
            </div>
            <div class="w-full h-88 mt-4 flex items-center justify-center">
                <line-chart
                    class="flex-1"
                    :class="[loading ? 'blur pointer-events-none' : '']"
                    :chart-data="graphData.data"
                />
            </div>
        </div>
    </div>
</template>

<script>
import Tab from "../../Shared/components/Tab.vue";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import ApiService from "../services/api";
import BarChart from "../../Shared/components/BarChart.vue";
import Api from "../../Billing/services/company-revenue.js";
import LineChart from "../../Shared/components/LineChart.vue";
import SimpleCard from "../../MarketingCampaign/SimpleCard.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import CustomCheckbox from "../../Shared/SlideWizard/components/CustomCheckbox.vue";
import TimeAgo from "../../Shared/components/TimeAgo.vue";


function generateGraphData() {
    const labels = [
        "August, 2024",
        "September, 2024",
        "October, 2024",
        "November, 2024",
        "December, 2024",
        "January, 2025",
        "February, 2025",
        "March, 2025",
        "April, 2025",
        "May, 2025",
        "June, 2025",
        "July, 2025",
        "August, 2025"
    ];

    function randomize(base, variation) {
        return Math.round(base + (Math.random() * variation * 2 - variation));
    }

    const datasets = [
        {
            label: "Chargeable + Delivered",
            base: 1100,
            variation: 1000,
            borderColor: "rgba(79, 70, 229, 0.3)",
            backgroundColor: "rgba(79, 70, 229, 0.1)"
        },
        {
            label: "Paid",
            base: 1080,
            variation: 90,
            borderColor: "rgba(34, 197, 94, 0.3)",
            backgroundColor: "rgba(34, 197, 94, 0.1)"
        },
        {
            label: "Rejected",
            base: 1400,
            variation: 500,
            borderColor: "rgba(239, 68, 68, 0.3)",
            backgroundColor: "rgba(239, 68, 68, 0.1)"
        },
        {
            label: "Cancelled",
            base: 1650,
            variation: 400,
            borderColor: "rgba(234, 179, 8, 0.3)",
            backgroundColor: "rgba(234, 179, 8, 0.1)"
        }
    ];

    const resultDatasets = datasets.map(ds => {
        const data = labels.map(() => randomize(ds.base, ds.variation));
        return {
            ...ds,
            fill: false,
            tension: 0.4,
            pointRadius: 5,
            pointHoverRadius: 7,
            pointBorderColor: "#fff",
            pointBackgroundColor: ds.borderColor,
            borderWidth: 2,
            data
        };
    });

    return {
        labels,
        datasets: resultDatasets
    };
}

const DATA_PLACEHOLDER = generateGraphData();

export default {
    name: "Revenue",
    components: {TimeAgo, CustomCheckbox, LabeledValue, Dropdown, SimpleCard, LineChart, BarChart, LoadingSpinner, Tab},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        }
    },
    data() {
        return {
            industryId: 'all',
            industryOptions: [],
            revenueOverview: {},
            loading: false,
            viewBy: 'billable',
            viewByOptions: [
                {
                    id: 'paid_leads',
                    name: 'Paid Leads',
                },
                {
                    id: 'billable',
                    name: 'Chargeable and Delivered',
                }
            ],
            title: 'Revenue',
            labels: null,
            statistics: null,
            period: "monthly",
            duration: 1,
            api: ApiService.make(),
            revenueApiV2: Api.make(),
            loadingSummary: false,
            options: [
                {label: '1 M', period: 'monthly', duration: 1},
                {label: '3 M', period: 'monthly', duration: 3},
                {label: '6 M', period: 'monthly', duration: 6},
                {label: '1 Y', period: 'monthly', duration: 12},
                {label: '3 Y', period: 'monthly', duration: 3 * 12},
                {label: '5 Y', period: 'monthly', duration: 5 * 12},
                {label: 'All Time', period: 'all-time', duration: 1},
            ],
            graphData: {
                data: DATA_PLACEHOLDER,
                industry_options: []
            }
        }
    },
    mounted() {
        this.getRevenueSummary()
        this.getRevenueGraphData()
    },
    methods: {
        handleIndustryChange() {
            this.getRevenueGraphData()
        },
        async getRevenueGraphData() {
            this.loading = true
            try {
                const data = await this.revenueApiV2.getRevenueGraph(this.companyId, {
                    period: this.period,
                    duration: this.duration,
                    industry_id: this.industryId !== 'all' ? this.industryId : null
                })
                this.graphData = data.data
                this.industryOptions = [
                    {id: 'all', name: 'All Industries'},
                    ...data.data.industry_options
                ]
            } catch (err) {
                console.error(err)
            }
            this.loading = false
        },
        async getRevenueSummary() {
            this.loadingSummary = true
            try {
                const data = await this.api.getRevenueOverview(this.companyId)

                this.revenueOverview = data.data.data
            } catch (err) {
                console.error(err)
            }

            this.loadingSummary = false
        },
        setPeriod(period, duration) {
            if (this.loading) {
                return;
            }

            this.period = period;
            this.duration = duration;

            this.getRevenueGraphData();
        },
    },
    computed: {
        revenueCards() {
            return this.viewBy ==='paid_leads' ? [
                {
                    title: 'Revenue All-time',
                    subtitle: '(Paid Leads)',
                    dateRangeKey: 'First Delivery',
                    valueKey: 'total_paid_all_time',
                    countKey: 'count_paid_all_time',
                    colorClass: 'text-green-500',
                },
                {
                    title: 'Last 30 Days',
                    subtitle: '(Paid Leads)',
                    dateRangeKey: 'last_30_days_start_date',
                    valueKey: 'paid_last_30_days',
                    countKey: 'count_last_30_days',
                },
                {
                    title: 'Last 6 Months',
                    subtitle: '(Paid Leads)',
                    dateRangeKey: 'last_6_months_start_date',
                    valueKey: 'total_paid_last_6_months',
                    countKey: 'count_paid_last_6_months',
                },
                {
                    title: 'Year to Date',
                    subtitle: '(Paid Leads)',
                    dateRangeKey: 'start_of_year_start_date',
                    valueKey: 'total_paid_year_to_date',
                    countKey: 'count_paid_year_to_date',
                }
            ] : [
                {
                    title: 'Revenue All-time',
                    subtitle: '(Chargeable and Delivered)',
                    dateRangeKey: 'First Delivery',
                    valueKey: 'total_billable_all_time',
                    countKey: 'count_billable_all_time',
                    colorClass: 'text-green-500',
                },
                {
                    title: 'Last 30 Days',
                    subtitle: '(Chargeable and Delivered)',
                    dateRangeKey: 'last_30_days_start_date',
                    valueKey: 'total_billable_last_30_days',
                    countKey: 'count_billable_last_30_days',
                },
                {
                    title: 'Last 6 Months',
                    subtitle: '(Chargeable and Delivered)',
                    dateRangeKey: 'last_6_months_start_date',
                    valueKey: 'total_billable_last_6_months',
                    countKey: 'count_billable_last_6_months',
                },
                {
                    title: 'Year to Date',
                    subtitle: '(Chargeable and Delivered)',
                    dateRangeKey: 'start_of_year_start_date',
                    valueKey: 'total_billable_year_to_date',
                    countKey: 'count_billable_year_to_date',
                }
            ]
        }
    }
}
</script>

<style scoped>
.skeleton {
    @apply relative overflow-hidden bg-gray-200 rounded;
}
.skeleton::after {
    content: "";
    @apply absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/40 to-transparent animate-[shimmer_1.5s_infinite];
}
@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

</style>
