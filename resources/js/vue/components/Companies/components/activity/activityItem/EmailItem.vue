<template>
    <div class="rounded-md mt-4 text-sm">
<!--        header -->
        <div class="flex justify-between px-2">
            <div class="flex">
<!--                <svg @click="" class="w-4" :class="{'transform transition duration-200 rotate-90' : true}" width="6" height="10" viewBox="0 0 6 10" fill="none" xmlns="http://www.w3.org/2000/svg">-->
<!--                    <path d="M1 9L5 5L1 1" stroke="#0081FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>-->
<!--                </svg>-->
                <p><span class="font-bold">Email - {{email.subject}}</span> from {{email.from_address}}</p>
            </div>
            <div class="w-1/3 grid grid-cols-3 gap-2">
<!--                todo add support for whatever pin does -->
<!--                <div class="text-primary-500">-->
<!--                    Pin-->
<!--                </div>-->
<!--                <div class="text-primary-500">-->
<!--                    Copy Link-->
<!--                </div>-->
                <div>
                    {{ email.created_at }}
                </div>
            </div>
        </div>
        <div>
<!--        contact info and buttons-->
            <div class="flex justify-between p-5">
                <div class="flex">
<!--                    todo link the sender to a user and get there information to render here-->
<!--                    <div>-->
<!--                        <img src="" class="rounded-full h-10 w-10">-->
<!--                    </div>-->
                    <div>
<!--                        <div @click="receiverDropDown = !receiverDropDown" class="flex">-->
<!--                            <p>{{email.sender_name}}</p>-->
<!--                            <svg @click="" class="w-4" :class="{'transform transition duration-200 rotate-90' : true}" width="6" height="10" viewBox="0 0 6 10" fill="none" xmlns="http://www.w3.org/2000/svg">-->
<!--                                <path d="M1 9L5 5L1 1" stroke="#0081FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>-->
<!--                            </svg>-->
<!--                        </div>-->
                        <div>
                            <p>to {{email.to_address}}</p>
<!--                            todo get the email receiver user details and render-->
                            <div v-if="receiverDropDown">
                            </div>
                        </div>
                    </div>
                </div>
<!--                todo support email sending - reply-->
<!--                <div class="flex grid grid-cols-3 gap-2">-->
<!--                    <div class="text-primary-500">-->
<!--                        Reply-->
<!--                    </div>-->
<!--                    <div class="text-primary-500">-->
<!--                        Forward-->
<!--                    </div>-->
<!--                    <div class="text-primary-500">-->
<!--                        Delete-->
<!--                    </div>-->
<!--                </div>-->
            </div>
<!--            <div class="flex">-->
<!--                <div class="w-4 p-1 mr-4 cursor-pointer">-->
<!--                    <svg @click="" class="w-4" :class="{'transform transition duration-200 rotate-90' : false}" width="6" height="10" viewBox="0 0 6 10" fill="none" xmlns="http://www.w3.org/2000/svg">-->
<!--                        <path d="M1 9L5 5L1 1" stroke="#0081FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>-->
<!--                    </svg>-->
<!--                </div>-->
<!--                <div>-->
<!--                    Opens: {{email.opens}}-->
<!--                </div>-->
<!--                <div>-->
<!--                    Clicks: {{email.clicks}}-->
<!--                </div>-->
<!--            </div>-->
            <!--    body -->
            <div class="p-5">
                <div>
                    <div v-html="email.body"></div>
                </div>
            </div>
<!--            todo threads-->
<!--            <div>-->
<!--                <div @click="showThread = !showThread" v-if="email.thread">-->
<!--                    View Thread ({{ email.thread.length }})-->
<!--                </div>-->
<!--                <div v-if="showThread">-->

<!--                </div>-->
<!--            </div>-->
        </div>
    </div>
</template>

<script>
export default {
    name: "EmailItem",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        itemData: {
            type: Object,
            default: {}
        },
    },
    data() {
        return {
            email: {
                from_address: null,
                to_address: null,
                subject: null,
                body: null,
                created_at: null,
                updated_at: null,
            },
            receiverDropDown: false,
            showThread: false,
        }
    },
    computed: {

    },
    mounted() {
        Object.assign(this.email, this.itemData);
        this.email.created_at = new Date(this.email.created_at).toDateString();
    },
}

</script>

<style scoped>

</style>
