<template>
    <div class="border-b relative p-5 group" :class="[darkMode ? (expanded ? 'bg-dark-module border-dark-border text-slate-50' : 'bg-dark-background hover:bg-dark-module border-dark-border text-slate-50') : (expanded ? 'bg-light-module border-light-border text-slate-900' : 'bg-light-background hover:bg-light-module border-light-border text-slate-900')]"
    >
        <header @click="toggleExpand()" class="cursor-pointer">
            <div class="flex items-center">
                <svg class="w-3 h-3 mr-2" :class="{'transform transition duration-200 rotate-90' : expanded }" viewBox="0 0 6 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 9L5 5L1 1" stroke="#0081FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <p class="text-sm font-semibold text-primary-400 mr-2 capitalize">{{ activityType }}</p>
                <p class="text-xs" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">{{activity.item?.type === 'call' || activity.item?.type === 'text' ? direction : 'created by'}}<span :class="[darkMode ? 'text-slate-300' : 'text-slate-800']" class="ml-2 font-medium">{{activity.user?.name}}</span></p>
            </div>
            <p class="text-xs text-slate-500">
                <span :title="createdTimestamp"> created {{ createdTimeAgo }} </span> {{ createDateTime }}
                <span v-if="activity.created_at !== activity.updated_at" :title="updatedTimestamp" class="block">updated {{ updatedTimeAgo }}</span>
            </p>
        </header>
        <div class="relative transition-[max-height] duration-200"
            :class="{ 'border-dark-border': darkMode, 'border-light-border': !darkMode,  'overflow-hidden h-0' : !expanded }">
            <component class="py-2 w-full"
                       :is="getComponent"
                       :item-data="activity.item.data ?? {}"
                       :user-data="activity.user ?? {}"
                       :dark-mode="darkMode"
                       :company-id="companyId"
                       :expanded="expanded"
                       @reload="reload">
            </component>

        </div>
        <footer class="relative">
            <ActivityComments
                :comment-count="activity.comment_count"
                :expanded="expanded"
                :activity-id="activity.id"
                :company-id="companyId"
                :dark-mode="darkMode"
            />
        </footer>
        <div @click="toggleExpand()"
             class="absolute left-0 bottom-0 w-full h-2/3 transition-[max-height] duration-200"
             :class="{ 'bg-gradient-to-t from-dark-background group-hover:from-dark-module': darkMode, 'bg-gradient-to-t from-light-background group-hover:from-light-module': !darkMode, 'cursor-pointer' : !expanded, [`opacity-0 pointer-events-none cursor-default`]: expanded }" />
    </div>

</template>

<script>
import ActivityComments from "../comments/ActivityComments.vue";
import CallItem from "./CallItem.vue";
import ActionItem from "./ActionItem.vue";
import TaskItem from "./TaskItem.vue";
import TextItem from "./TextItem.vue";
import EmailItem from "./EmailItem.vue";
import MailboxEmailItem from "./MailboxEmailItem.vue";
import {DateTime} from "luxon";

const componentMap = {
    'call': CallItem,
    'action': ActionItem,
    'task': TaskItem,
    'text': TextItem,
    'email': EmailItem,
    'mailbox_email': MailboxEmailItem
}

export default {
    name: "ActivityItem",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        activity: {
            type: Object,
            default: {},
        },
        companyId: {
            type: Number,
            default: null,
        },
    },
    computed: {
        createdTimeAgo() { return this.$filters.simpleRelativeTimeFromTimestamp(this.activity.created_at); },
        createdTimestamp() { return this.$filters.absoluteDatetimeFromTimestamp(this.activity.created_at); },
        updatedTimeAgo() { return  this.$filters.simpleRelativeTimeFromTimestamp(this.activity.updated_at); },
        updatedTimestamp() { return this.$filters.absoluteDatetimeFromTimestamp(this.activity.updated_at); },
        createDateTime() {
            if (!this.activity.item?.data?.created_at) return null;

            return '(' + DateTime.fromISO(this.activity.item.data.created_at).toFormat('DD hh:mm a') + ')';
        },
        activityType() {
            if (!this.activity.item?.type) return 'Unknown Activity';
            if (this.activity.item.type === 'call' || this.activity.item.type === 'text') return `${this.activity.item.data?.direction} ${this.activity.item.type}`;
            if (this.activity.item.type === 'mailbox_email') return 'Mailbox Email'

            return this.activity.item.type;
        },
        direction () { return this.activity.item?.data?.direction === 'inbound' ? 'to' : 'from'; },
        getComponent() {
            return componentMap[this.activity.item?.type] ?? 'div';
        }
    },
    data() {
        return {
            expanded: false,
            comments: null,
        }
    },
    emits: [
        "reload"
    ],
    components: {
        ActivityComments,
        CallItem,
        ActionItem,
        TextItem,
        TaskItem,
        EmailItem
    },
    methods: {
        toggleExpand() {
            this.expanded = !this.expanded;
        },
        reload() {
            this.$emit('reload');
        }
    }
}
</script>

<style scoped>

</style>
