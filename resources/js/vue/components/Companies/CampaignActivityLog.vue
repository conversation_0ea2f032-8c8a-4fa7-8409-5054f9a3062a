<template>
    <div class="min-h-96 mr-3">
        <div class="h-96 flex items-center" v-if="loading">
            <LoadingSpinner :dark-mode="darkMode"/>
        </div>
        <div v-for="log in logs" class="my-3" v-else>
            <div class="flex justify-between items-center text-sm">
                <div class="flex gap-2 items-center mb-1">
                    <div class="h-3 w-3 rounded-full border" v-if="singleLineDisplay(log)"
                         :class="{'border-dark-border bg-dark-module': darkMode, 'bg-light-background': !darkMode }"/>
                    <svg width="13" height="8" viewBox="0 0 13 8" fill="none" xmlns="http://www.w3.org/2000/svg" v-else
                         class="cursor-pointer" @click="log.active = !log.active"
                         :class="{'rotate-180': !log.active}"
                    >
                        <path d="M1.87939 6.04257L6.75883 1.22554L11.6383 6.04257" stroke="#0081FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <div class="flex items-center gap-3">
                        <p class="" v-html="getTitle(log)"></p>
                    </div>
                </div>
                <div class="text-xs" :class="{'text-slate-300': darkMode, 'text-slate-500': !darkMode}">{{ log.display_date }}</div>
            </div>
            <div class="text-xs flex " v-if="!singleLineDisplay(log)" v-show="log.active" :class="{'text-slate-300': darkMode, 'text-slate-500': !darkMode}">
                <div class="border-r ml-[6px] mr-[12px] min-h-[4rem]" :class="{'border-dark-border': darkMode}"></div>
                <div class="border rounded-lg px-4 py-2 flex-grow" :class="{'border-dark-border': darkMode}">
                    <div v-if="log.batch_uuid"
                         class="text-xs leading-6"
                    >
                        <div>
                            <p>Batched logs: {{ log.batch_count }}</p>
                            <div v-if="!batchDetails[log.batch_uuid]"
                                 class="font-semibold"
                                 :class="[loadingDetails ===  log.batch_uuid ? 'pointer-events-none text-slate-500' : 'text-primary-500 cursor-pointer hover:text-primary-300']"
                                 @click.prevent="loadBatchDetail(log)"
                            >
                                Load details
                            </div>
                        </div>
                        <div v-if="loadingDetails === log.batch_uuid">
                            <LoadingSpinner :dark-mode="darkMode" />
                        </div>
                        <div v-else-if="batchDetails[log.batch_uuid]"
                             class="ml-2"
                        >
                            <div v-for="batchedLog in batchDetails[log.batch_uuid]">
                                <div>
                                    <span class="font-semibold">{{ batchedLog.event }}:</span> {{ batchedLog.description }}
                                </div>
                                <div class="ml-2">
                                    <div v-if="batchedLog.event === 'created'">
                                        <div v-for="(value, key) in batchedLog.changes.attributes ?? []">
                                            {{ key }} created with value {{ value }}
                                        </div>
                                    </div>
                                    <div v-if="batchedLog.event === 'deleted'">
                                        <div v-for="(value, key) in batchedLog.changes.old ?? []">
                                            {{ key }} with value {{ value }} deleted
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div v-for="(value, key) in batchedLog.changes.old ?? []">
                                            {{ key }} from {{ value }} to {{ batchedLog.changes.attributes?.[key] ?? '-' }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p v-for="logMessage in processLog(log)" class="my-1" v-else>
                        {{ logMessage }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {useFutureCampaignStore} from "../Campaigns/Wizard/stores/future-campaigns.js";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import {dateFromTimestamp} from "../../../modules/helpers.js";
import ApiService from "../../components/Shared/services/api.js";

export default {
    name: "CampaignActivityLog",
    components: {LoadingSpinner},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        campaignId: {
            type: Number,
            require: true
        }
    },
    data() {
        return {
            campaignStore: useFutureCampaignStore(),
            api: ApiService.make(),
            loading: false,
            campaignReference: '',
            logs: [],
            loadingDetails: false,
            batchDetails: {},
            campaignStatusMapping: {
                0: 'Paused Permanently',
                1: 'Paused Temporarily',
                2: 'Active'
            },
            budgetTypeMapping: {
                0: 'No Limit',
                1: 'Max Daily Products',
                2: 'Max Daily Spend'
            }
        }
    },
    beforeMount() {
        this.campaignReference = this.campaignStore.campaigns.find(campaign => campaign.id === this.campaignId)?.reference;
        this.getActivityLogs();
    },
    methods: {
        getActivityLogs() {
            this.loading = true;

            this.campaignStore.apiService.getCampaignActivityLogs(this.campaignReference)
                .then(resp => {
                    this.logs = resp.data.data.activity_logs;
                    this.logs.forEach(log => log.active = true);
                })
                .catch(e => console.error(e))
                .finally(() => this.loading = false)
        },
        getTitle (log) {
            const userName = this.getUserName(log);
            let title = '';

            switch (log.log_name_original) {
                case 'company_campaign_status':
                    if (log.changes?.old?.status !== undefined && log.changes?.attributes?.status !== undefined) {
                        title =  `changed status from <strong>${this.transformCampaignField('status', log.changes.old.status)}</strong> to <strong>${this.transformCampaignField('status', log.changes.attributes.status)}</strong>`;
                    } else {
                        title = 'changed status';
                    }

                    break;
                case 'campaign_budget':
                    if (log.changes?.old?.type !== undefined && log.changes?.attributes?.type !== undefined) {
                        title =  `updated budget from <strong>${this.transformBudgetField('type', log.changes.old.type)}</strong> to <strong>${this.transformBudgetField('type', log.changes.attributes.type)}</strong>`;
                    } else {
                        title = `${log.event} budged`
                    }
                    break;
                case 'campaign_service_area':
                    title = `${log.event} service area`;
                    break;
                case 'campaign_state_bid_price':
                    title = 'created state bid prices';
                    break;
                case 'campaign_county_bid_price':
                    title = 'created county bid prices';
                    break;
                default:
                    title = `${log.event} campaign`
                    break;
            }

            return `${userName} ${title}`;
        },
        getUserName(log) {
            if (!log.causer) {
                return 'System';
            }

            if (log.causer.name) {
                return log.causer.name;
            }

            if (log.causer.first_name || log.causer.last_name) {
                return `${log.causer.first_name} ${log.causer.last_name}`;
            }

            return 'Unknown User';
        },
        singleLineDisplay(log) {
            switch (log.log_name_original) {
                case 'company_campaign_status':
                    return log.changes?.attributes?.status === 'Active' || log.changes?.attributes?.status === 2;
                case 'campaign_budget':
                    return log.event === 'updated' && (log.changes?.attributes?.type === 'No Limit' || log.changes?.attributes?.type === 0);
                default:
                    return false
            }
        },
        processLog(log) {
            const logMessages = [];

            switch (log.log_name_original) {
                case 'company_campaign_status':
                    if (log.properties?.data?.pause_reason) {
                        logMessages.push(`Reason: ${log.properties.data.pause_reason}`);
                    }

                    if (log.properties?.data?.reactivation_date) {
                        logMessages.push(`Reactivation: ${dateFromTimestamp(log.properties.data.reactivation_date, 'MMMM D YYYY h:mm A', 'America/Denver')} MST`);
                    }
                    break;
                case 'campaign_budget':
                    for (const [key, value] of Object.entries(log.changes?.attributes ?? {})) {
                        const properKey = this.$filters.toProperCase(key);

                        if (log.changes.old && log.changes.old[key] !== undefined) {
                            logMessages.push(`${properKey} from ${this.transformBudgetField(key, log.changes.old[key])} to ${this.transformBudgetField(key, value)}`)
                        } else {
                            logMessages.push(`${properKey} set to ${this.transformBudgetField(key, value)}`)
                        }
                    }
                    break;
                case 'campaign_service_area':
                    if (log.changes?.attributes?.zip_codes_deleted && log.changes.attributes.zip_codes_deleted.length) {
                        logMessages.push(`Removed Zip Codes: ${log.changes.attributes.zip_codes_deleted.join(', ')}`)
                    }

                    if (log.changes?.attributes?.zip_codes_added && log.changes.attributes.zip_codes_added.length) {
                        logMessages.push(`Added Zip Codes: ${log.changes.attributes.zip_codes_added.join(', ')}`)
                    }
                    break;
                default:
                    for (const [key, value] of Object.entries(log.changes?.attributes ?? {})) {
                        const properKey = this.$filters.toProperCase(key);

                        if (log.changes.old && log.changes.old[key] !== undefined) {
                            logMessages.push(`${properKey} from ${this.transformCampaignField(key, log.changes.old[key])} to ${this.transformCampaignField(key, value)}`)
                        } else {
                            logMessages.push(`${properKey} set to ${this.transformCampaignField(key, value)}`)
                        }
                    }
                    break;
            }

            return logMessages;
        },
        async loadBatchDetail(log) {
            this.loadingDetails = log.batch_uuid;
            const resp = await this.api.getBatchedActivityLogDetails(log.batch_uuid).catch(e => console.log(e));
            if (resp.data.data.activity_logs) {
                this.batchDetails[log.batch_uuid] = resp.data.data.activity_logs;
            }
            else {
                console.error(resp.response?.message ?? "An error occurred");
            }

            this.loadingDetails = null;
        },
        transformCampaignField(key, value) {
            if (key === 'status') {
                return this.campaignStatusMapping[value] ?? value;
            }

            return value;
        },
        transformBudgetField(key, value) {
            if (key === 'type') {
                return this.budgetTypeMapping[value] ?? value;
            }

            return value;
        }
    }
}
</script>
