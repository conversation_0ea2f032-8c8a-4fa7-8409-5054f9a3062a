<template>
    <div class="main-layout font-body">
        <div class="w-full">
            <div v-if="!loading" class="w-full flex-auto relative"
                 :class="{'bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                <div :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div :class="[navLayout === 'tabs' ? 'block' : 'hidden']">
                        <div class="flex flex-col gap-4 px-5 lg:px-10 pt-5 pb-2"
                             :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                            <div class="flex items-center justify-between flex-wrap">
                                <h3 class="text-xl font-semibold leading-none mr-5">Billing Management</h3>
                                <svg @click="toggleNavStyle()" class="cursor-pointer fill-current text-slate-500 hover:text-primary-500" width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M2.08301 18C1.53301 18 1.06234 17.8043 0.671008 17.413C0.279675 17.0217 0.0836745 16.5507 0.0830078 16V2C0.0830078 1.45 0.279008 0.979333 0.671008 0.588C1.06301 0.196667 1.53367 0.000666667 2.08301 0H16.083C16.633 0 17.104 0.196 17.496 0.588C17.888 0.98 18.0837 1.45067 18.083 2V16C18.083 16.55 17.8873 17.021 17.496 17.413C17.1047 17.805 16.6337 18.0007 16.083 18H2.08301ZM9.08301 16H16.083V2H9.08301V16Z" />
                                </svg>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between">
                                <tab
                                    :dark-mode="darkMode"
                                    :tabs="tabTitles"
                                    @selected="selectTab"
                                    tab-style="fit"
                                    background-color="light"
                                    :tab-type="'Normal'"
                                    wrapper-classes="px-5 lg:px-10"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="mx-5 lg:mx-10 pb-10 flex gap-4 pt-6">
                        <div class="w-64" :class="[navLayout === 'tabs' ? 'hidden' : 'block']">
                            <div class="w-64 sticky top-[5.5rem]">
                                <div class="flex justify-between items-center p-5 border-b rounded-t-lg border" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                                    <h3 class="text-base font-bold leading-none">Billing Management</h3>

                                    <svg @click="toggleNavStyle()" class="w-6 cursor-pointer fill-current text-slate-500 hover:text-primary-500" viewBox="0 0 16 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M0 2C0 1.46957 0.210714 0.960859 0.585786 0.585786C0.960859 0.210714 1.46957 0 2 0H14C14.5304 0 15.0391 0.210714 15.4142 0.585786C15.7893 0.960859 16 1.46957 16 2V4C16 4.53043 15.7893 5.03914 15.4142 5.41421C15.0391 5.78929 14.5304 6 14 6H2C1.46957 6 0.960859 5.78929 0.585786 5.41421C0.210714 5.03914 0 4.53043 0 4V2ZM6 5H10V1H6V5ZM15 4V2C15 1.73478 14.8946 1.48043 14.7071 1.29289C14.5196 1.10536 14.2652 1 14 1H11V5H14C14.2652 5 14.5196 4.89464 14.7071 4.70711C14.8946 4.51957 15 4.26522 15 4Z" />
                                    </svg>

                                </div>
                                <div class="w-64 flex-shrink-0 border-x border-b rounded-b-lg z-40 h-[calc(70vh-8rem)] overflow-y-auto" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                                    <ol>
                                        <li v-for="tab in tabTitles" :key="tab"
                                            @click="selectTab(tab.name)"
                                            class="px-5 py-2 cursor-pointer relative text-sm"
                                            :class="[
                                (darkMode ? (selectedTab === tab.name ? 'text-primary-500 font-semibold bg-dark-background' : 'hover:bg-dark-border hover:text-white font-medium text-slate-200')
                                 : (selectedTab === tab.name ? 'text-primary-500 font-semibold bg-primary-50' : 'hover:bg-slate-100 hover:text-slate-900 font-medium text-slate-700'))]">
                                            {{tab.name}}
                                            <span v-if="tab.redCount" :class="[darkMode ? 'text-rose-400 bg-dark-background' : 'text-rose-700 bg-rose-100', 'group leading-4 hidden ml-1 px-2 rounded-full text-[11px] font-bold md:inline-block relative']">{{tab.redCount}}</span>
                                            <span v-if="tab.greenCount" :class="[darkMode ? 'text-emerald-400 bg-dark-background' : 'text-emerald-700 bg-emerald-100', 'group leading-4 hidden ml-1 px-2 rounded-full text-[11px] font-bold md:inline-block relative']">{{tab.greenCount}}</span>
                                            <span v-if="tab.yellowCount" :class="[darkMode ? 'text-amber-400 bg-dark-background' : 'text-amber-700 bg-amber-100', 'group leading-4 hidden ml-1 px-2 rounded-full text-[11px] font-bold md:inline-block relative']">{{tab.yellowCount}}</span>
                                            <span v-if="selectedTab === tab.name" class="absolute left-0 inset-y-0 h-full w-1 bg-primary-500"></span>
                                        </li>
                                    </ol>
                                </div>
                            </div>

                        </div>

                        <div class="flex-grow">
                            <component :is="currentTabComponent" :dark-mode="darkMode"/>
                        </div>
                    </div>
                </div>
            </div>
            <loading-spinner v-else/>
        </div>
        <view-create-invoice-modal
            @invoice-created-updated="handleInvoiceUpdatedOrCreated"
            @close="showInvoiceModal = false"
            v-if="showInvoiceModal"
            :dark-mode="darkMode"
        />
        <display-toast-notifications />
        <AlertsContainer
            v-if="alertActive"
            :alert-type="alertType"
            :text="alertText"
            :dark-mode="darkMode"
        />
    </div>
</template>
<script>
import Tab from "../Shared/components/Tab.vue";
import {markRaw} from "vue";
import OverviewTab from "./Tabs/OverviewTab.vue";
import InvoicesTab from "./Tabs/InvoicesTab.vue";
import InvoiceActionRequestTab from "./Tabs/InvoceActionRequest/InvoiceActionRequestTab.vue";
import ReceivableInvoicesReport from "./Tabs/Reports/ReceivableInvoicesReport.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import {PERMISSIONS, ROLES, useRolesPermissions} from "../../../stores/roles-permissions.store";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin";
import ViewCreateInvoiceModal from "../Billing/ViewCreateInvoiceModal.vue";
import useQueryParams from "../../../composables/useQueryParams";
import BillingProfilesTab from "./Tabs/BillingProfilesTab.vue";
import InvoiceTransactionsTab from "./Tabs/InvoiceTransactionsTab.vue";
import InvoiceRefundsTab from "./Tabs/InvoiceRefundsTab.vue";
import InvoiceCollectionsTab from "./Tabs/InvoiceCollectionsTab.vue";
import InvoiceChargebacksTab from "./Tabs/InvoiceChargebacksTab.vue";
import RevenueReport from "./Tabs/Reports/RevenueReport.vue";
import AgedReport from "./Tabs/Reports/AgedReport.vue";
import InvoicesBalanceReport from "./Tabs/Reports/InvoicesBalanceReport.vue";
import CreditsMovementReport from "./Tabs/Reports/CreditsMovementReport.vue";
import CreditsOutstandingReport from "./Tabs/Reports/CreditsOutstandingReport.vue";
import InvoiceWriteOffsTab from "./Tabs/InvoiceWriteOffsTab.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import DisplayToastNotifications from "../Billing/components/ToastNotification/DisplayToastNotifications.vue";
import CompaniesOverviewReport from "./Tabs/Reports/CompaniesOverviewReport.vue";
import OverdueInvoicesReport from "./Tabs/Reports/OverdueInvoicesReport.vue";

const DEFAULT_SELECTED_TAB = 'Invoices'

export default {
    name: "BillingManagement",
    components: {
        DisplayToastNotifications,
        LoadingSpinner,
        ViewCreateInvoiceModal,
        AlertsContainer,
        CustomButton,
        Tab,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    mixins: [AlertsMixin],
    created() {
        this.loading = true
        const {tab = DEFAULT_SELECTED_TAB} = this.queryParamsHelper.getCurrentParams()
        this.setSelectedTab(tab)
    },
    data() {
        return {
            loading: false,
            permissionStore: useRolesPermissions(),
            tabs: [
                {
                    name: 'Overview',
                    component: markRaw(OverviewTab),
                    current: false,
                    permission: PERMISSIONS.BILLING_OVERVIEW_VIEW,
                },
                {
                    name: DEFAULT_SELECTED_TAB,
                    current: false,
                    component: markRaw(InvoicesTab),
                    permission: PERMISSIONS.BILLING_INVOICES_VIEW,
                },
                {
                    name: "Action Requests",
                    component: markRaw(InvoiceActionRequestTab),
                    current: false,
                    permission: PERMISSIONS.BILLING_ACTION_REQUESTS_VIEW,
                },
                {
                    name: "Billing Profiles",
                    component: markRaw(BillingProfilesTab),
                    current: false,
                    permission: PERMISSIONS.BILLING_BILLING_PROFILES_VIEW,
                },
                {
                    name: "Accounts Receivable Report",
                    component: markRaw(ReceivableInvoicesReport),
                    current: false,
                    permission: PERMISSIONS.BILLING_REPORTS_RECEIVABLE_VIEW,
                },
                {
                    name: "Revenue Report",
                    component: markRaw(RevenueReport),
                    current: false,
                    permission: PERMISSIONS.BILLING_REPORTS_REVENUE_VIEW,
                },
                {
                    name: "Aged Report",
                    component: markRaw(AgedReport),
                    current: false,
                    permission: PERMISSIONS.BILLING_REPORTS_AGED_VIEW,
                },
                {
                    name: "Balance Report",
                    component: markRaw(InvoicesBalanceReport),
                    current: false,
                    permission: PERMISSIONS.BILLING_REPORTS_BALANCE_VIEW,
                },
                {
                    name: "Overdue Report",
                    component: markRaw(OverdueInvoicesReport),
                    current: false,
                    permission: PERMISSIONS.BILLING_REPORTS_OVERDUE_INVOICES_VIEW,
                },
                {
                    name: "Transactions",
                    component: markRaw(InvoiceTransactionsTab),
                    current: false,
                    permission: PERMISSIONS.BILLING_INVOICE_TRANSACTIONS_VIEW,
                },
                {
                    name: "Refunds",
                    component: markRaw(InvoiceRefundsTab),
                    current: false,
                    permission: PERMISSIONS.BILLING_REPORTS_REFUNDS_VIEW,
                },
                {
                    name: "Collections",
                    component: markRaw(InvoiceCollectionsTab),
                    current: false,
                    permission: PERMISSIONS.BILLING_REPORTS_COLLECTIONS_VIEW,
                },
                {
                    name: "Write Offs",
                    component: markRaw(InvoiceWriteOffsTab),
                    current: false,
                    permission: PERMISSIONS.BILLING_REPORTS_WRITE_OFFS_VIEW,
                },
                {
                    name: "Chargebacks",
                    component: markRaw(InvoiceChargebacksTab),
                    current: false,
                    permission: PERMISSIONS.BILLING_REPORTS_CHARGEBACKS_VIEW,
                },
                {
                    name: "Credit Movement Report",
                    component: markRaw(CreditsMovementReport),
                    current: false,
                    permission: PERMISSIONS.BILLING_REPORTS_CREDIT_MOVEMENTS_VIEW,
                },
                {
                    name: "Credits Outstanding Report",
                    component: markRaw(CreditsOutstandingReport),
                    current: false,
                    permission: PERMISSIONS.BILLING_REPORTS_CREDIT_OUTSTANDING_VIEW,
                },
            ],
            selectedTab: null,
            showInvoiceModal: false,
            queryParamsHelper: useQueryParams(),
            navLayout: localStorage.getItem("navLayout") || "tabs"
        }
    },
    computed: {
        tabTitles() {
            return this.tabs.filter(tab => {
                return !tab?.permission || this.permissionStore.hasPermission(tab?.permission);
            });
        },
        currentTabComponent() {
            return this.tabTitles.find(e => e.current)?.component
        },
    },
    methods: {
        changeToSelectedTab() {
            this.tabs.forEach(e => {
                e.current = e.name === this.selectedTab
            })
        },
        setSelectedTab(tab) {
            if (this.tabs.length === 0) return
            this.selectedTab = tab

            if (!this.tabs.find(e => e.name === this.selectedTab)) {
                this.selectedTab = this.tabs[0].name
            }

            this.changeToSelectedTab()
        },
        selectTab(tab) {
            this.queryParamsHelper.setQueryParamsOnCurrentUrl({tab})
            this.setSelectedTab(tab)
        },
        handleInvoiceUpdatedOrCreated(invoiceUuid, message) {
            this.showAlert('success', message + invoiceUuid);
            this.showInvoiceModal = false;
        },
        toggleNavStyle() {
            this.navLayout = this.navLayout === "tabs" ? "side" : "tabs";
        }
    },
    watch: {
        'permissionStore.initialized'(val){
            if (val) {
                this.loading = false
            }
        },
        navLayout(newVal) {
            localStorage.setItem("navLayout", newVal);
        }
    },

}
</script>
