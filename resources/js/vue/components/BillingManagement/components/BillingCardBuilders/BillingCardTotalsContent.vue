<template>
    <div class="flex flex-col">
        <div v-if="data.items.length > 0" v-for="item in data.items" class="grid grid-cols-3 border-b px-5 py-3 text-xs uppercase font-bold leading-tight" :class="[darkMode ? 'border-dark-border' : '']">
            <div class="text-slate-500">
                {{item.name}}
            </div>
            <div class="flex justify-center">
                {{ item?.other }}
            </div>
            <div class="flex justify-end">
                {{ prefix ? '$' + item.value : item.value}}
            </div>
        </div>
        <div class="flex justify-between  px-5 py-3" :class="[darkMode ? 'bg-cyan-900' : 'bg-cyan-100']">
            <div class="text-xs uppercase font-bold leading-tight">
                {{totalTitle}}
            </div>
            <div class="text-xs uppercase font-bold leading-tight">
                {{ prefix ? '$' + data.total : data.total}}
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: "BillingCardTotalsContent",
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        data: {
            type: Object,
            default: {}
        },
        totalTitle: {
            type: String,
            default: "Total:"
        },
        prefix: {
            type: Boolean,
            default: true
        }
    },
}
</script>
