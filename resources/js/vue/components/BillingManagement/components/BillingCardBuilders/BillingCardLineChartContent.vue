<template>
    <div class="flex p-6">
        <div v-if="summaryData" class="flex flex-col mr-4 mb-4">
            <div class="text-primary-500 uppercase text-sm font-bold mb-2">
                {{summaryData.title}}
            </div>
            <div class="grid gap-2 divide-y flex-shrink-0" :class="[darkMode ? 'divide-dark-border' : 'divide-light-border']">
                <div v-for="item in summaryData.data" class="flex flex-col py-4">
                    <div class="font-bold flex-shrink-0 whitespace-nowrap">
                        {{Intl.NumberFormat('en-US', {style: 'currency', currency: 'USD'}).format(item.value)}}
                    </div>
                    <div class="font-semibold text-slate-500 text-xs flex-shrink-0 whitespace-nowrap" v-if="item?.additional">
                        {{ item.additional }}
                    </div>
                </div>
            </div>
        </div>
        <line-chart
            class="flex-1"
            chart-options="noGrid"
            :chart-data="chartData"
            :height="200"
        />
    </div>
</template>
<script>
import LineChart from "../../../Shared/components/LineChart.vue";

export default {
    name: "BillingCardLineChartContent",
    components: {LineChart},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        chartData: {
            type: Object,
            required: true,
        },
        summaryData: {
            type: Array,
            default: null
        },
    },
}
</script>
