<template>
    <div class="flex flex-col py-5">
        <div class="pb-2 mb-3">
            <div class="uppercase font-bold text-sm" :class="[darkMode ? 'text-slate-300' : 'text-slate-700']">
                Your overview
            </div>
        </div>
        <div class="grid sm:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            <div class="flex flex-col gap-4">
                <payments-billing-card :dark-mode="darkMode"/>
                <failed-payments-billing-card :dark-mode="darkMode"/>
                <bundles-sold-billing-card :dark-mode="darkMode"/>
            </div>
            <div class="flex flex-col gap-4">
                <invoice-statuses-billing-card :dark-mode="darkMode"/>
                <invoices-billing-card :dark-mode="darkMode"/>
                <cancelled-voided-invoices-billing-card :dark-mode="darkMode"/>
            </div>
            <div class="flex flex-col gap-4">
                <credit-management-billing-card :dark-mode="darkMode"/>
                <billing-policies-management-card :dark-mode="darkMode"/>
                <invoice-templates-management-card :dark-mode="darkMode"/>
            </div>
        </div>
    </div>
</template>

<script>
import PaymentsBillingCard from "./BillingOverviewCards/PaymentsBillingCard.vue";
import InvoicesBillingCard from "./BillingOverviewCards/InvoicesBillingCard.vue";
import FailedPaymentsBillingCard from "./BillingOverviewCards/FailedPaymentsBillingCard.vue";
import ChargebacksAndDisputesBillingCard from "./BillingOverviewCards/ChargebacksAndDisputesBillingCard.vue";
import GrossVolumeBillingCard from "./BillingOverviewCards/GrossVolumeBillingCard.vue";
import BundlesSoldBillingCard from "./BillingOverviewCards/BundlesSoldBillingCard.vue";
import CreditManagementBillingCard from "./BillingOverviewCards/CreditManagementBillingCard.vue";
import BillingPoliciesManagementCard from "./BillingOverviewCards/BillingPoliciesManagementCard.vue";
import InvoiceTemplatesManagementCard from "./BillingOverviewCards/InvoiceTemplatesManagementCard/InvoiceTemplatesManagementCard.vue";
import CancelledVoidedInvoicesBillingCard from "./BillingOverviewCards/CancelledVoidedInvoicesBillingCard.vue";
import InvoiceStatusesBillingCard from "./BillingOverviewCards/InvoiceStatusesBillingCard.vue";

export default {
    name: "BillingOverview",
    components: {
        InvoiceStatusesBillingCard,
        CancelledVoidedInvoicesBillingCard,
        InvoiceTemplatesManagementCard,
        BillingPoliciesManagementCard,
        CreditManagementBillingCard,
        BundlesSoldBillingCard,
        GrossVolumeBillingCard,
        ChargebacksAndDisputesBillingCard,
        FailedPaymentsBillingCard,
        InvoicesBillingCard,
        PaymentsBillingCard
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
}
</script>
