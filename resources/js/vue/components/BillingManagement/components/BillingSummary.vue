<template>
    <div class="flex flex-col">
        <div class="uppercase font-bold text-sm py-5" :class="[darkMode ? 'text-slate-300' : 'text-slate-700']">
            Billing Summary
        </div>
        <div class="grid xl:grid-cols-3 gap-4">
            <gross-volume-summary-card class="xl:col-span-2" :dark-mode="darkMode"/>
            <div class="rounded-lg overflow-hidden border" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                <total-billed-invoices-summary-card
                    class="pb-5 border-b" :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                    :dark-mode="darkMode"/>
                <pending-invoices-summary-card class="pb-5 xl:pb-0" :dark-mode="darkMode"/>
            </div>
        </div>
    </div>
</template>
<script>
import GrossVolumeSummaryCard from "./BillingSummaryCards/GrossVolumeSummaryCard.vue";
import PendingInvoicesSummaryCard from "./BillingSummaryCards/PendingInvoicesSummaryCard.vue";
import TotalBilledInvoicesSummaryCard from "./BillingSummaryCards/TotalBilledInvoicesSummaryCard.vue";
import BillingCardWrapper from "./BillingCardBuilders/BillingCardWrapper.vue";

export default {
    name: "BillingSummary",
    components: {BillingCardWrapper, TotalBilledInvoicesSummaryCard, PendingInvoicesSummaryCard, GrossVolumeSummaryCard},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
}
</script>
