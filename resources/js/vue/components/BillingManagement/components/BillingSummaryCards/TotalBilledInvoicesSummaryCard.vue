<template>
    <div>
        <billing-card-wrapper
            heading="Total Billed Invoices"
            :dark-mode="darkMode"
            :loading="loading"
            variant="naked"
        >
            <template v-slot:headerAction>
                <a class="text-primary-500 text-xs font-semibold cursor-pointer" @click="handleViewMoreClicked">View</a>
            </template>
            <div class="flex flex-col px-5">
                <div class="font-bold text-lg">
                    {{balance}}
                </div>
                <div class="font-semibold text-xs text-slate-500">
                    estimated future payouts
                </div>
            </div>
        </billing-card-wrapper>
    </div>
</template>
<script>
import BillingCardWrapper from "../BillingCardBuilders/BillingCardWrapper.vue";
import {useBillingManagementStore} from "../../../../../stores/billing/billing-management.store";
import useInvoiceHelper from "../../../../../composables/useInvoiceHelper";
const invoiceHelper = useInvoiceHelper()

export default {
    name: "TotalBilledInvoicesSummaryCard",
    components: {BillingCardWrapper},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false,
            balance: 0,
            billingStore: useBillingManagementStore(),
            invoiceHelper,
        }
    },
    created() {
        this.getBalance();
        this.billingStore.addPeriodUpdateEventListener('balance-summary', this.getBalance)

    },
    methods: {
        async getBalance() {
            this.loading = true;
            const response = await this.billingStore.api.getTotalInvoiceValueByStatus({'status': this.invoiceHelper.STATUSES.PAID})
            this.balance = response.data.data.data.total
            this.loading = false;
        },
        handleViewMoreClicked() {

        }
    }
}
</script>
