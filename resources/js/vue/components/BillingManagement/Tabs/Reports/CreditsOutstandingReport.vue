<template>
    <div>
        <simple-table
            v-model="filters"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            :loading="loading"
            :current-per-page="filters.per_page"
            :pagination-data="paginationData"
            title="Credits Outstanding Report"
            @search="getReport"
            @reset="handleReset"
            @page-change="handlePageChange"
            :table-filters="tableFilters"
            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
        >
            <template v-slot:title-actions>
                <CustomButton v-if="canExport" :disabled="exporting" :dark-mode="darkMode" @click="handleExportReport('csv')">
                    <svg v-if="exporting" aria-hidden="true" class="w-4 h-4 text-gray-200 animate-spin fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/></svg>
                    <p v-else>Export</p>
                </CustomButton>
            </template>
            <template v-slot:visible-filters>
                <date-picker
                    v-model="filters.date"
                    :range="false"
                    :enable-time-picker="false"
                    :dark="darkMode"
                    auto-apply
                    placeholder="mm-dd-yy"
                    format="PP"
                    timezone="America/Denver"
                >
                    <template #input-icon>
                        <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                fill="#0081FF"/>
                            <path
                                d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                fill="#0081FF"/>
                        </svg>
                    </template>
                </date-picker>
                <company-search-autocomplete v-model="filters.company_id" :dark-mode="darkMode" />
            </template>
            <template v-slot:row.col.company_name="{item}">
                <entity-hyperlink :prefix="item?.company?.name" :entity-id="item?.company?.id" type="company"/>
            </template>
            <template v-slot:row.col.company_id="{item}">
                <entity-hyperlink :prefix="item?.company?.id" :entity-id="item?.company?.id" type="company"/>
            </template>
            <template v-slot:row.col.billing_profile_ids="{value}">
                <div v-if="value?.length"  class="flex flex-col">
                    <entity-hyperlink v-for="id in value" :prefix="id" :entity-id="id" type="billing_profile"/>
                </div>
                <p v-else>All</p>
            </template>
            <template v-slot:row.col.expires_at="{value, item}">
                <div class="flex gap-1 items-center">
                    <p>{{value}}</p>
                    <badge v-if="item.is_expired" color="red">Expired</badge>
                </div>
            </template>
            <template v-slot:row.col.invoice_id="{value}">
                <entity-hyperlink
                    v-if="value"
                    :entity-id="value"
                    type="invoice"
                    :prefix="value"
                >
                </entity-hyperlink>
            </template>
            <template #row.col.actions="{item, value}">
                <simple-icon
                    :icon="simpleIcon.icons.EYE"
                    clickable
                    @click="toggleCreditLifeCycleModalVisibility(item)"
                >
                </simple-icon>
            </template>
        </simple-table>
    </div>
    <CreditLifeCycleModal
        v-if="showCreditLifeCycleModal"
        :credit-id="selectedCreditId"
        @close="toggleCreditLifeCycleModalVisibility()"
    />
</template>
<script>
import SimpleTable from "../../../Shared/components/SimpleTable/SimpleTable.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import DatePicker from "@vuepic/vue-datepicker";
import Autocomplete from "../../../Shared/components/Autocomplete.vue";
import SharedApiService from "../../../Shared/services/api";
import SimpleAlert from "../../../Shared/components/SimpleAlert.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import InvoiceStatusBadge from "../../components/InvoiceStatusBadge.vue";
import ApiService from "../../services/invoices-report-api.js";
import EntityHyperlink from "../../components/EntityHyperlink.vue";
import CompanySearchAutocomplete from "../../../Shared/components/Company/CompanySearchAutocomplete.vue";
import {
    SimpleTableHiddenFilterTypesEnum
} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import {
    SimpleTableFilterTypesEnum
} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {useCreditManagementStore} from "../../../../../stores/credit/credit-management.store.js";
import CustomInput from "../../../Shared/components/CustomInput.vue";
import Badge from "../../../Shared/components/Badge.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import {downloadCsvString} from "../../../../../composables/exportToCsv.js";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import CreditLifeCycleModal from "../../components/Modals/CreditLifeCycleModal.vue";

export default {
    name: "CreditsOutstandingReport",
    components: {
        CreditLifeCycleModal,
        SimpleIcon,
        Badge,
        CustomInput,
        CompanySearchAutocomplete,
        EntityHyperlink,
        InvoiceStatusBadge,
        Dropdown, SimpleAlert, Autocomplete, DatePicker, CustomButton, SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    created() {
        this.handleReset()
        this.getFilterOptions()
    },
    data() {
        return {
            creditManagementStore: useCreditManagementStore(),
            sharedApi: SharedApiService.make(),
            exporting: false,
            selectedReceivableInvoice: null,
            headers: [
                {title: 'Credit Id', field: 'credit_id', sortable: true},
                {title: 'Company Id', field: 'company_id', sortable: true},
                {title: 'Company Name', field: 'company_name'},
                {title: 'Billing Profiles', field: 'billing_profile_ids'},
                {title: 'Initial Amount', field: 'initial_value', sortable: true},
                {title: 'Outstanding', field: 'outstanding', sortable: true},
                {title: 'Applied At', field: 'applied_at', sortable: true},
                {title: 'Expires At', field: 'expires_at', sortable: true},
                {title: 'Credit Type', field: 'credit_type'},
                {title: 'Actions', field: 'actions'},
            ],
            rolesPermissions: useRolesPermissions(),
            companyOptions: [],
            accountManagerOptions: [],
            successManagerOptions: [],
            invoiceStatusOptions: [],
            showCreditLifeCycleModal: false,
            selectedCreditId: null,
            tableFilters: [
                {
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    field: 'status',
                    title: 'Status',
                    options: [
                        {id: 'all', name: 'All'},
                        {id: 'expired_only', name: 'Expired Only'},
                        {id: 'exclude_expired', name: 'Exclude Expired'},
                    ]
                },
            ],
            filters: {
                per_page: 25,
                date: null
            },
            api: ApiService.make(),
            data: [],
            loading: false,
            paginationData: {
                perPage: 25
            },
        }
    },

    methods: {
        toggleCreditLifeCycleModalVisibility(item){
            this.showCreditLifeCycleModal = !this.showCreditLifeCycleModal
            this.selectedCreditId = item?.credit_id
        },
        useSimpleIcon,
        async handleExportReport(){
            this.exporting = true

            const response = await this.api.getCreditsOutstandingReport({
                ...this.filters,
                all: 1
            });

            const formattedItems = response.data.data.map(item => [
                item.credit_id,
                item.company?.id,
                item.company?.name,
                item.billing_profile_ids.join(', '),
                item.initial_value,
                item.outstanding,
                item.applied_at,
                item.expires_at,
                item.credit_type,
            ])

            const headers = this.headers.filter(e => e.field !== 'actions').map(h => h.title);

            const formattedFilters = Object.entries(this.filters)
                .map(([key, value]) => `${key}=${JSON.stringify(value)}`)
                .map((item, idx) => [idx === 0 ? 'Filters' : '', item])

            const exportInfoData = [
                ['Exported date', this.$filters.dateFromTimestamp((new Date()).toISOString(), 'usWithTime')],
                ...formattedFilters,
                ['Total Items', formattedItems.length],
            ]

            downloadCsvString(
                headers,
                [
                    ...formattedItems,
                    Array.from({length: headers.length}, () => ''),
                    ...exportInfoData,
                ],
                `credits_outstanding_report_${Math.floor(Date.now() / 1000)}`
            )

            this.exporting = false
        },
        handlePageChange({ newPage }){
            if (this.page === newPage) return;
            this.filters.page = newPage;
            this.getReport();
        },
        async getFilterOptions() {
            const response = await this.creditManagementStore.api.getCreditTypes()

            this.tableFilters.push({
                type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                location: SimpleTableFilterTypesEnum.HIDDEN,
                field: 'credit_type',
                title: 'Credit type',
                options: response.data.data.map(e => ({id: e.slug, name: e.name}))
            })
        },
        async getReport() {
            this.loading = true

            const res = await this.api.getCreditsOutstandingReport(this.filters);

            this.data = res.data.data
            this.paginationData = {links: res.data.links, ...res.data.meta}

            this.loading = false
        },
        async searchCompanies(query) {
            this.sharedApi.searchCompanyNamesAndId(query).then(res => {
                if (res.data.data.status === true) {
                    this.companyOptions = res.data.data.companies;
                }
            })
        },
        handleReset() {
            this.filters = {
                date: new Date(),
                perPage: 25,
                per_page: 25,
                status: 'exclude_expired'
            }

            this.getReport()
        },
        getCreditMovementTypeColor(value) {
            return {
                added_to_company: 'green',
                applied_to_invoice: 'blue',
            }[value] ?? 'gray'
        },
    },
    computed: {
        canExport() {
            return this.rolesPermissions.hasPermission(PERMISSIONS.BILLING_REPORTS_EXPORT)
        },
        simpleIcon() {
            return useSimpleIcon()
        },
        creditMovementTypes() {
            return {
                added_to_company: 'Added To Company',
                applied_to_invoice: 'Applied To Invoice',
            }
        }
    }
}
</script>
