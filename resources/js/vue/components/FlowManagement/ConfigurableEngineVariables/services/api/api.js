import axios from 'axios';
import {BaseApiService} from "./base";

export class ApiService extends BaseApiService {
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    getConfigurableEngineVariables(params) {
        return this.axios().get('/', {
            params
        })
    }

    deleteConfigurableEngineVariable(id, environment) {
        return this.axios().delete(`${id}`, {
            params: {
                environment,
            }
        });
    }

    saveConfigurableEngineVariable(payload) {
        return this.axios().post('/', payload)
    }
}
