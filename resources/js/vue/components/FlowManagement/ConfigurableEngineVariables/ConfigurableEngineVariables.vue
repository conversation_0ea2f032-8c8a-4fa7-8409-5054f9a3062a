<template>
    <div>
        <simple-table
            v-model="filter"
            :data="data"
            :dark-mode="darkMode"
            :headers="headers"
            title="Configurable Engine"
            no-pagination
            :loading="loading"
            @reset="resetFilter"
            @search="getConfigurableEngineVariables"
            row-classes="gap-5 grid items-center py-4 rounded px-5"
        >
            <template #visible-filters>
                <labeled-value
                    label="Environment"
                    :tooltip="tooltip"
                    :dark-mode="darkMode"
                    v-if="environmentDropdownOptions.length > 1"
                >
                    <Dropdown
                        :dark-mode="darkMode"
                        :options="environmentDropdownOptions"
                        v-model="selectedEnvironment"
                    />
                </labeled-value>
                <labeled-value label="State">
                    <Dropdown
                        v-model="filter.state"
                        :dark-mode="darkMode"
                        :options="stateOptions"
                        v-on:update:modelValue="refreshCounties"
                    />
                </labeled-value>
                <labeled-value label="County">
                    <Dropdown
                        v-model="filter.county"
                        :dark-mode="darkMode"
                        :options="countyOptions"
                    />
                </labeled-value>
                <labeled-value label="Engine">
                    <Dropdown
                        v-model="filter.engine"
                        :dark-mode="darkMode"
                        :options="engineOptions"
                        @update:modelValue="handleEngineSelected"
                    />
                </labeled-value>
                <labeled-value label="Variable Key">
                    <Dropdown
                        v-model="filter.variable_key"
                        :dark-mode="darkMode"
                        :options="engineVariableOptions"
                    />
                </labeled-value>
            </template>
            <template #title-actions>
                <custom-button @click="toggleSaveConfigurableEngine()">
                    Add new variable
                </custom-button>
            </template>
            <template #row.col.actions="{item}">
                <div class="flex items-center gap-1">
                    <simple-icon
                        :icon="simpleIcon.icons.BIN"
                        @click="handleDelete(item)"
                        clickable
                        :color="simpleIcon.colors.RED"
                    />
                </div>
            </template>
            <template #row.col.created_at="{value}">
                <p class="text-sm">
                    {{ $filters.dateFromTimestamp(value, 'usWithTime') }}
                </p>
            </template>
            <template #row.col.updated_at="{value}">
                <p class="text-sm">
                    {{ $filters.dateFromTimestamp(value, 'usWithTime') }}
                </p>
            </template>
        </simple-table>
        <SaveConfigurableEngineVariableModal
            v-if="showSaveConfigurableEngineVariableModal"
            v-model="selectedConfigurableEngineVariable"
            @close="handleSaveConfigurableEngineVariableModalClose"
            :engine-options="engineOptions"
            :state-options="stateOptions"
            :environment="selectedEnvironment"
            :dark-mode="darkMode"
        >

        </SaveConfigurableEngineVariableModal>
        <ConfirmModal
            v-show="confirmModal?.show"
            :title="confirmModal?.title"
            :dark-mode="darkMode"
            :text="confirmModal?.text"
            @choice="confirmModal?.callback"
        />
    </div>
</template>

<script>
import {defineComponent} from 'vue'
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import {ApiFactory} from "./services/api/factory.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import {useToastNotificationStore} from "../../../../stores/billing/tost-notification.store.js";
import TextField from "../../IndustryManagement/WebsiteManagement/ApiKeys/components/TextField.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import MultiSelect from "../../Shared/components/MultiSelect.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import SaveConfigurableEngineVariableModal from "./SaveConfigurableEngineVariableModal.vue";
import ConfirmModal from "../../Shared/components/ConfirmationModal.vue";
import SharedApiService from "../../Shared/services/api.js";
import Tooltip from "../../Shared/components/Tooltip.vue";

export default defineComponent({
    name: "ConfigurableEngineVariables",
    components: {
        Tooltip,
        ConfirmModal,
        SaveConfigurableEngineVariableModal,
        Dropdown, MultiSelect, Autocomplete, CustomButton, LabeledValue, TextField, SimpleIcon, SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        apiDriver: {
            type: String,
            default: 'api'
        }
    },

    data() {
        return {
            confirmModal: {},
            selectedConfigurableEngineVariable: {},
            filter: {},
            loading: false,
            api: ApiFactory.makeApiService(this.apiDriver),
            showSaveConfigurableEngineVariableModal: false,
            headers: [
                {title: "Engine", field: "engine_name"},
                {title: "State", field: "state"},
                {title: "County", field: "county"},
                {title: "Variable key", field: "key"},
                {title: "Value", field: "value"},
                {title: "Created At", field: "created_at"},
                {title: "Updated At", field: "updated_at"},
                {title: "Actions", field: "actions"},
            ],
            allData: {},
            simpleIcon: useSimpleIcon(),
            toastNotificationStore: useToastNotificationStore(),
            stateOptions: [],
            sharedApi: SharedApiService.make(),
            countyOptions: [],
            allEngineOptions: {},
            engineVariableOptions: [],
            engineEnvironmentOptions: ['production'],
            selectedEnvironment: 'production',
            tooltips: {
                production: 'These values will be used by production calculators',
                development: `These values can be tested in Flow Builder's "Preview" mode`
            }
        }
    },

    mounted() {
        this.getConfigurableEngineVariables()
        this.getStateOptions()
    },
    computed: {
        data() {
            return this.allData[this.selectedEnvironment];
        },
        engineOptions() {
            return this.allEngineOptions[this.selectedEnvironment];
        },
        environmentDropdownOptions() {
            return this.engineEnvironmentOptions.map(env => ({ name: env, id: env }));
        },
        tooltip() {
            return this.tooltips[this.selectedEnvironment];
        }
    },
    methods: {
        handleEngineSelected(engineName){
            const engine = this.engineOptions.find(e => e.id === engineName)
            if (!engine) {
                return
            }

            this.engineVariableOptions = engine.payload.location
        },
        refreshCounties(stateSlug){
            const state = this.stateOptions.find(e => e.id === stateSlug)

            if (!state) {
                return
            }

            this.countyOptions = state.counties.map(e => ({
                ...e,
                id: e.key,
            }))
        },
        async getEngineOptions(){
            const response = await this.api.getEngineOptions()
            this.engineOptions = response.data.data
        },
        async getStateOptions(){
            const response = await this.sharedApi.getStatesWithCounties()
            this.stateOptions = response.data.data.data.map(e => ({
                ...e,
                id: e.abbrev,
            }))
        },
        async getConfigurableEngineVariables() {
            this.loading = true
            const response = await this.api.getConfigurableEngineVariables(this.filter);
            for (const environment of response.data.data.environments) {
                this.allData[environment] = response.data.data[environment].data.values.map(function (item) {
                    const [state, county] = item.variable_identifier.split('|')

                    return {
                        ...item,
                        state,
                        county
                    }
                })
                this.allEngineOptions[environment] = Object.entries(response.data.data[environment].data.engines).map(([key,val]) => ({
                    id: key,
                    name: key,
                    payload: val
                }))
            }
            this.engineEnvironmentOptions = response.data.data.environments;

            this.loading = false
        },
        async handleDelete(item) {
            this.confirmModal = {
                show: true,
                title: `Confirm deletion`,
                text: `Do you want to delete ${item.key} for ${item.engine_name} ${item.variable_identifier}?`,
                callback: async (choice) => {
                    if (choice) {
                        this.loading = true
                        await this.api.deleteConfigurableEngineVariable(item.id, this.selectedEnvironment)
                        this.toastNotificationStore.notifySuccess('Successfully deleted')
                        this.getConfigurableEngineVariables()
                    }

                    this.confirmModal = {}
                }
            }
        },
        toggleSaveConfigurableEngine(item){
            this.showSaveConfigurableEngineVariableModal = !this.showSaveConfigurableEngineVariableModal
            this.selectedConfigurableEngineVariable = item ?? {}
        },
        handleSaveConfigurableEngineVariableModalClose(){
            this.showSaveConfigurableEngineVariableModal = false
            this.getConfigurableEngineVariables()
        },
        resetFilter(){
            this.filter = {
                page: 1,
                perPage: 50
            }

            this.getConfigurableEngineVariables()
        }
    }
});
</script>
