<template>
    <modal
        :dark-mode="darkMode"
        @close="handleClose"
        no-min-height
        no-close-button
        confirm-text="Save"
        :disable-confirm="saveDisabled"
        :loading-confirmation="loadingConfirmation"
        @confirm="handleSave"
        no-scroll
    >
        <template v-slot:header>
            Save Configurable Engine Variable Modal
        </template>
        <template v-slot:content>
            <simple-alert
                v-if="errorHandler.message"
                :content="errorHandler.message"
                :dark-mode="darkMode"
                variant="light-red"
            >
            </simple-alert>
            <simple-card class="p-4">
                <div class="grid gap-4 grid-cols-3">
                    <labeled-value label="Engine">
                        <Dropdown
                            v-model="localModelValue.engine_name"
                            :dark-mode="darkMode"
                            :options="engineOptions"
                            @update:modelValue="refreshEngineVariables"
                        />
                    </labeled-value>
                    <labeled-value label="Variable Key">
                        <Dropdown
                            v-model="localModelValue.key"
                            :dark-mode="darkMode"
                            :options="engineVariableOptions"
                        />
                    </labeled-value>
                    <labeled-value label="Variable Value">
                        <TextField
                            v-model="localModelValue.value"
                            :dark-mode="darkMode"
                        />
                    </labeled-value>
                    <labeled-value label="State">
                        <Dropdown
                            v-model="localModelValue.state"
                            :dark-mode="darkMode"
                            :options="stateOptions"
                            @update:modelValue="refreshCounties"
                        />
                    </labeled-value>
                    <labeled-value label="County">
                        <Dropdown
                            v-model="localModelValue.county"
                            :dark-mode="darkMode"
                            :options="countyOptions"
                        />
                    </labeled-value>
                </div>
            </simple-card>
        </template>
    </modal>
</template>
<script>
import Modal from "../../Shared/components/Modal.vue";
import TextField from "../../IndustryManagement/WebsiteManagement/ApiKeys/components/TextField.vue";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import {ApiFactory} from "./services/api/factory.js";
import SimpleCard from "../../MarketingCampaign/SimpleCard.vue";
import useErrorHandler from "../../../../composables/useErrorHandler.js";
import SimpleAlert from "../../Shared/components/SimpleAlert.vue";
import {useToastNotificationStore} from "../../../../stores/billing/tost-notification.store.js";

export default {
    name: "SaveConfigurableEngineVariableModal",
    components: {
        SimpleAlert,
        SimpleCard,
        CustomButton,
        Dropdown,
        LabeledValue,
        TextField,
        Modal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        modelValue: {
            type: Object,
            default: {}
        },
        engineOptions: {
            type: Array,
            default: []
        },
        stateOptions: {
            type: Array,
            default: []
        },
        environment: {
            type: String,
            default: 'production',
        }
    },
    data() {
        return {
            localModelValue: {
                ...this.modelValue
            },
            countyOptions: [],
            engineVariableOptions: [],
            saving: false,
            api: ApiFactory.makeApiService('api'),
            saveDisabled: false,
            loadingConfirmation: false,
            errorHandler: useErrorHandler(),
            toastNotificationStore: useToastNotificationStore()
        }
    },
    watch: {
        modelValue: {
            handler(newVal) {
                this.localModelValue = newVal
            },
            deep: true,
        }
    },
    methods: {
        async handleSave() {
            this.loadingConfirmation = true
            const type = this.localModelValue.county ? 'county' : 'state';

            const identifier = [
                this.localModelValue.state,
                this.localModelValue.county
            ].filter(e => !!e).join('|')

            try {
                const response = await this.api.saveConfigurableEngineVariable({
                    engine_name: this.localModelValue.engine_name,
                    variable_category: 'location',
                    variable_type: type,
                    variable_identifier: identifier,
                    variable_key: this.localModelValue.key,
                    variable_value: this.localModelValue.value,
                    environment: this.environment,
                });

                this.toastNotificationStore.notifySuccess('Variable saved')
            } catch (error) {
                this.loadingConfirmation = false
                this.errorHandler.handleError(error)
                return
            }

            this.handleClose()
        },
        refreshCounties(stateSlug) {
            const state = this.stateOptions.find(e => e.id === stateSlug)
            if (!state) {
                return
            }

            this.countyOptions = state.counties.map(e => ({
                ...e,
                id: e.key,
            }))
        },
        refreshEngineVariables(engineName) {
            const engine = this.engineOptions.find(e => e.id === engineName)
            if (!engine) {
                return
            }

            this.engineVariableOptions = engine.payload.location
        },
        handleClose() {
            this.$emit('close')
        }
    }
}
</script>
